cmake_minimum_required(VERSION 3.14)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

set(PARAM_VIEWER_SOURCES
    paramviewer.cpp
    paramviewer.h
)

add_library(ParamViewer STATIC ${PARAM_VIEWER_SOURCES})

target_link_libraries(ParamViewer
    PRIVATE
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
)

target_include_directories(ParamViewer
    PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
) 