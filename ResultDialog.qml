import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Dialog {
    id: resultDialog
    title: success ? qsTr("日志处理完成") : qsTr("处理出现问题")
    modal: true
    width: 450
    height: 220
    
    property bool success: false
    property string message: ""
    
    background: Rectangle {
        color: "#ffffff"
        border.color: "#dddddd"
        border.width: 1
        radius: 5
    }
    
    header: Rectangle {
        color: success ? "#4CAF50" : "#F44336"
        height: 40
        radius: 5
        
        Label {
            text: resultDialog.title
            color: "white"
            font.bold: true
            font.pixelSize: 14
            anchors {
                verticalCenter: parent.verticalCenter
                left: parent.left
                leftMargin: 15
            }
        }
    }
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 20
        
        RowLayout {
            Layout.fillWidth: true
            spacing: 15
            
            Rectangle {
                width: 40
                height: 40
                radius: 20
                color: success ? "#4CAF50" : "#F44336"
                Layout.alignment: Qt.AlignVCenter
                
                Text {
                    anchors.centerIn: parent
                    text: success ? "✓" : "✗"
                    color: "white"
                    font.pixelSize: 24
                    font.bold: true
                }
            }
            
            Label {
                text: resultDialog.message
                Layout.fillWidth: true
                wrapMode: Text.Wrap
                font.pixelSize: 14
            }
        }
        
        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: "#eeeeee"
        }
        
        Label {
            text: success ? 
                qsTr("所有日志文件已处理完成，日志列表将自动刷新") : 
                qsTr("处理过程中出现问题，请检查日志文件")
            Layout.fillWidth: true
            wrapMode: Text.Wrap
            horizontalAlignment: Text.AlignHCenter
            font.italic: true
            color: success ? "#4CAF50" : "#F44336"
        }
        
        Button {
            text: qsTr("确定")
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 100
            
            background: Rectangle {
                color: {
                    if (success) {
                        return parent.down ? "#388E3C" : (parent.hovered ? "#43A047" : "#4CAF50")
                    } else {
                        return parent.down ? "#D32F2F" : (parent.hovered ? "#E53935" : "#F44336")
                    }
                }
                radius: 4
            }
            
            contentItem: Text {
                text: parent.text
                color: "white"
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
            
            onClicked: {
                resultDialog.accept()
            }
        }
    }
    
    onAccepted: {
        if (success) {
            // 如果处理成功，通知外部刷新日志列表
            resultDialog.refreshList()
        }
        destroy()
    }
    
    // 定义一个信号，用于通知外部需要刷新列表
    signal refreshList()
} 