#include "dataidentifierconfig.h"
#include "protocoltypes.h"
#include <QFile>
#include <QDebug>
#include <QXmlStreamReader>
#include <QCoreApplication>
#include <algorithm>

// 静态实例指针定义
DataIdentifierConfig* DataIdentifierConfig::s_instance = nullptr;

DataIdentifierConfig* DataIdentifierConfig::instance()
{
    if (!s_instance) {
        s_instance = new DataIdentifierConfig();
    }
    return s_instance;
}

void DataIdentifierConfig::destroyInstance()
{
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

DataIdentifierConfig::DataIdentifierConfig(QObject *parent)
    : QObject(parent)
{
    // 初始化转换函数
    m_conversionFunctions["hex_to_decimal"] = [](const QString& hexCode) {
        bool ok;
        int decimal = hexCode.toInt(&ok, 16);
        return ok ? QString::number(decimal) : hexCode;
    };

    m_conversionFunctions["keep_hex"] = [](const QString& hexCode) {
        return hexCode.toUpper();
    };

    m_conversionFunctions["to_upper"] = [](const QString& code) {
        return code.toUpper();
    };

    m_conversionFunctions["to_lower"] = [](const QString& code) {
        return code.toLower();
    };
    QString configDir = QCoreApplication::applicationDirPath() + "/config/";

    // 定义不同协议对应的配置文件
    QMap<ProtocolType, QString> configFiles;
    configFiles[ProtocolType::DLT645_2007] = configDir + "dlt645_2007_config.xml";
    configFiles[ProtocolType::NW_UP] = configDir + "nwup_2024_config.xml";
//    configFiles[DLT645_1997] = configDir + "dlt645_1997_config.xml";
//    configFiles[NW_PLC] = configDir + "nw_plc_config.xml";

    // 加载每个协议的配置文件
    for (auto it = configFiles.begin(); it != configFiles.end(); ++it) {
        ProtocolType protocolType = it.key();
        QString filePath = it.value();

        if (QFile::exists(filePath)) {
            bool success = loadFromXml(filePath, protocolType);
            if (success) {
                qDebug() << "成功加载协议配置:" << protocolType << filePath;
            } else {
                qWarning() << "加载协议配置失败:" << protocolType << filePath << getLastError();
            }
        } else {
            qDebug() << "配置文件不存在:" << filePath;
        }
    }
}

DataIdentifierConfig::~DataIdentifierConfig()
{
}



bool DataIdentifierConfig::loadFromXml(const QString &filePath, ProtocolType protocolType)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        setError(QString("无法打开配置文件: %1").arg(filePath));
        return false;
    }

    QXmlStreamReader xmlReader(&file);
    bool result = parseXmlConfig(xmlReader, protocolType);
    file.close();

    if (xmlReader.hasError()) {
        setError(QString("XML解析错误: %1").arg(xmlReader.errorString()));
        return false;
    }

    return result;
}

DataItemConfig DataIdentifierConfig::getDataItemConfig(quint32 dataIdentifier, ProtocolType protocolType) const
{
    // 从指定协议的配置中查找
    if (m_protocolConfigs.contains(protocolType)) {
        const ConfigData &configData = m_protocolConfigs[protocolType];
        if (configData.dataItemCache.contains(dataIdentifier)) {
            return configData.dataItemCache[dataIdentifier];
        }
    }

    // 返回空配置
    return DataItemConfig();
}

QString DataIdentifierConfig::getDataItemName(quint32 dataIdentifier, ProtocolType protocolType) const
{
    DataItemConfig config = getDataItemConfig(dataIdentifier, protocolType);
    return config.name.isEmpty() ? QString("未知数据项(0x%1)").arg(dataIdentifier, 8, 16, QChar('0')).toUpper() : config.name;
}

QString DataIdentifierConfig::getDataItemDescription(quint32 dataIdentifier, ProtocolType protocolType) const
{
    DataItemConfig config = getDataItemConfig(dataIdentifier, protocolType);
    return config.description.isEmpty() ? QString("数据标识0x%1").arg(dataIdentifier, 8, 16, QChar('0')).toUpper() : config.description;
}

QString DataIdentifierConfig::getDataEncoding(quint32 dataIdentifier, ProtocolType protocolType) const
{
    DataItemConfig config = getDataItemConfig(dataIdentifier, protocolType);
    return config.encoding.isEmpty() ? "BCD" : config.encoding;
}

bool DataIdentifierConfig::isDataBlock(quint32 dataIdentifier, ProtocolType protocolType) const
{
    DataItemConfig config = getDataItemConfig(dataIdentifier, protocolType);
    return config.isBlock;
}

QList<BlockItemInfo> DataIdentifierConfig::getBlockItems(quint32 dataIdentifier, ProtocolType protocolType) const
{
    DataItemConfig config = getDataItemConfig(dataIdentifier, protocolType);

    if (config.isBlock) {
        return config.blockItems;
    }

    return QList<BlockItemInfo>();
}

QList<quint32> DataIdentifierConfig::getAllDataIdentifiers(ProtocolType protocolType) const
{
    if (m_protocolConfigs.contains(protocolType)) {
        const ConfigData &configData = m_protocolConfigs[protocolType];
        return configData.dataItemCache.keys();
    }

    return QList<quint32>();
}

QList<quint32> DataIdentifierConfig::getDataIdentifiersByCategory(const QString &categoryId, ProtocolType protocolType) const
{
    // 由于我们现在使用扁平化结构，这个方法返回所有标识符
    // 如果需要按类别过滤，可以根据数据标识的高字节进行过滤
    Q_UNUSED(categoryId)
    return getAllDataIdentifiers(protocolType);
}



bool DataIdentifierConfig::parseXmlConfig(QXmlStreamReader &xmlReader, ProtocolType protocolType)
{
    // 确保协议配置存在
    if (!m_protocolConfigs.contains(protocolType)) {
        m_protocolConfigs[protocolType] = ConfigData();
    }

    ConfigData &configData = m_protocolConfigs[protocolType];

    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isStartElement()) {
            if (xmlReader.name() == "root") {
                // 从根节点获取版本信息
                configData.version = xmlReader.attributes().value("version").toString();

                // 根据协议类型设置协议名称
                switch (protocolType) {
                case ProtocolType::DLT645_2007:
                    configData.protocol = "DL/T645-2007";
                    break;
                case ProtocolType::DLT645_1997:
                    configData.protocol = "DL/T645-1997";
                    break;
                case ProtocolType::NW_UP:
                    configData.protocol = "NW-UP";
                    break;
                case ProtocolType::NW_PLC:
                    configData.protocol = "NW-PLC";
                    break;
                default:
                    configData.protocol = "Unknown";
                    break;
                }
            }
            else if (xmlReader.name() == "description") {
                configData.description = xmlReader.readElementText();
            }
            else if (xmlReader.name() == "data_categories") {
                if (!parseDataCategories(xmlReader, protocolType)) {
                    return false;
                }
            }
        }
    }

    return !xmlReader.hasError();
}

bool DataIdentifierConfig::parseDataCategories(QXmlStreamReader &xmlReader, ProtocolType protocolType)
{
    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "data_categories") {
            break;
        }

        if (xmlReader.isStartElement()) {
            if (xmlReader.name() == "data_item") {
                // 检查是否为模板数据项（有DI范围属性）
                QXmlStreamAttributes attrs = xmlReader.attributes();
                if (attrs.hasAttribute("DI0") || attrs.hasAttribute("DI1") || attrs.hasAttribute("DI2")) {
                    // 解析模板数据项
                    if (!parseTemplateDataItem(xmlReader, protocolType)) {
                        return false;
                    }
                } else {
                    // 解析普通数据项
                    if (!parseSingleDataItem(xmlReader, protocolType)) {
                        return false;
                    }
                }
            }
            else if (xmlReader.name() == "data_block") {
                // 统一使用 parseTemplateDataBlock 处理所有数据块
                if (!parseTemplateDataBlock(xmlReader, protocolType)) {
                    return false;
                }
            }
        }
    }

    return true;
}

bool DataIdentifierConfig::parseSingleDataItem(QXmlStreamReader &xmlReader, ProtocolType protocolType)
{
    DataItemConfig item;

    // 解析数据项属性
    item.id = xmlReader.attributes().value("id").toString();
    item.name = xmlReader.attributes().value("name").toString();
    item.format = xmlReader.attributes().value("format").toString();
    item.length = xmlReader.attributes().value("length").toInt();
    item.unit = xmlReader.attributes().value("unit").toString();
    item.encoding = xmlReader.attributes().value("encoding").toString();
    item.access = xmlReader.attributes().value("access").toString();
    item.description = xmlReader.attributes().value("description").toString();
    item.isBlock = false;
    item.isComplex = (item.format.toUpper() == "COMPLEX");
    item.isVariable = (xmlReader.attributes().value("variable").toString().toLower() == "true");

    // 如果是复合数据格式，解析field字段
    if (item.isComplex) {
        if (!parseFieldItems(xmlReader, item)) {
            return false;
        }
    }

    // 将8位十六进制字符串转换为32位整数
    bool ok;
    quint32 identifier = item.id.toUInt(&ok, 16);
    if (ok) {
        // 存储到指定协议的配置中
        if (!m_protocolConfigs.contains(protocolType)) {
            m_protocolConfigs[protocolType] = ConfigData();
        }
        m_protocolConfigs[protocolType].dataItemCache[identifier] = item;

        if (item.isComplex) {
            qDebug() << "加载复合数据项:" << item.name << "ID:" << QString("0x%1").arg(identifier, 8, 16, QChar('0')).toUpper()
                     << "包含" << item.fields.size() << "个字段" << "协议:" << protocolType;
        } else {
            qDebug() << "加载数据项:" << item.name << "ID:" << QString("0x%1").arg(identifier, 8, 16, QChar('0')).toUpper()
                     << "协议:" << protocolType;
        }
    } else {
        setError(QString("无效的数据标识: %1").arg(item.id));
        return false;
    }

    return true;
}

bool DataIdentifierConfig::parseDataBlock(QXmlStreamReader &xmlReader, ProtocolType protocolType)
{
    DataItemConfig item;

    // 解析数据块属性
    item.id = xmlReader.attributes().value("id").toString();
    item.name = xmlReader.attributes().value("name").toString();
    item.format = xmlReader.attributes().value("format").toString();
    item.length = xmlReader.attributes().value("length").toInt();
    item.unit = xmlReader.attributes().value("unit").toString();
    item.encoding = xmlReader.attributes().value("encoding").toString();
    item.access = xmlReader.attributes().value("access").toString();
    item.description = xmlReader.attributes().value("description").toString();
    item.isBlock = true;

    // 解析数据块包含的项目
    if (!parseBlockItems(xmlReader, item)) {
        return false;
    }

    // 将8位十六进制字符串转换为32位整数
    bool ok;
    quint32 identifier = item.id.toUInt(&ok, 16);
    if (ok) {
        // 存储到指定协议的配置中
        if (!m_protocolConfigs.contains(protocolType)) {
            m_protocolConfigs[protocolType] = ConfigData();
        }
        m_protocolConfigs[protocolType].dataItemCache[identifier] = item;

        qDebug() << "加载数据块:" << item.name << "ID:" << QString("0x%1").arg(identifier, 8, 16, QChar('0')).toUpper()
                 << "包含" << item.blockItems.size() << "个项目" << "协议:" << protocolType;
    } else {
        setError(QString("无效的数据块标识: %1").arg(item.id));
        return false;
    }

    return true;
}

bool DataIdentifierConfig::parseBlockItems(QXmlStreamReader &xmlReader, DataItemConfig &blockItem)
{
    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "data_block") {
            break;
        }

        if (xmlReader.isStartElement() && xmlReader.name() == "item") {
            BlockItemInfo itemInfo;

            // 解析项目属性 - 只需要order和data_id
            itemInfo.order = xmlReader.attributes().value("order").toInt();

            QString dataIdStr = xmlReader.attributes().value("data_id").toString();
            bool ok;
            itemInfo.dataId = dataIdStr.toUInt(&ok, 16);

            if (ok && itemInfo.order > 0) {
                blockItem.blockItems.append(itemInfo);
                qDebug() << "  添加数据块项目: 顺序" << itemInfo.order
                         << "ID:" << QString("0x%1").arg(itemInfo.dataId, 8, 16, QChar('0')).toUpper();
            } else {
                qWarning() << "无效的数据块项目:" << dataIdStr << "顺序:" << itemInfo.order;
            }
        }
    }

    // 按order排序确保顺序正确
    std::sort(blockItem.blockItems.begin(), blockItem.blockItems.end(),
              [](const BlockItemInfo &a, const BlockItemInfo &b) {
                  return a.order < b.order;
              });

    return true;
}

bool DataIdentifierConfig::parseFieldItems(QXmlStreamReader &xmlReader, DataItemConfig &complexItem)
{
    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "data_item") {
            break;
        }

        if (xmlReader.isStartElement() && xmlReader.name() == "field") {
            FieldInfo fieldInfo;

            // 解析字段属性
            fieldInfo.name = xmlReader.attributes().value("name").toString();
            fieldInfo.format = xmlReader.attributes().value("format").toString();
            fieldInfo.length = xmlReader.attributes().value("length").toInt();
            fieldInfo.unit = xmlReader.attributes().value("unit").toString();
            fieldInfo.description = xmlReader.attributes().value("description").toString();

            if (!fieldInfo.name.isEmpty() && fieldInfo.length > 0) {
                complexItem.fields.append(fieldInfo);
                qDebug() << "  添加字段:" << fieldInfo.name
                         << "格式:" << fieldInfo.format
                         << "长度:" << fieldInfo.length
                         << "单位:" << fieldInfo.unit;
            } else {
                qWarning() << "无效的字段配置:" << fieldInfo.name << "长度:" << fieldInfo.length;
            }
        }
    }

    return true;
}





void DataIdentifierConfig::parseDataIdentifier(quint32 dataIdentifier,
                                              QString &categoryId,
                                              QString &subcategoryId,
                                              QString &itemId) const
{
    quint8 di3 = (dataIdentifier >> 24) & 0xFF;
    quint8 di2 = (dataIdentifier >> 16) & 0xFF;
    quint8 di1 = (dataIdentifier >> 8) & 0xFF;
    quint8 di0 = dataIdentifier & 0xFF;
    
    categoryId = QString("%1").arg(di3, 2, 16, QChar('0')).toUpper();
    subcategoryId = QString("%1").arg(di2, 2, 16, QChar('0')).toUpper();
    itemId = QString("%1%2").arg(di1, 2, 16, QChar('0')).arg(di0, 2, 16, QChar('0')).toUpper();
}

void DataIdentifierConfig::setError(const QString &error)
{
    m_lastError = error;
    qWarning() << "DataIdentifierConfig Error:" << error;
}

QString DataIdentifierConfig::getLastError() const
{
    return m_lastError;
}

bool DataIdentifierConfig::parseTemplateDataItem(QXmlStreamReader &xmlReader, ProtocolType protocolType)
{
    TemplateDataItem templateItem;

    // 解析基础属性
    QXmlStreamAttributes attrs = xmlReader.attributes();
    templateItem.base_id = attrs.value("id").toString();
    templateItem.base_name = attrs.value("name").toString();
    templateItem.base_description = attrs.value("description").toString();
    templateItem.format = attrs.value("format").toString();
    templateItem.length = attrs.value("length").toInt();
    templateItem.unit = attrs.value("unit").toString();
    templateItem.encoding = attrs.value("encoding").toString();

    // 解析DI范围
    templateItem.DI0_range = attrs.value("DI0").toString();
    templateItem.DI1_range = attrs.value("DI1").toString();
    templateItem.DI2_range = attrs.value("DI2").toString();

    templateItem.is_block = false;
    templateItem.is_composite = false;
    templateItem.is_conditional = false;
    templateItem.is_variable = (attrs.value("variable").toString().toLower() == "true");

    // 解析子元素
    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "data_item") {
            break;
        }

        if (xmlReader.isStartElement()) {
            QString elementName = xmlReader.name().toString();

            if (elementName == "DI0_names") {
                templateItem.DI0_names = parseDINameRules(xmlReader, elementName);
            }
            else if (elementName == "DI1_names") {
                templateItem.DI1_names = parseDINameRules(xmlReader, elementName);
            }
            else if (elementName == "DI2_names") {
                templateItem.DI2_names = parseDINameRules(xmlReader, elementName);
            }
            else if (elementName == "name_rule") {
                templateItem.name_rule = xmlReader.readElementText();
            }
            else if (elementName == "description_rule") {
                templateItem.description_rule = xmlReader.readElementText();
            }
            else if (elementName == "composite_format") {
                auto result = parseCompositeFormat(xmlReader);
                templateItem.composite_fields = result.first;
                templateItem.total_length = result.second;
                templateItem.is_composite = true;
            }
            else if (elementName == "conditional_format") {
                templateItem.conditional_formats = parseConditionalFormat(xmlReader);
                templateItem.is_conditional = true;
            }
            else if (elementName == "total_length") {
                templateItem.total_length = xmlReader.readElementText().toInt();
            }
        }
    }

    // 存储模板数据项到缓存
    if (!m_protocolConfigs.contains(protocolType)) {
        m_protocolConfigs[protocolType] = ConfigData();
    }
    m_protocolConfigs[protocolType].templateItems[templateItem.base_id] = templateItem;

    // 生成所有变体
    generateTemplateVariants(templateItem, protocolType);

    return true;
}

bool DataIdentifierConfig::parseTemplateDataBlock(QXmlStreamReader &xmlReader, ProtocolType protocolType)
{
    // 解析基础属性
    QXmlStreamAttributes attrs = xmlReader.attributes();
    QString blockId = attrs.value("id").toString();
    QString blockName = attrs.value("name").toString();
    QString blockDescription = attrs.value("description").toString();

    qDebug() << "开始解析数据块:" << blockId << blockName;
    QString format = attrs.value("format").toString();
    QString encoding = attrs.value("encoding").toString();

    // 解析DI范围属性
    QString di0Range = attrs.value("DI0").toString();
    QString di1Range = attrs.value("DI1").toString();
    QString di2Range = attrs.value("DI2").toString();

    // 检查是否有DI范围定义
    bool hasDIRange = !di0Range.isEmpty() || !di1Range.isEmpty() || !di2Range.isEmpty();

    if (hasDIRange) {
        qDebug() << "检测到DI范围定义 - DI0:" << di0Range << "DI1:" << di1Range << "DI2:" << di2Range;
        return parseDataBlockWithDIRange(xmlReader, protocolType, blockId, blockName, blockDescription,
                                       format, encoding, di0Range, di1Range, di2Range);
    }

    // 处理固定名称的数据块（原有逻辑）
    DataItemConfig blockItem;
    blockItem.id = blockId;
    blockItem.name = blockName;
    blockItem.description = blockDescription;
    blockItem.format = format;
    blockItem.encoding = encoding;
    blockItem.isBlock = true;
    blockItem.isComplex = false;

    QString autoCalcBase;
    QList<ItemPattern> itemPatterns;
    QString nameRule;
    QString descriptionRule;

    // 解析子元素
    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "data_block") {
            break;
        }

        if (xmlReader.isStartElement()) {
            QString elementName = xmlReader.name().toString();

            if (elementName == "auto_calculate_length") {
                autoCalcBase = xmlReader.attributes().value("base_item").toString();
            }
            else if (elementName == "include_items") {
                itemPatterns = parseIncludeItems(xmlReader);
            }
            else if (elementName == "name_rule") {
                nameRule = xmlReader.readElementText();
            }
            else if (elementName == "description_rule") {
                descriptionRule = xmlReader.readElementText();
            }
        }
    }

    // 如果有固定的name_rule和description_rule，使用它们
    if (!nameRule.isEmpty()) {
        blockItem.name = nameRule;
    }
    if (!descriptionRule.isEmpty()) {
        blockItem.description = descriptionRule;
    }

    // 生成数据块的item列表
    if (!itemPatterns.isEmpty()) {
        generateBlockItems(blockItem, itemPatterns, autoCalcBase, protocolType);
    }

    // 存储到缓存
    bool ok;
    quint32 identifier = blockItem.id.toUInt(&ok, 16);
    if (ok) {
        if (!m_protocolConfigs.contains(protocolType)) {
            m_protocolConfigs[protocolType] = ConfigData();
        }
        m_protocolConfigs[protocolType].dataItemCache[identifier] = blockItem;

        qDebug() << "生成数据块:" << blockItem.name
                 << "ID:" << QString("0x%1").arg(identifier, 8, 16, QChar('0')).toUpper()
                 << "包含项目数:" << blockItem.blockItems.size();
    }

    return true;
}

QList<DINameRule> DataIdentifierConfig::parseDINameRules(QXmlStreamReader &xmlReader, const QString &elementName)
{
    QList<DINameRule> rules;

    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == elementName) {
            break;
        }

        if (xmlReader.isStartElement() && xmlReader.name() == "name") {
            DINameRule rule;
            QXmlStreamAttributes attrs = xmlReader.attributes();

            rule.code = attrs.value("code").toString();
            rule.value = attrs.value("value").toString();
            rule.pattern = attrs.value("pattern").toString();
            rule.conversion = attrs.value("conversion").toString();

            rules.append(rule);
        }
    }

    return rules;
}

QPair<QList<FieldInfo>, int> DataIdentifierConfig::parseCompositeFormat(QXmlStreamReader &xmlReader)
{
    QList<FieldInfo> fields;
    int totalLength = 0;

    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "composite_format") {
            break;
        }

        if (xmlReader.isStartElement() && xmlReader.name() == "field") {
            FieldInfo field;
            QXmlStreamAttributes attrs = xmlReader.attributes();

            field.name = attrs.value("name").toString();
            field.format = attrs.value("format").toString();
            field.length = attrs.value("length").toInt();
            field.unit = attrs.value("unit").toString();
            field.offset = attrs.value("offset").toInt();
            field.description = attrs.value("description").toString();

            fields.append(field);
        }
        else if (xmlReader.isStartElement() && xmlReader.name() == "total_length") {
            totalLength = xmlReader.readElementText().toInt();
        }
    }

    return qMakePair(fields, totalLength);
}

QList<ConditionalFormat> DataIdentifierConfig::parseConditionalFormat(QXmlStreamReader &xmlReader)
{
    QList<ConditionalFormat> formats;

    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "conditional_format") {
            break;
        }

        if (xmlReader.isStartElement() && xmlReader.name() == "condition") {
            ConditionalFormat format;
            format.condition = xmlReader.attributes().value("DI1").toString();

            // 解析条件内的字段
            while (!xmlReader.atEnd()) {
                xmlReader.readNext();

                if (xmlReader.isEndElement() && xmlReader.name() == "condition") {
                    break;
                }

                if (xmlReader.isStartElement()) {
                    if (xmlReader.name() == "field") {
                        FieldInfo field;
                        QXmlStreamAttributes attrs = xmlReader.attributes();

                        field.name = attrs.value("name").toString();
                        field.format = attrs.value("format").toString();
                        field.length = attrs.value("length").toInt();
                        field.unit = attrs.value("unit").toString();
                        field.offset = attrs.value("offset").toInt();

                        format.fields.append(field);
                    }
                    else if (xmlReader.name() == "total_length") {
                        format.total_length = xmlReader.readElementText().toInt();
                    }
                }
            }

            formats.append(format);
        }
    }

    return formats;
}

void DataIdentifierConfig::generateTemplateVariants(const TemplateDataItem &templateItem, ProtocolType protocolType)
{
    // 展开DI范围 - 字符串顺序：DI3 DI2 DI1 DI0
    QStringList di0Values = expandDIRange(templateItem.DI0_range, templateItem.base_id.mid(6, 2));  // DI0在位置6-7
    QStringList di1Values = expandDIRange(templateItem.DI1_range, templateItem.base_id.mid(4, 2));  // DI1在位置4-5
    QStringList di2Values = expandDIRange(templateItem.DI2_range, templateItem.base_id.mid(2, 2));  // DI2在位置2-3

    // 生成所有组合
    for (const QString &di0 : di0Values) {
        for (const QString &di1 : di1Values) {
            for (const QString &di2 : di2Values) {
                DataItemConfig item;

                // 生成ID
                item.id = generateDataItemId(templateItem.base_id, di0, di1, di2);

                // 生成名称
                QString di0Name = getDIName(templateItem.DI0_names, di0);
                QString di1Name = getDIName(templateItem.DI1_names, di1);
                QString di2Name = getDIName(templateItem.DI2_names, di2);

                item.name = applyNameRule(templateItem.name_rule, di0Name, di1Name, di2Name);
                if (item.name.isEmpty()) {
                    item.name = templateItem.base_name;
                }

                item.description = applyNameRule(templateItem.description_rule, di0Name, di1Name, di2Name);
                if (item.description.isEmpty()) {
                    item.description = templateItem.base_description;
                }

                // 复制其他属性
                item.format = templateItem.format;
                item.length = templateItem.length;
                item.unit = templateItem.unit;
                item.encoding = templateItem.encoding;
                item.isBlock = templateItem.is_block;
                item.isComplex = templateItem.is_composite || templateItem.is_conditional;
                item.isVariable = templateItem.is_variable;

                // 处理复合格式
                if (templateItem.is_composite) {
                    // 复制字段并应用模板变量替换
                    item.fields.clear();
                    for (const FieldInfo &templateField : templateItem.composite_fields) {
                        FieldInfo field = templateField;
                        // 对字段名应用模板变量替换
                        field.name = applyNameRule(templateField.name, di0Name, di1Name, di2Name);
                        // 对字段描述也应用模板变量替换（如果有的话）
                        if (!templateField.description.isEmpty()) {
                            field.description = applyNameRule(templateField.description, di0Name, di1Name, di2Name);
                        }
                        item.fields.append(field);
                    }
                    if (templateItem.total_length > 0) {
                        item.length = templateItem.total_length;
                    }
                }

                // 处理条件格式
                if (templateItem.is_conditional) {
                    // 根据DI1值选择合适的条件格式
                    for (const ConditionalFormat &condFormat : templateItem.conditional_formats) {
                        if (matchesCondition(condFormat.condition, di1)) {
                            // 复制字段并应用模板变量替换
                            item.fields.clear();
                            for (const FieldInfo &templateField : condFormat.fields) {
                                FieldInfo field = templateField;
                                // 对字段名应用模板变量替换
                                field.name = applyNameRule(templateField.name, di0Name, di1Name, di2Name);
                                // 对字段描述也应用模板变量替换（如果有的话）
                                if (!templateField.description.isEmpty()) {
                                    field.description = applyNameRule(templateField.description, di0Name, di1Name, di2Name);
                                }
                                item.fields.append(field);
                            }
                            item.length = condFormat.total_length;
                            break;
                        }
                    }
                }

                // 处理数据块的自动长度计算
                if (templateItem.is_block && !templateItem.auto_calculate_base.isEmpty()) {
                    // 计算数据块长度：64个数据项 × 基础项长度
                    QString baseItemId = generateDataItemId(templateItem.auto_calculate_base, di0, "00", di2);
                    bool ok;
                    quint32 baseIdentifier = baseItemId.toUInt(&ok, 16);
                    if (ok) {
                        DataItemConfig baseItem = getDataItemConfig(baseIdentifier, protocolType);
                        if (baseItem.length > 0) {
                            item.length = 64 * baseItem.length;  // 64个费率项
                        }
                    }
                }

                // 存储到缓存
                bool ok;
                quint32 identifier = item.id.toUInt(&ok, 16);
                if (ok) {
                    if (!m_protocolConfigs.contains(protocolType)) {
                        m_protocolConfigs[protocolType] = ConfigData();
                    }
                    m_protocolConfigs[protocolType].dataItemCache[identifier] = item;

                    qDebug() << "生成模板变体:" << item.name
                             << "ID:" << QString("0x%1").arg(identifier, 8, 16, QChar('0')).toUpper()
                             << "协议:" << protocolType;
                }
            }
        }
    }
}

QStringList DataIdentifierConfig::expandDIRange(const QString &range, const QString &defaultValue)
{
    if (range.isEmpty()) {
        return QStringList() << defaultValue;
    }

    QStringList result;
    QStringList parts = range.split(',', QString::SkipEmptyParts);

    for (const QString &part : parts) {
        QString trimmedPart = part.trimmed();

        if (trimmedPart.contains('-')) {
            // 范围格式：如 "01-3F"
            QStringList rangeParts = trimmedPart.split('-');
            if (rangeParts.size() == 2) {
                bool ok1, ok2;
                int start = rangeParts[0].toInt(&ok1, 16);
                int end = rangeParts[1].toInt(&ok2, 16);

                if (ok1 && ok2 && start <= end) {
                    for (int i = start; i <= end; ++i) {
                        result << QString("%1").arg(i, 2, 16, QChar('0')).toUpper();
                    }
                }
            }
        } else {
            // 单个值
            result << trimmedPart.toUpper();
        }
    }

    return result;
}

QString DataIdentifierConfig::getDIName(const QList<DINameRule> &rules, const QString &diCode)
{
    for (const DINameRule &rule : rules) {
        if (matchesDIRule(rule, diCode)) {
            if (!rule.value.isEmpty()) {
                return rule.value;
            } else if (!rule.pattern.isEmpty()) {
                return applyConversion(rule.pattern, diCode, rule.conversion);
            }
        }
    }

    return diCode;  // 默认返回原始代码
}

QString DataIdentifierConfig::applyConversion(const QString &pattern, const QString &diCode, const QString &conversion)
{
    QString result = pattern;

    // 应用转换函数
    QString convertedValue = diCode;
    if (m_conversionFunctions.contains(conversion)) {
        convertedValue = m_conversionFunctions[conversion](diCode);
    }

    // 替换模板变量
    result.replace("{DI0}", convertedValue);
    result.replace("{DI1}", convertedValue);
    result.replace("{DI2}", convertedValue);

    return result;
}

QString DataIdentifierConfig::applyNameRule(const QString &rule, const QString &di0Name, const QString &di1Name, const QString &di2Name)
{
    QString result = rule;
    result.replace("{DI0_name}", di0Name);
    result.replace("{DI1_name}", di1Name);
    result.replace("{DI2_name}", di2Name);
    return result;
}

QString DataIdentifierConfig::generateDataItemId(const QString &baseId, const QString &di0, const QString &di1, const QString &di2)
{
    QString result = baseId;

    // 字符串顺序：DI3 DI2 DI1 DI0 (从左到右)
    // 参数映射：di2->DI2, di1->DI1, di0->DI0
    // 位置映射：DI3(0-1), DI2(2-3), DI1(4-5), DI0(6-7)

    if (!di2.isEmpty() && di2 != baseId.mid(2, 2)) {
        result = result.mid(0, 2) + di2 + result.mid(4);  // DI2 -> 位置2-3
    }
    if (!di1.isEmpty() && di1 != baseId.mid(4, 2)) {
        result = result.mid(0, 4) + di1 + result.mid(6);  // DI1 -> 位置4-5
    }
    if (!di0.isEmpty() && di0 != baseId.mid(6, 2)) {
        result = result.mid(0, 6) + di0;  // DI0 -> 位置6-7
    }

    return result.toUpper();
}

bool DataIdentifierConfig::matchesDIRule(const DINameRule &rule, const QString &diCode)
{
    if (rule.code.contains('-')) {
        // 范围匹配
        QStringList parts = rule.code.split('-');
        if (parts.size() == 2) {
            bool ok1, ok2, ok3;
            int start = parts[0].toInt(&ok1, 16);
            int end = parts[1].toInt(&ok2, 16);
            int code = diCode.toInt(&ok3, 16);

            return ok1 && ok2 && ok3 && code >= start && code <= end;
        }
    } else {
        // 精确匹配
        return rule.code.toUpper() == diCode.toUpper();
    }

    return false;
}

bool DataIdentifierConfig::matchesCondition(const QString &condition, const QString &diValue)
{
    if (condition.contains('-')) {
        // 范围条件
        QStringList parts = condition.split('-');
        if (parts.size() == 2) {
            bool ok1, ok2, ok3;
            int start = parts[0].toInt(&ok1, 16);
            int end = parts[1].toInt(&ok2, 16);
            int value = diValue.toInt(&ok3, 16);

            return ok1 && ok2 && ok3 && value >= start && value <= end;
        }
    } else {
        // 精确条件
        return condition.toUpper() == diValue.toUpper();
    }

    return false;
}

QList<ItemPattern> DataIdentifierConfig::parseIncludeItems(QXmlStreamReader &xmlReader)
{
    QList<ItemPattern> patterns;

    qDebug() << "开始解析 include_items";

    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "include_items") {
            break;
        }

        if (xmlReader.isStartElement() && xmlReader.name() == "item_pattern") {
            ItemPattern pattern;
            QXmlStreamAttributes attrs = xmlReader.attributes();

            pattern.base_id = attrs.value("base_id").toString();
            pattern.DI0_range = attrs.value("DI0_range").toString();
            pattern.DI1_range = attrs.value("DI1_range").toString();
            pattern.DI2_range = attrs.value("DI2_range").toString();
            pattern.item_length = attrs.value("item_length").toInt();

            qDebug() << "解析到 item_pattern:";
            qDebug() << "  base_id:" << pattern.base_id;
            qDebug() << "  DI0_range:" << pattern.DI0_range;
            qDebug() << "  DI1_range:" << pattern.DI1_range;
            qDebug() << "  DI2_range:" << pattern.DI2_range;

            patterns.append(pattern);
        }
    }

    qDebug() << "解析完成，共" << patterns.size() << "个 pattern";
    return patterns;
}

void DataIdentifierConfig::generateBlockItems(DataItemConfig &blockItem, const QList<ItemPattern> &patterns, const QString &autoCalcBase, ProtocolType protocolType)
{
    int totalLength = 0;
    int order = 1;
    bool hasVariableItems = false;  // 检查是否包含可变长度数据项

    for (const ItemPattern &pattern : patterns) {
        QStringList di0Values = expandDIRange(pattern.DI0_range, pattern.base_id.mid(6, 2));  // DI0在位置6-7
        QStringList di1Values = expandDIRange(pattern.DI1_range, pattern.base_id.mid(4, 2));  // DI1在位置4-5
        QStringList di2Values = expandDIRange(pattern.DI2_range, pattern.base_id.mid(2, 2));  // DI2在位置2-3


        // 确定哪个DI有范围（只允许一个DI有范围）
        bool hasDI0Range = !pattern.DI0_range.isEmpty();
        bool hasDI1Range = !pattern.DI1_range.isEmpty();
        bool hasDI2Range = !pattern.DI2_range.isEmpty();

        int rangeCount = (hasDI0Range ? 1 : 0) + (hasDI1Range ? 1 : 0) + (hasDI2Range ? 1 : 0);

        if (rangeCount > 1) {
            qWarning() << "数据块中只允许一个DI有范围，当前有" << rangeCount << "个DI有范围";
            continue;  // 跳过这个pattern
        }

        // 根据哪个DI有范围来生成数据项
        if (hasDI0Range) {
            // 只有DI0变化
            QString fixedDI1 = di1Values.first();
            QString fixedDI2 = di2Values.first();
            for (const QString &di0 : di0Values) {
                QString itemId = generateDataItemId(pattern.base_id, di0, fixedDI1, fixedDI2);

                bool ok;
                quint32 numericId = itemId.toUInt(&ok, 16);
                if (ok) {
                    BlockItemInfo itemInfo;
                    itemInfo.order = order++;
                    itemInfo.dataId = numericId;
                    blockItem.blockItems.append(itemInfo);

                    // 检查该数据项是否为可变长度
                    DataItemConfig itemConfig = getDataItemConfig(numericId, protocolType);
                    if (itemConfig.isVariable) {
                        hasVariableItems = true;
                    }
                }

                // 计算长度
                if (pattern.item_length > 0) {
                    totalLength += pattern.item_length;
                } else if (!autoCalcBase.isEmpty()) {
                    // 从基础项获取长度
                    bool ok;
                    quint32 baseIdentifier = autoCalcBase.toUInt(&ok, 16);
                    if (ok) {
                        DataItemConfig baseItem = getDataItemConfig(baseIdentifier, protocolType);
                        if (baseItem.length > 0) {
                            totalLength += baseItem.length;
                        }
                    }
                }
            }
        } else if (hasDI1Range) {
            // 只有DI1变化
            QString fixedDI0 = di0Values.first();
            QString fixedDI2 = di2Values.first();
            for (const QString &di1 : di1Values) {
                QString itemId = generateDataItemId(pattern.base_id, fixedDI0, di1, fixedDI2);

                bool ok;
                quint32 numericId = itemId.toUInt(&ok, 16);
                if (ok) {
                    BlockItemInfo itemInfo;
                    itemInfo.order = order++;
                    itemInfo.dataId = numericId;
                    blockItem.blockItems.append(itemInfo);

                    // 检查该数据项是否为可变长度
                    DataItemConfig itemConfig = getDataItemConfig(numericId, protocolType);
                    if (itemConfig.isVariable) {
                        hasVariableItems = true;
                    }
                }

                // 计算长度
                if (pattern.item_length > 0) {
                    totalLength += pattern.item_length;
                } else if (!autoCalcBase.isEmpty()) {
                    // 从基础项获取长度
                    bool ok;
                    quint32 baseIdentifier = autoCalcBase.toUInt(&ok, 16);
                    if (ok) {
                        DataItemConfig baseItem = getDataItemConfig(baseIdentifier, protocolType);
                        if (baseItem.length > 0) {
                            totalLength += baseItem.length;
                        }
                    }
                }
            }
        } else if (hasDI2Range) {
            // 只有DI2变化
            QString fixedDI0 = di0Values.first();
            QString fixedDI1 = di1Values.first();
            for (const QString &di2 : di2Values) {
                QString itemId = generateDataItemId(pattern.base_id, fixedDI0, fixedDI1, di2);

                bool ok;
                quint32 numericId = itemId.toUInt(&ok, 16);
                if (ok) {
                    BlockItemInfo itemInfo;
                    itemInfo.order = order++;
                    itemInfo.dataId = numericId;
                    blockItem.blockItems.append(itemInfo);

                    // 检查该数据项是否为可变长度
                    DataItemConfig itemConfig = getDataItemConfig(numericId, protocolType);
                    if (itemConfig.isVariable) {
                        hasVariableItems = true;
                    }
                }

                // 计算长度
                if (pattern.item_length > 0) {
                    totalLength += pattern.item_length;
                } else if (!autoCalcBase.isEmpty()) {
                    // 从基础项获取长度
                    bool ok;
                    quint32 baseIdentifier = autoCalcBase.toUInt(&ok, 16);
                    if (ok) {
                        DataItemConfig baseItem = getDataItemConfig(baseIdentifier, protocolType);
                        if (baseItem.length > 0) {
                            totalLength += baseItem.length;
                        }
                    }
                }
            }
        } else {
            // 没有范围，只有一个固定的数据项
            QString itemId = generateDataItemId(pattern.base_id, di0Values.first(), di1Values.first(), di2Values.first());

            bool ok;
            quint32 numericId = itemId.toUInt(&ok, 16);
            if (ok) {
                BlockItemInfo itemInfo;
                itemInfo.order = order++;
                itemInfo.dataId = numericId;
                blockItem.blockItems.append(itemInfo);

                // 检查该数据项是否为可变长度
                DataItemConfig itemConfig = getDataItemConfig(numericId, protocolType);
                if (itemConfig.isVariable) {
                    hasVariableItems = true;
                }
            }

            // 计算长度
            if (pattern.item_length > 0) {
                totalLength += pattern.item_length;
            } else if (!autoCalcBase.isEmpty()) {
                // 从基础项获取长度
                bool ok;
                quint32 baseIdentifier = autoCalcBase.toUInt(&ok, 16);
                if (ok) {
                    DataItemConfig baseItem = getDataItemConfig(baseIdentifier, protocolType);
                    if (baseItem.length > 0) {
                        totalLength += baseItem.length;
                    }
                }
            }
        }
    }

    // 设置总长度
    if (blockItem.length <= 0) {
        blockItem.length = totalLength;
    }

    // 设置数据块的可变长度标志
    blockItem.isVariable = hasVariableItems;

    qDebug() << "生成数据块项目:" << blockItem.name
             << "总项目数:" << blockItem.blockItems.size()
             << "总长度:" << blockItem.length
             << "是否可变长度:" << blockItem.isVariable;
             //打印数据项
             for (const BlockItemInfo &item : blockItem.blockItems) {
                 qDebug() << "  项目" << item.order << ":"
                          << QString("0x%1").arg(item.dataId, 8, 16, QChar('0')).toUpper();
             }

}

bool DataIdentifierConfig::parseDataBlockWithDIRange(QXmlStreamReader &xmlReader, ProtocolType protocolType,
                                                    const QString &baseBlockId, const QString &baseBlockName,
                                                    const QString &baseBlockDescription, const QString &format,
                                                    const QString &encoding, const QString &di0Range,
                                                    const QString &di1Range, const QString &di2Range)
{
    // 展开DI范围，如果没有定义范围则使用基础ID中的对应字节作为默认值
    QStringList di0Values = expandDIRange(di0Range, baseBlockId.mid(6, 2));
    QStringList di1Values = expandDIRange(di1Range, baseBlockId.mid(4, 2));
    QStringList di2Values = expandDIRange(di2Range, baseBlockId.mid(2, 2));

    // 确保每个DI值列表至少有一个元素（使用基础ID中的默认值）
    if (di0Values.isEmpty()) {
        di0Values.append(baseBlockId.mid(6, 2));
    }
    if (di1Values.isEmpty()) {
        di1Values.append(baseBlockId.mid(4, 2));
    }
    if (di2Values.isEmpty()) {
        di2Values.append(baseBlockId.mid(2, 2));
    }

    // 解析include_items、auto_calculate_length和DI_names（只解析一次，后面复用）
    QString autoCalcBase;
    QList<ItemPattern> itemPatterns;
    QList<DINameRule> blockDI0Names;
    QList<DINameRule> blockDI1Names;
    QList<DINameRule> blockDI2Names;
    QString blockNameRule;
    QString blockDescriptionRule;

    // 先读取所有子元素内容
    while (!xmlReader.atEnd()) {
        xmlReader.readNext();

        if (xmlReader.isEndElement() && xmlReader.name() == "data_block") {
            break;
        }

        if (xmlReader.isStartElement()) {
            QString elementName = xmlReader.name().toString();

            if (elementName == "auto_calculate_length") {
                autoCalcBase = xmlReader.attributes().value("base_item").toString();
            }
            else if (elementName == "include_items") {
                itemPatterns = parseIncludeItems(xmlReader);
            }
            else if (elementName == "DI0_names") {
                blockDI0Names = parseDINameRules(xmlReader, elementName);
            }
            else if (elementName == "DI1_names") {
                blockDI1Names = parseDINameRules(xmlReader, elementName);
            }
            else if (elementName == "DI2_names") {
                blockDI2Names = parseDINameRules(xmlReader, elementName);
            }
            else if (elementName == "name_rule") {
                blockNameRule = xmlReader.readElementText();
            }
            else if (elementName == "description_rule") {
                blockDescriptionRule = xmlReader.readElementText();
            }
        }
    }

    // 支持多个DI范围的组合
    bool hasDI0Range = !di0Range.isEmpty();
    bool hasDI1Range = !di1Range.isEmpty();
    bool hasDI2Range = !di2Range.isEmpty();

    qDebug() << "DI范围信息 - DI0:" << (hasDI0Range ? di0Range : "固定")
             << "DI1:" << (hasDI1Range ? di1Range : "固定")
             << "DI2:" << (hasDI2Range ? di2Range : "固定");
    qDebug() << "将生成" << (di0Values.size() * di1Values.size() * di2Values.size()) << "个数据块";

    // 生成所有DI组合的数据块
    for (const QString &di0 : di0Values) {
        for (const QString &di1 : di1Values) {
            for (const QString &di2 : di2Values) {
                generateSingleDataBlockFromDI(baseBlockId, baseBlockName, baseBlockDescription, format, encoding,
                                            itemPatterns, autoCalcBase, di0, di1, di2, protocolType,
                                            blockDI0Names, blockDI1Names, blockDI2Names,
                                            blockNameRule, blockDescriptionRule);
            }
        }
    }

    return true;
}

void DataIdentifierConfig::generateSingleDataBlockFromDI(const QString &baseBlockId, const QString &baseBlockName,
                                                        const QString &baseBlockDescription, const QString &format,
                                                        const QString &encoding, const QList<ItemPattern> &itemPatterns,
                                                        const QString &autoCalcBase, const QString &di0, const QString &di1,
                                                        const QString &di2, ProtocolType protocolType,
                                                        const QList<DINameRule> &blockDI0Names,
                                                        const QList<DINameRule> &blockDI1Names,
                                                        const QList<DINameRule> &blockDI2Names,
                                                        const QString &blockNameRule,
                                                        const QString &blockDescriptionRule)
{
    // 生成新的数据块ID
    QString blockId = generateDataItemId(baseBlockId, di0, di1, di2);

    // 生成数据块名称和描述
    QString blockName = baseBlockName;
    QString blockDescription = baseBlockDescription;

    // 优先使用数据块自身的配置
    if (!blockNameRule.isEmpty() || !blockDescriptionRule.isEmpty()) {
        // 使用数据块自身的DI_names配置
        QString di0Name = getDIName(blockDI0Names, di0);
        QString di1Name = getDIName(blockDI1Names, di1);
        QString di2Name = getDIName(blockDI2Names, di2);

        // 对于数据块，DI1=FF时应该返回空字符串
        if (di1 == "FF") {
            di1Name = "";
        }

        // 应用数据块的命名规则
        if (!blockNameRule.isEmpty()) {
            blockName = applyNameRule(blockNameRule, di0Name, di1Name, di2Name);
        }
        if (!blockDescriptionRule.isEmpty()) {
            blockDescription = applyNameRule(blockDescriptionRule, di0Name, di1Name, di2Name);
        }
    }
    // 如果数据块没有配置，则尝试使用数据项的命名规则作为后备
    else if (!itemPatterns.isEmpty()) {
        QString baseItemId = itemPatterns.first().base_id;
        TemplateDataItem templateItem = findTemplateDataItem(baseItemId, protocolType);

        if (!templateItem.base_id.isEmpty()) {
            // 使用数据项的命名规则
            QString di0Name = getDIName(templateItem.DI0_names, di0);
            QString di1Name = getDIName(templateItem.DI1_names, di1);
            QString di2Name = getDIName(templateItem.DI2_names, di2);

            // 对于数据块，DI1=FF时应该返回空字符串
            if (di1 == "FF") {
                di1Name = "";
            }

            // 应用命名规则生成数据块名称
            if (!templateItem.name_rule.isEmpty()) {
                blockName = applyNameRule(templateItem.name_rule, di0Name, di1Name, di2Name);
                // 为数据块名称添加"数据块"后缀
                if (!blockName.contains("数据块")) {
                    blockName += "数据块";
                }
            }

            // 应用命名规则生成数据块描述
            if (!templateItem.description_rule.isEmpty()) {
                blockDescription = applyNameRule(templateItem.description_rule, di0Name, di1Name, di2Name);
                // 为数据块描述添加"数据块"后缀
                if (!blockDescription.contains("数据块")) {
                    blockDescription += "数据块";
                }
            }
        }
    }

    // 如果没有找到模板数据项，则使用简单的DI替换方法作为后备
    if (blockName == baseBlockName) {
        if (!di0.isEmpty()) {
            blockName = updateBlockNameWithDI(baseBlockName, di0, "DI0");
            blockDescription = updateBlockNameWithDI(baseBlockDescription, di0, "DI0");
        }
        if (!di1.isEmpty()) {
            blockName = updateBlockNameWithDI(baseBlockName, di1, "DI1");
            blockDescription = updateBlockNameWithDI(baseBlockDescription, di1, "DI1");
        }
        if (!di2.isEmpty()) {
            blockName = updateBlockNameWithDI(baseBlockName, di2, "DI2");
            blockDescription = updateBlockNameWithDI(baseBlockDescription, di2, "DI2");
        }
    }

    // 创建数据块配置
    DataItemConfig blockItem;
    blockItem.id = blockId;
    blockItem.name = blockName;
    blockItem.description = blockDescription;
    blockItem.format = format;
    blockItem.encoding = encoding;
    blockItem.isBlock = true;
    blockItem.isComplex = false;

    // 调整item_patterns中的base_id
    QList<ItemPattern> adjustedPatterns;
    for (const ItemPattern &pattern : itemPatterns) {
        ItemPattern adjustedPattern = pattern;
        // 用当前数据块的DI值替换item_pattern的base_id
        adjustedPattern.base_id = generateDataItemId(pattern.base_id, di0, di1, di2);
        adjustedPatterns.append(adjustedPattern);
    }

    // 生成数据块的item列表
    if (!adjustedPatterns.isEmpty()) {
        generateBlockItems(blockItem, adjustedPatterns, autoCalcBase, protocolType);
    }

    // 存储到缓存
    bool ok;
    quint32 identifier = blockItem.id.toUInt(&ok, 16);
    if (ok) {
        if (!m_protocolConfigs.contains(protocolType)) {
            m_protocolConfigs[protocolType] = ConfigData();
        }
        m_protocolConfigs[protocolType].dataItemCache[identifier] = blockItem;

        qDebug() << "生成DI范围数据块:" << blockItem.name
                 << "ID:" << QString("0x%1").arg(identifier, 8, 16, QChar('0')).toUpper()
                 << "包含项目数:" << blockItem.blockItems.size();
    }
}

QString DataIdentifierConfig::updateBlockNameWithDI(const QString &baseName, const QString &diValue, const QString &diType)
{
    QString result = baseName;


    return result;
}

TemplateDataItem DataIdentifierConfig::findTemplateDataItem(const QString &baseId, ProtocolType protocolType)
{
    if (m_protocolConfigs.contains(protocolType)) {
        const ConfigData &configData = m_protocolConfigs[protocolType];
        if (configData.templateItems.contains(baseId)) {
            return configData.templateItems[baseId];
        }
    }

    // 返回空的模板数据项
    return TemplateDataItem();
}
