import QtQuick 2.12
import QtQuick.Controls 2.12

/**
 * @brief 自定义脚本按钮组件
 * @details 用于显示和执行自定义脚本
 */
Button {
    id: customScriptButton
    
    // 脚本路径属性
    property string scriptPath: ""
    
    // 自定义信号，传递脚本路径
    signal scriptClicked(string path)
    
    // 按钮样式
    background: Rectangle {
        color: parent.down ? "#005fa3" : (parent.hovered ? "#0078d7" : "#0086f0")
        radius: 4
    }
    
    // 按钮文本样式
    contentItem: Text {
        text: parent.text
        color: "white"
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        font.pixelSize: 12
        elide: Text.ElideRight
        wrapMode: Text.NoWrap
    }
    
    // 添加工具提示，鼠标悬停时显示完整脚本名称和路径
    ToolTip {
        visible: parent.hovered
        text: parent.text + "\n路径: " + scriptPath
        delay: 500
    }
    
    // 点击事件处理
    onClicked: {
        // 发送自定义信号，传递脚本路径
        scriptClicked(scriptPath)
    }
} 