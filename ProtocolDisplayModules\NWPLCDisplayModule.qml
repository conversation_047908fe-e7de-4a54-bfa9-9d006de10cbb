import QtQuick 2.12

/**
 * @brief NW_PLC协议显示模块
 * 专门处理南网PLC协议的树形显示
 */
BaseDisplayModule {
    id: nwplcModule
    
    /**
     * @brief 构建NW_PLC协议特定的树形结构
     * @param parsedData 解析后的数据
     * @param level 层级
     */
    function buildProtocolSpecificTree(parsedData, level) {
        if (!parsedData) {
            addTreeItemWithVisibility("解析错误", "", "没有可显示的协议数据", level, false, false, true)
            return
        }
        
        console.log("NWPLCDisplayModule: buildProtocolSpecificTree - parsedData:", JSON.stringify(parsedData))
        
        // 获取原始报文用于提取原始字节数据
        var rawMessage = getRawMessage()
        
        // 构建NW_PLC协议帧结构（示例结构，需要根据实际协议调整）
        buildNWPLCFrameTree(parsedData, level, rawMessage)
    }
    
    /**
     * @brief 构建NW_PLC帧结构树（示例实现）
     */
    function buildNWPLCFrameTree(parsedData, level, rawMessage) {
        // 示例：NW_PLC协议可能有不同的帧结构
        
        // 2级：同步头
        var syncRaw = extractBytes(rawMessage, 1, 4)
        addTreeItemWithVisibility("同步头", syncRaw, "PLC同步序列", level, false, false, true)
        
        // 2级：帧控制
        var frameControlRaw = extractBytes(rawMessage, 5, 1)
        addTreeItemWithVisibility("帧控制", frameControlRaw, "帧控制字段", level, true, true, true)
        
        // 3级：帧控制子项
        buildFrameControlTree(parsedData, level + 1)
        
        // 2级：源地址
        var srcAddrRaw = extractBytes(rawMessage, 6, 2)
        var srcAddrDesc = "源地址：" + (parsedData.sourceAddress || "")
        addTreeItemWithVisibility("源地址", srcAddrRaw, srcAddrDesc, level, false, false, true)
        
        // 2级：目标地址
        var dstAddrRaw = extractBytes(rawMessage, 8, 2)
        var dstAddrDesc = "目标地址：" + (parsedData.destinationAddress || "")
        addTreeItemWithVisibility("目标地址", dstAddrRaw, dstAddrDesc, level, false, false, true)
        
        // 2级：数据单元
        if (parsedData.dataUnit) {
            addTreeItemWithVisibility("数据单元", "", "PLC数据单元", level, true, true, true)
            buildDataUnitTree(parsedData, level + 1, rawMessage)
        }
        
        // 2级：帧校验序列
        var fcsRaw = rawMessage.length >= 8 ? rawMessage.substring(rawMessage.length - 8, rawMessage.length - 4) : ""
        addTreeItemWithVisibility("帧校验序列", fcsRaw, "FCS校验", level, false, false, true)
        
        // 2级：结束定界符
        var endDelimiterRaw = rawMessage.length >= 4 ? rawMessage.substring(rawMessage.length - 4) : ""
        addTreeItemWithVisibility("结束定界符", endDelimiterRaw, "帧结束定界符", level, false, false, true)
    }
    
    /**
     * @brief 构建帧控制树（示例实现）
     */
    function buildFrameControlTree(parsedData, level) {
        if (parsedData.frameControl) {
            var frameControl = parsedData.frameControl
            
            // 3级：帧类型
            var frameTypeValue = frameControl.frameType || 0
            var frameTypeDesc = getFrameTypeDescription(frameTypeValue)
            addTreeItemWithVisibility("帧类型", frameTypeValue.toString(), frameTypeDesc, level, false, false, true)
            
            // 3级：优先级
            var priorityValue = frameControl.priority || 0
            var priorityDesc = "优先级：" + priorityValue
            addTreeItemWithVisibility("优先级", priorityValue.toString(), priorityDesc, level, false, false, true)
            
            // 3级：确认标志
            var ackFlag = frameControl.ackRequired ? "1" : "0"
            var ackDesc = frameControl.ackRequired ? "需要确认" : "无需确认"
            addTreeItemWithVisibility("确认标志", ackFlag, ackDesc, level, false, false, true)
        }
    }
    
    /**
     * @brief 构建数据单元树（示例实现）
     */
    function buildDataUnitTree(parsedData, level, rawMessage) {
        if (parsedData.dataUnit) {
            var dataUnit = parsedData.dataUnit
            
            // 3级：应用层协议标识
            var protocolIdRaw = extractBytes(rawMessage, 10, 1)
            var protocolDesc = "应用协议：" + (dataUnit.protocolId || "未知")
            addTreeItemWithVisibility("协议标识", protocolIdRaw, protocolDesc, level, false, false, true)
            
            // 3级：服务类型
            var serviceTypeRaw = extractBytes(rawMessage, 11, 1)
            var serviceDesc = "服务类型：" + (dataUnit.serviceType || "未知")
            addTreeItemWithVisibility("服务类型", serviceTypeRaw, serviceDesc, level, false, false, true)
            
            // 3级：数据载荷
            if (dataUnit.payload) {
                addTreeItemWithVisibility("数据载荷", "", "应用数据", level, true, true, true)
                buildPayloadTree(dataUnit.payload, level + 1, rawMessage)
            }
        }
    }
    
    /**
     * @brief 构建载荷树（示例实现）
     */
    function buildPayloadTree(payload, level, rawMessage) {
        // 4级：载荷数据项
        if (payload.dataItems && payload.dataItems.length > 0) {
            for (var i = 0; i < payload.dataItems.length; i++) {
                var item = payload.dataItems[i]
                var itemName = item.name || ("载荷项" + (i + 1))
                var itemValue = item.value || ""
                var itemDesc = item.description || itemValue
                
                // 计算载荷项在报文中的位置（示例计算）
                var itemRaw = extractBytes(rawMessage, 12 + i * 2, 2)
                
                addTreeItemWithVisibility(itemName, itemRaw, itemDesc, level, false, false, true)
            }
        } else {
            addTreeItemWithVisibility("载荷数据", "", "PLC载荷内容", level, false, false, true)
        }
    }
    
    /**
     * @brief 获取帧类型描述
     */
    function getFrameTypeDescription(frameType) {
        switch (frameType) {
            case 0: return "数据帧"
            case 1: return "确认帧"
            case 2: return "控制帧"
            case 3: return "管理帧"
            default: return "未知帧类型：" + frameType
        }
    }
}
