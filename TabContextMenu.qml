import QtQuick 2.12
import QtQuick.Controls 2.12

Menu {
    id: tabContextMenu
    
    property string filePath: ""
    property int tabIndex: -1
    
    signal closeTab(int index)
    signal closeOtherTabs(int index)
    
    MenuItem {
        text: qsTr("打开文件所在目录")
        onTriggered: {
            console.log("Menu: 打开文件所在目录", tabContextMenu.filePath);
            if (tabContextMenu.filePath && tabContextMenu.filePath.length > 0) {
                if (typeof archiveHandler !== "undefined" && archiveHandler && 
                    typeof archiveHandler.openFileDirectory === "function") {
                    
                    // 确保使用完整的文件路径
                    var result = archiveHandler.openFileDirectory(tabContextMenu.filePath);
                    console.log("打开文件目录结果:", result);
                } else {
                    console.error("C++端未实现openFileDirectory方法");
                }
            }
        }
    }
    
    MenuSeparator { }
    
    MenuItem {
        text: qsTr("关闭此标签页")
        onTriggered: {
            if (tabContextMenu.tabIndex >= 0) {
                tabContextMenu.closeTab(tabContextMenu.tabIndex);
            }
        }
    }
    
    MenuItem {
        text: qsTr("关闭其他标签页")
        onTriggered: {
            if (tabContextMenu.tabIndex >= 0) {
                tabContextMenu.closeOtherTabs(tabContextMenu.tabIndex);
            }
        }
    }
    
    onClosed: {
        // 菜单关闭后销毁自身
        tabContextMenu.destroy();
    }
} 