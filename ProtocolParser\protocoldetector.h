#ifndef PROTOCOLDETECTOR_H
#define PROTOCOLDETECTOR_H

#include <QObject>
#include <QByteArray>
#include <QString>
#include <QVector>

#include "protocoltypes.h"

/**
 * @brief 协议检测器类
 * 
 * 负责分析字节数据，识别可能的协议类型
 */
class ProtocolDetector : public QObject
{
    Q_OBJECT

public:
    explicit ProtocolDetector(QObject *parent = nullptr);
    ~ProtocolDetector();

    /**
     * @brief 检测协议类型
     * @param data 字节数据
     * @return 帧解析结果
     */
    FrameParseResult detectProtocol(const QByteArray &data);

    /**
     * @brief 检测DL/T645-2007协议
     * @param data 字节数据
     * @return 帧解析结果
     */
    FrameParseResult detectDLT645_2007(const QByteArray &data);

    /**
     * @brief 检测DL/T645-1997协议
     * @param data 字节数据
     * @return 帧解析结果
     */
    FrameParseResult detectDLT645_1997(const QByteArray &data);

    /**
     * @brief 检测南网上行协议
     * @param data 字节数据
     * @return 帧解析结果
     */
    FrameParseResult detectNWUP(const QByteArray &data);

    /**
     * @brief 设置检测的最小帧长度
     * @param minLength 最小长度
     */
    void setMinFrameLength(int minLength);

    /**
     * @brief 设置检测的最大帧长度
     * @param maxLength 最大长度
     */
    void setMaxFrameLength(int maxLength);

    /**
     * @brief 启用或禁用特定协议的检测
     * @param protocolType 协议类型
     * @param enabled 是否启用
     */
    void setProtocolEnabled(ProtocolType protocolType, bool enabled);

private:
    /**
     * @brief 查找DL/T645帧的起始和结束位置
     * @param data 字节数据
     * @param frameStart 帧起始位置（输出）
     * @param frameEnd 帧结束位置（输出）
     * @return 是否找到有效帧
     */
    bool findDLT645Frame(const QByteArray &data, int &frameStart, int &frameEnd);

    /**
     * @brief 验证DL/T645帧的校验码
     * @param frameData 帧数据
     * @return 校验是否正确
     */
    bool verifyDLT645Checksum(const QByteArray &frameData);

    /**
     * @brief 计算DL/T645校验码
     * @param data 数据（不包含校验码）
     * @return 校验码
     */
    quint8 calculateDLT645Checksum(const QByteArray &data);

    /**
     * @brief 查找南网上行协议帧的起始和结束位置
     * @param data 字节数据
     * @param frameStart 帧起始位置（输出）
     * @param frameEnd 帧结束位置（输出）
     * @return 是否找到有效帧
     */
    bool findNWUPFrame(const QByteArray &data, int &frameStart, int &frameEnd);

    /**
     * @brief 验证南网上行协议帧的校验码
     * @param frameData 帧数据
     * @return 校验是否正确
     */
    bool verifyNWUPChecksum(const QByteArray &frameData);

    /**
     * @brief 计算南网上行协议校验码
     * @param data 数据（不包含校验码）
     * @return 校验码
     */
    quint8 calculateNWUPChecksum(const QByteArray &data);



    /**
     * @brief 创建错误结果
     * @param errorMessage 错误信息
     * @return 错误的帧解析结果
     */
    FrameParseResult createErrorResult(const QString &errorMessage);

    /**
     * @brief 创建成功结果
     * @param protocolType 协议类型
     * @param data 原始数据
     * @param frameStart 帧起始位置
     * @param frameEnd 帧结束位置
     * @return 成功的帧解析结果
     */
    FrameParseResult createSuccessResult(ProtocolType protocolType, 
                                       const QByteArray &data,
                                       int frameStart, 
                                       int frameEnd);

private:
    int m_minFrameLength;                           // 最小帧长度
    int m_maxFrameLength;                           // 最大帧长度
    QVector<ProtocolType> m_enabledProtocols;       // 启用的协议类型
};

#endif // PROTOCOLDETECTOR_H
