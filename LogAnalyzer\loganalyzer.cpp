#include "loganalyzer.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QTextStream>
#include <QDebug>
#include <QScrollBar>

LogAnalyzer::LogAnalyzer(QWidget* parent)
    : QWidget(parent)
    , m_logDisplay(nullptr)
    , m_analyzeButton(nullptr)
    , m_clearButton(nullptr)
    , m_statusLabel(nullptr)
{
}

LogAnalyzer::~LogAnalyzer()
{
}

QWidget* LogAnalyzer::createLogAnalyzerWidget()
{
    QWidget* widget = new QWidget;
    QVBoxLayout* layout = new QVBoxLayout(widget);
    
    m_logDisplay = new QTextEdit;
    
    m_logDisplay->setReadOnly(true);
    
    QFont font("Consolas", 10);
    m_logDisplay->setFont(font);
    
    m_logDisplay->setText("日志分析器已准备就绪");
    
    m_analyzeButton = new QPushButton(tr("选择日志文件"));
    m_clearButton = new QPushButton(tr("清空"));
    
    m_statusLabel = new QLabel(tr("请选择日志文件进行分析"));
    
    QHBoxLayout* buttonLayout = new QHBoxLayout;
    buttonLayout->addWidget(m_analyzeButton);
    buttonLayout->addWidget(m_clearButton);
    
    layout->addWidget(m_statusLabel);
    layout->addLayout(buttonLayout);
    layout->addWidget(m_logDisplay, 1);
    
    m_logDisplay->show();
    
    connect(m_analyzeButton, &QPushButton::clicked, this, &LogAnalyzer::analyzeLog);
    connect(m_clearButton, &QPushButton::clicked, this, &LogAnalyzer::clearLog);
    
    return widget;
}

void LogAnalyzer::analyzeLog()
{
    QString filePath = QFileDialog::getOpenFileName(
        this,
        tr("选择日志文件"),
        m_termDiskPath.isEmpty() ? QString() : m_termDiskPath,
        tr("日志文件 (*.log *.txt);;所有文件 (*.*)")
    );
    
    if (filePath.isEmpty())
    {
        return;
    }
    
    QFileInfo fileInfo(filePath);
    m_statusLabel->setText(tr("当前文件: %1").arg(fileInfo.fileName()));
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QMessageBox::warning(this, tr("错误"), tr("无法打开文件: %1").arg(filePath));
        return;
    }
    
    QTextStream in(&file);
    in.setCodec("UTF-8");
    QString content = in.readAll();
    file.close();
    
    qDebug() << "读取到的文件内容长度:" << content.length();
    if (content.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("文件内容为空!"));
        return;
    }
    
    if (content.length() > 1000000) { // 1MB文本
        if (QMessageBox::question(this, tr("大文件提示"), 
            tr("文件较大 (%1 字节)，加载可能需要一些时间。是否继续?").arg(content.length()),
            QMessageBox::Yes | QMessageBox::No) == QMessageBox::No) {
            return;
        }
    }
    
    m_logDisplay->clear();
    
    m_logDisplay->setUpdatesEnabled(false);
    
    m_logDisplay->setText(content);
    
    m_logDisplay->setUpdatesEnabled(true);
    
    m_logDisplay->update();
    
    m_logDisplay->verticalScrollBar()->setValue(0);
    
    qDebug() << "文本设置后的长度:" << m_logDisplay->toPlainText().length();
}

void LogAnalyzer::clearLog()
{
    m_logDisplay->clear();
    m_statusLabel->setText(tr("请选择日志文件进行分析"));
} 