@echo off
setlocal EnableDelayedExpansion

echo 开始处理用户日志文件...

:: 处理用户日志文件
for %%T in (user.log) do (
    echo 处理 %%T 文件...
    
    :: 删除如果已存在的合并文件
    if exist ".\%%T.last.amalgamation.log" del /q ".\%%T.last.amalgamation.log"

    
    :: 添加当前的日志文件
    if exist "termdisk\data0\var-bak\log\userlog\%%T" (
        echo 添加 data0\var-bak 目录下的 %%T...
        type "termdisk\data0\var-bak\log\userlog\%%T" >> ".\%%T.last.amalgamation.log"
    )
    
    :: 添加var目录下的日志文件
    if exist "termdisk\var\log\userlog\%%T" (
        echo 添加 var\log 目录下的 %%T...
        type "termdisk\var\log\userlog\%%T" >> ".\%%T.last.amalgamation.log"
    )
    
    :: 添加tmp目录下的wsBase日志文件
    if exist "termdisk\tmp\wsBase\userlog\%%T" (
        echo 添加 tmp\wsBase 目录下的 %%T...
        type "termdisk\tmp\wsBase\userlog\%%T" >> ".\%%T.last.amalgamation.log"
    )
    
    :: 添加tmp目录下的log日志文件
    if exist "termdisk\tmp\log\userlog\%%T" (
        echo 添加 tmp\log 目录下的 %%T...
        type "termdisk\tmp\log\userlog\%%T" >> ".\%%T.last.amalgamation.log"
    )
)

:: 创建logs目录（如果不存在）
if not exist ".\logs" mkdir ".\logs"

:: 将合并后的日志文件复制到logs目录
echo 将合并后的日志文件复制到logs目录...
for %%F in (*.amalgamation.log) do (
    if exist "%%F" (
        echo 复制 %%F 到 logs 目录...
        copy /Y "%%F" ".\logs\%%F" > nul
    )
)

echo 用户日志合并处理完成！ 