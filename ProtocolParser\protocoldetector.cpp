#include "protocoldetector.h"
#include <QDebug>

ProtocolDetector::ProtocolDetector(QObject *parent)
    : QObject(parent)
    , m_minFrameLength(8)
    , m_maxFrameLength(1024)
{
    // 默认启用所有协议检测
    m_enabledProtocols = {
        ProtocolType::DLT645_2007,
        ProtocolType::DLT645_1997,
        ProtocolType::NW_UP
    };
}

ProtocolDetector::~ProtocolDetector()
{
}

FrameParseResult ProtocolDetector::detectProtocol(const QByteArray &data)
{
    if (data.isEmpty()) {
        return createErrorResult("输入数据为空");
    }

    if (data.size() < m_minFrameLength) {
        return createErrorResult(QString("数据长度(%1)小于最小帧长度(%2)")
                                .arg(data.size()).arg(m_minFrameLength));
    }

    if (data.size() > m_maxFrameLength) {
        return createErrorResult(QString("数据长度(%1)超过最大帧长度(%2)")
                                .arg(data.size()).arg(m_maxFrameLength));
    }

    // 按优先级顺序检测协议
    FrameParseResult result;

    // 1. 检测DL/T645-2007协议
    if (m_enabledProtocols.contains(ProtocolType::DLT645_2007)) {
        result = detectDLT645_2007(data);
        if (result.isValid) {
            return result;
        }
    }

    // 2. 检测南网上行协议
    if (m_enabledProtocols.contains(ProtocolType::NW_UP)) {
        result = detectNWUP(data);
        if (result.isValid) {
            return result;
        }
    }

    // 3. 检测DL/T645-1997协议
    if (m_enabledProtocols.contains(ProtocolType::DLT645_1997)) {
        result = detectDLT645_1997(data);
        if (result.isValid) {
            return result;
        }
    }

    return createErrorResult("未识别出有效的协议类型");
}

FrameParseResult ProtocolDetector::detectDLT645_2007(const QByteArray &data)
{
    int frameStart, frameEnd;
    
    if (!findDLT645Frame(data, frameStart, frameEnd)) {
        return createErrorResult("未找到有效的DL/T645帧结构");
    }

    // 提取帧数据
    QByteArray frameData = data.mid(frameStart, frameEnd - frameStart + 1);
    
    // 验证帧长度（最小长度：68H + 6字节地址 + 68H + 控制码 + 长度 + 校验码 + 16H = 12字节）
    if (frameData.size() < 12) {
        return createErrorResult("DL/T645帧长度不足");
    }

    // 验证帧起始和结束标志
    if (static_cast<quint8>(frameData.at(0)) != 0x68 || 
        static_cast<quint8>(frameData.at(frameData.size() - 1)) != 0x16) {
        return createErrorResult("DL/T645帧起始或结束标志错误");
    }

    // 验证第二个68H
    if (frameData.size() >= 8 && static_cast<quint8>(frameData.at(7)) != 0x68) {
        return createErrorResult("DL/T645帧第二个68H标志错误");
    }

    // 验证校验码
    if (!verifyDLT645Checksum(frameData)) {
        return createErrorResult("DL/T645帧校验码错误");
    }

    return createSuccessResult(ProtocolType::DLT645_2007, data, frameStart, frameEnd);
}

FrameParseResult ProtocolDetector::detectDLT645_1997(const QByteArray &data)
{
    // DL/T645-1997与2007版本的帧结构基本相同，但数据标识格式不同
    // 这里先使用相同的帧检测逻辑，后续可以根据数据标识进一步区分
    int frameStart, frameEnd;
    
    if (!findDLT645Frame(data, frameStart, frameEnd)) {
        return createErrorResult("未找到有效的DL/T645帧结构");
    }

    QByteArray frameData = data.mid(frameStart, frameEnd - frameStart + 1);
    
    if (frameData.size() < 12) {
        return createErrorResult("DL/T645帧长度不足");
    }

    if (!verifyDLT645Checksum(frameData)) {
        return createErrorResult("DL/T645帧校验码错误");
    }

    // TODO: 添加1997版本特有的识别逻辑
    // 目前暂时返回未识别，优先识别为2007版本
    return createErrorResult("DL/T645-1997协议识别逻辑待实现");
}



void ProtocolDetector::setMinFrameLength(int minLength)
{
    m_minFrameLength = minLength;
}

void ProtocolDetector::setMaxFrameLength(int maxLength)
{
    m_maxFrameLength = maxLength;
}

void ProtocolDetector::setProtocolEnabled(ProtocolType protocolType, bool enabled)
{
    if (enabled) {
        if (!m_enabledProtocols.contains(protocolType)) {
            m_enabledProtocols.append(protocolType);
        }
    } else {
        m_enabledProtocols.removeAll(protocolType);
    }
}

bool ProtocolDetector::findDLT645Frame(const QByteArray &data, int &frameStart, int &frameEnd)
{
    frameStart = -1;
    frameEnd = -1;

    // 查找帧起始标志68H
    for (int i = 0; i < data.size(); ++i) {
        if (static_cast<quint8>(data.at(i)) == 0x68) {
            // 检查是否有足够的字节来构成最小帧
            if (i + 11 < data.size()) {
                // 检查第二个68H（位置i+7）
                if (static_cast<quint8>(data.at(i + 7)) == 0x68) {
                    // 获取数据长度
                    quint8 dataLength = static_cast<quint8>(data.at(i + 9));
                    
                    // 计算帧结束位置
                    int expectedEnd = i + 10 + dataLength + 1; // +1为校验码，+1为16H
                    
                    if (expectedEnd < data.size()) {
                        // 检查结束标志16H
                        if (static_cast<quint8>(data.at(expectedEnd)) == 0x16) {
                            frameStart = i;
                            frameEnd = expectedEnd;
                            return true;
                        }
                    }
                }
            }
        }
    }

    return false;
}

bool ProtocolDetector::verifyDLT645Checksum(const QByteArray &frameData)
{
    if (frameData.size() < 3) {
        return false;
    }

    // 校验码是倒数第二个字节
    quint8 receivedChecksum = static_cast<quint8>(frameData.at(frameData.size() - 2));
    
    // 计算校验码（从第一个68H到校验码前的所有字节）
    QByteArray dataForChecksum = frameData.left(frameData.size() - 2);
    quint8 calculatedChecksum = calculateDLT645Checksum(dataForChecksum);
    
    return receivedChecksum == calculatedChecksum;
}

quint8 ProtocolDetector::calculateDLT645Checksum(const QByteArray &data)
{
    quint8 checksum = 0;
    for (int i = 0; i < data.size(); ++i) {
        checksum += static_cast<quint8>(data.at(i));
    }
    return checksum;
}

FrameParseResult ProtocolDetector::detectNWUP(const QByteArray &data)
{
    int frameStart, frameEnd;
    
    if (!findNWUPFrame(data, frameStart, frameEnd)) {
        return createErrorResult("未找到有效的南网上行协议帧");
    }

    QByteArray frameData = data.mid(frameStart, frameEnd - frameStart + 1);
    
    if (!verifyNWUPChecksum(frameData)) {
        return createErrorResult("南网上行协议帧校验码错误");
    }

    return createSuccessResult(ProtocolType::NW_UP, data, frameStart, frameEnd);
}

bool ProtocolDetector::findNWUPFrame(const QByteArray &data, int &frameStart, int &frameEnd)
{
    frameStart = -1;
    frameEnd = -1;

    qDebug() << "[NWUP检测] 开始检测南网上行协议，数据长度:" << data.size() << "字节";
    qDebug() << "[NWUP检测] 数据内容:" << data.toHex(' ').toUpper();

    // 查找起始标志 68H
    for (int i = 0; i <= data.size() - 12; ++i) {
        if (static_cast<quint8>(data.at(i)) == 0x68) {
            qDebug() << "[NWUP检测] 在位置" << i << "找到起始字符68H";

            // 检查是否有足够的数据来验证帧头
            if (i + 5 >= data.size()) {
                qDebug() << "[NWUP检测] 数据不足，无法验证帧头";
                continue;
            }

            // 检查长度域是否一致（两个字节）
            quint8 length1 = static_cast<quint8>(data.at(i + 1));
            quint8 length2 = static_cast<quint8>(data.at(i + 2));
            qDebug() << "[NWUP检测] 长度域: L1=" << QString("0x%1").arg(length1, 2, 16, QChar('0')).toUpper()
                     << "L2=" << QString("0x%1").arg(length2, 2, 16, QChar('0')).toUpper();

            if (length1 != length2) {
                qDebug() << "[NWUP检测] 长度域不一致，跳过";
                continue;
            }

            // 检查第三个起始标志
            if (static_cast<quint8>(data.at(i + 3)) != 0x68) {
                qDebug() << "[NWUP检测] 第二个起始字符不是68H，跳过";
                continue;
            }

            // 计算帧的预期长度
            // 帧格式：68H + L + L + 68H + 用户数据(L字节) + 校验和 + 16H
            // 固定头部：68H + L + L + 68H = 4字节
            // 用户数据：length1字节（包含控制域+地址域+应用层数据）
            // 校验和：1字节
            // 结束符：1字节
            int expectedLength = 4 + length1 + 1 + 1; // length1 + 6
            qDebug() << "[NWUP检测] 用户数据长度:" << length1 << "字节，预期总长度:" << expectedLength << "字节";

            if (i + expectedLength > data.size()) {
                qDebug() << "[NWUP检测] 数据长度不足，需要" << expectedLength << "字节，实际只有" << (data.size() - i) << "字节";
                continue;
            }

            // 检查结束标志 16H
            quint8 endChar = static_cast<quint8>(data.at(i + expectedLength - 1));
            qDebug() << "[NWUP检测] 结束字符:" << QString("0x%1").arg(endChar, 2, 16, QChar('0')).toUpper();

            if (endChar != 0x16) {
                qDebug() << "[NWUP检测] 结束字符不是16H，跳过";
                continue;
            }

            qDebug() << "[NWUP检测] 找到有效的南网上行协议帧，起始位置:" << i << "结束位置:" << (i + expectedLength - 1);
            frameStart = i;
            frameEnd = i + expectedLength - 1;
            return true;
        }
    }

    qDebug() << "[NWUP检测] 未找到有效的南网上行协议帧";
    return false;
}

bool ProtocolDetector::verifyNWUPChecksum(const QByteArray &frameData)
{
    if (frameData.size() < 14) { // 最小帧长度：4字节头部 + 1字节控制域 + 7字节地址域 + 1字节校验和 + 1字节结束符
        return false;
    }

    // 校验和覆盖从控制域开始到数据内容结束的所有字节
    // 帧格式：68H + L + L + 68H + 控制域 + 地址域 + 应用层数据 + 校验和 + 16H
    // 校验和计算范围：从控制域（第5个字节）到数据内容结束（倒数第3个字节）
    QByteArray dataToCheck = frameData.mid(4, frameData.size() - 6); // 从第5个字节到倒数第2个字节
    quint8 calculatedCS = calculateNWUPChecksum(dataToCheck);
    quint8 frameCS = static_cast<quint8>(frameData.at(frameData.size() - 2));

    return calculatedCS == frameCS;
}

quint8 ProtocolDetector::calculateNWUPChecksum(const QByteArray &data)
{
    quint8 checksum = 0;
    for (int i = 0; i < data.size(); ++i) {
        checksum += static_cast<quint8>(data.at(i));
    }
    return checksum;
}



FrameParseResult ProtocolDetector::createErrorResult(const QString &errorMessage)
{
    FrameParseResult result;
    result.isValid = false;
    result.errorMessage = errorMessage;
    return result;
}

FrameParseResult ProtocolDetector::createSuccessResult(ProtocolType protocolType, 
                                                     const QByteArray &data,
                                                     int frameStart, 
                                                     int frameEnd)
{
    FrameParseResult result;
    result.isValid = true;
    result.protocolType = protocolType;
    result.rawData = data;
    result.frameData = data.mid(frameStart, frameEnd - frameStart + 1);
    result.frameStart = frameStart;
    result.frameEnd = frameEnd;
    result.frameLength = frameEnd - frameStart + 1;
    return result;
}
