import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import QtQuick.Dialogs 1.3
import Qt.labs.platform 1.1 as Labs
import QtGraphicalEffects 1.0
import com.nw.databasetool 1.0

Item {
    id: databaseToolView
    anchors.fill: parent
    
    // 背景色
    Rectangle {
        anchors.fill: parent
        color: "#ffffff"
    }
    
    // 属性定义
    property string termDiskPath: ""
    property bool isConnected: false
    property var selectedTable: null
    property var tableData: []
    property bool isConnecting: false
    property int currentRowIndex: -1 // 当前选中的行索引
    property bool tableNeedsUpdate: false // 控制表格是否需要更新
    property bool isInitialized: false // 标记是否已初始化
    property var dbToolCpp: null // 接收从main.qml传递的dbToolCpp对象
    property var originalTablesList: [] // 保存原始表列表用于过滤
    property bool hasManuallyAdjustedColumns: false // Track if user has manually adjusted column widths
    property var sqlScriptsList: [] // 存储SQL脚本文件列表
    property int currentPage: 0 // 当前页码，从0开始
    property int pageSize: 1000 // 每页显示的记录数
    property int totalRecords: 0 // 总记录数
    property string currentQuery: "" // 当前查询语句
    property bool parseRulesLoaded: false // 标记解析规则是否已加载
    property bool lazyInitComplete: false // 延迟初始化标记
    property var _descriptionCache: ({}) // 缓存表解析规则描述
    
    // 错误对话框
    MessageDialog {
        id: errorDialog
        title: "错误"
        standardButtons: StandardButton.Ok
        text: ""
        
        property string errorMessage: ""
        
        onTextChanged: {
            text = errorMessage;
        }
        
        onAccepted: {
            errorDialog.close();
        }
    }
    
    // 文件选择对话框
    Labs.FileDialog {
        id: fileDialog
        title: "选择数据库文件"
        nameFilters: ["SQLite数据库文件 (*.db *.db3 *.sqlite *.sqlite3)", "所有文件 (*)"]
        
        // 设置初始文件夹
        folder: {
            if (termDiskPath && termDiskPath.length > 0) {
                // 如果有termDiskPath，使用它
                return "file:///" + termDiskPath+"/data0";
            } else {
                // 否则使用桌面目录
                return Labs.StandardPaths.writableLocation(Labs.StandardPaths.DesktopLocation);
            }
        }
        
        onAccepted: {
            var path = fileDialog.file.toString();
            // 移除 file:/// 前缀
            path = path.replace(/^(file:\/{2,3})/, "");
            // 处理 Windows 路径
            if (Qt.platform.os === "windows") {
                path = decodeURIComponent(path);
            }
            console.log("选择的数据库文件路径:", path);
            
            // 如果已经连接到数据库，先断开连接
            if (isConnected && dbToolCpp) {
                console.log("断开当前数据库连接");
                dbToolCpp.disconnectFromDatabase();
                isConnected = false;
                selectedTable = null;
                tableData = [];
                
                // 清空表格数据
                tablesListModel.clearTables();
                headerModel.clear();
                dataModel.clear();
            }
            
            // 设置数据库路径并连接
            if (dbToolCpp) {
                dbToolCpp.setDatabasePath(path);
                connectToDatabase();
            } else {
                showError("数据库工具对象未初始化");
            }
        }
    }
    
    // 导出文件对话框
    Labs.FileDialog {
        id: exportFileDialog
        title: "导出数据"
        fileMode: Labs.FileDialog.SaveFile
        nameFilters: ["CSV文件 (*.csv)", "Excel文件 (*.xlsx)", "所有文件 (*)"]
        
        // 添加导出模式属性
        property string exportMode: "page" // "page" 或 "table"
        
        // 在Qt.labs.platform.FileDialog中设置默认文件名
        // 注意：在不同Qt版本中，设置默认文件名的方法可能有所不同
        property string defaultFileName: ""
        
        onDefaultFileNameChanged: {
            if (defaultFileName) {
                // 尝试各种可能的属性名
                if (typeof currentFile !== 'undefined') {
                    currentFile = defaultFileName;
                }
                
                if (typeof file !== 'undefined') {
                    file = defaultFileName;
                }
                
                // 对于Qt 5.12，可能需要设置currentFile的URL形式
                var urlPrefix = "file:///";
                if (Qt.platform.os === "windows") {
                    // Windows路径需要特殊处理
                    var winPath = defaultFileName.replace(/\//g, "\\");
                    if (!winPath.match(/^[A-Za-z]:\\/)) {
                        // 如果不是绝对路径，添加当前目录
                        winPath = "C:\\Users\\<USER>\\Documents\\" + winPath;
                    }
                    currentFile = urlPrefix + winPath;
                } else {
                    // Linux/Mac路径
                    if (!defaultFileName.startsWith("/")) {
                        // 如果不是绝对路径，添加当前目录
                        currentFile = urlPrefix + "/home/" + defaultFileName;
                    } else {
                        currentFile = urlPrefix + defaultFileName;
                    }
                }
                
                console.log("设置文件对话框默认文件名:", defaultFileName, "URL形式:", currentFile);
            }
        }
        
        onAccepted: {
            var path = exportFileDialog.file.toString();
            // 移除 file:/// 前缀
            path = path.replace(/^(file:\/{2,3})/, "");
            // 处理 Windows 路径
            if (Qt.platform.os === "windows") {
                path = decodeURIComponent(path);
            }
            console.log("导出文件路径:", path);
            exportTableData(path, exportMode);
        }
    }
    
    // 连接到数据库
    function connectToDatabase() {
        try {
            // 防止重复连接
            if (isConnecting) {
                console.log("已经在连接中，忽略此次请求");
                return;
            }
            
            // 如果已连接，不要重复连接
            if (isConnected) {
                console.log("数据库已连接，无需重复连接");
                return;
            }
            
            isConnecting = true;
            
            // 检查dbToolCpp对象是否存在
            if (!dbToolCpp) {
                console.error("dbToolCpp属性未设置");
                showError("数据库工具对象未初始化，请重启应用");
                isConnecting = false;
                return;
            }
            
            // 连接数据库
            console.log("尝试调用C++连接数据库函数...");
            var success = false;
            
            try {
                // 验证函数存在性
                if (typeof dbToolCpp.connectToDatabase !== "function") {
                    console.error("dbToolCpp.connectToDatabase函数不存在");
                    showError("数据库连接功能未实现");
                    isConnecting = false;
                    return;
                }
                
                // 调用连接方法
                success = dbToolCpp.connectToDatabase();
            } catch (e) {
                console.error("调用connectToDatabase函数出错:", e);
                showError("连接数据库时出错: " + e);
                isConnecting = false;
                return;
            }
            
            console.log("数据库连接结果:", success);
            isConnected = success;
            
            if (success) {
                console.log("数据库连接成功");
                // 获取表列表
                loadTablesList();
                
                // 扫描SQL脚本文件
                scanSqlScripts();
            } else {
                // 显示连接失败错误
                console.error("数据库连接失败");
                showError("无法连接到数据库");
            }
        } catch (e) {
            console.error("连接数据库时发生异常:", e);
            showError("连接数据库时发生异常: " + e);
        } finally {
            isConnecting = false;
        }
    }
    
    // 显示错误信息
    function showError(message) {
        console.error(message);
        errorDialog.errorMessage = message;
        errorDialog.open();
    }
    
    // 加载表列表
    function loadTablesList() {
        if (!isConnected) {
            console.warn("尝试加载表列表，但数据库未连接");
            return;
        }
        
        try {
            console.log("开始获取表列表...");
            var tables = dbToolCpp.getTablesList();
            
            // 输出更多调试信息
            console.log("tables类型:", typeof tables);
            if (typeof tables === 'object') {
                console.log("tables是否有length属性:", tables.hasOwnProperty('length'));
                console.log("tables.length:", tables.length);
                console.log("tables.constructor.name:", tables.constructor ? tables.constructor.name : "unknown");
            }
            
            // 清空表列表
            tablesListModel.clearTables();
            
            // 根据不同的返回类型处理表列表
            if (typeof tables === 'object' && tables !== null) {
                var tableCount = 0;
                // 检查是否是QStringList转换的对象，这种对象有length属性但不是数组
                if (tables.length !== undefined) {
                    // 将类数组对象转为JavaScript数组
                    originalTablesList = [];
                    for (var i = 0; i < tables.length; i++) {
                        var tableName = tables[i];
                        if (tableName && typeof tableName === 'string' && tableName.length > 0) {
                            originalTablesList.push(tableName);
                            tableCount++;
                        }
                    }

                    // 对表名进行排序
                    originalTablesList = sortTableNames(originalTablesList);

                    // 添加排序后的表到模型
                    for (var j = 0; j < originalTablesList.length; j++) {
                        tablesListModel.addTable(originalTablesList[j]);
                    }
                }
                
                console.log("成功加载了", tableCount, "个表");
            } else {
                console.warn("无法识别的表列表类型:", typeof tables);
            }
            
            // 清空过滤输入框
            if (tableFilterInput) {
                tableFilterInput.text = "";
            }
        } catch (e) {
            console.error("加载表列表时发生异常:", e);
            showError("加载表列表时发生异常: " + e);
        }
    }
    
    // 加载表数据
    function loadTableData(tableName) {
        if (!isConnected) {
            console.warn("尝试加载表数据，但数据库未连接");
            return;
        }
        
        try {
            console.log("加载表数据:", tableName);
            selectedTable = tableName;
            
            // 重置分页
            currentPage = 0;
            
            // 生成并显示SQL查询语句，不带LIMIT
            var sqlQuery = "SELECT * FROM " + tableName + ";";
            currentQuery = "SELECT * FROM " + tableName; // 保存查询
            
            // 使用延迟调用确保sqlQueryTextArea已经初始化
            Qt.callLater(function() {
                if (sqlQueryTextArea && typeof sqlQueryTextArea.setTextWithoutExecution === "function") {
                    sqlQueryTextArea.setTextWithoutExecution(sqlQuery);
                }
            });
            
            // 获取表数据 - 不限制数量
            tableData = dbToolCpp.getTableData(tableName);
            console.log("获取到表数据，行数:", tableData.length);
            
            // 检查是否有错误
            if (tableData.length > 0 && tableData[0].error) {
                console.error("加载表数据失败: " + tableData[0].message);
                return;
            }
            
            // 设置总记录数
            totalRecords = tableData.length > 0 ? tableData.length - 1 : 0; // 减去表头行
            console.log("表总记录数:", totalRecords);
            
            // 打印表头信息进行调试
            if (tableData.length > 0) {
                var headerRow = tableData[0];
                console.log("表头行:", JSON.stringify(headerRow));
                
                // 输出所有列名
                var columnNames = [];
                for (var i = 1; i <= 20; i++) {
                    var colKey = "col" + i;
                    if (colKey in headerRow) {
                        columnNames.push(headerRow[colKey]);
                    } else {
                        break;
                    }
                }
                console.log("列名:", columnNames.join(", "));
            }
            
            // 更新表头模型
            updateHeaderModel();
            
            // 更新数据模型 - 只显示当前页的数据
            updateDataModel();
            
            // 强制更新布局
            Qt.callLater(updateLayout);
            
            // 检查是否需要显示分页控件
            console.log("是否应显示分页控件:", totalRecords > 1000, "总记录数:", totalRecords);
            
            // 处理解析规则 - 优化为单次调用
            handleParseRules(tableName);
        } catch (e) {
            console.error("加载表数据时发生异常:", e);
            showError("加载表数据时发生异常: " + e);
        }
    }
    
    // 新增函数：处理解析规则的加载和应用
    function handleParseRules(tableName) {
        console.log("处理表", tableName, "的解析规则，当前状态:", parseRulesLoaded ? "已加载" : "未加载");
        
        // 如果解析规则尚未加载，先加载规则
        if (!parseRulesLoaded && dbToolCpp && typeof dbToolCpp.loadAllParseRules === "function") {
            console.log("解析规则未加载，执行加载...");
            var success = dbToolCpp.loadAllParseRules();
            parseRulesLoaded = success;
            
            if (success) {
                console.log("解析规则加载成功，应用到当前表");
                applyParseRuleForTable(tableName);
            } else {
                console.log("解析规则加载失败");
            }
        } else if (parseRulesLoaded) {
            // 解析规则已加载，直接应用
            console.log("解析规则已加载，直接应用到当前表");
            applyParseRuleForTable(tableName);
        }
    }
    
    // 更新表头模型
    function updateHeaderModel() {
        if (tableData.length === 0) return;
        
        try {
            var headerRow = tableData[0];
            headerModel.clear();
            
            // 检查是否为表头行
            if (headerRow.isHeader) {
                console.log("处理表头行");
                // 确保按正确顺序添加列
                var columnKeys = [];
                for (var i = 1; i <= 100; i++) {
                    var colKey = "col" + i;
                    if (colKey in headerRow) {
                        columnKeys.push(colKey);
                    } else {
                        break;
                    }
                }
                
                // 按顺序添加列到表头模型
                for (var j = 0; j < columnKeys.length; j++) {
                    var key = columnKeys[j];
                    var columnName = headerRow[key];
                    console.log("添加列:", j+1, columnName);
                    headerModel.append({
                        columnName: columnName,
                        columnIndex: j
                    });
                }
                
                // 添加固定的Parse列
                headerModel.append({
                    columnName: "Parse",
                    columnIndex: columnKeys.length
                });
                
                console.log("表头列数:", headerModel.count);
            } else {
                console.warn("首行不是表头行");
            }
        } catch (e) {
            console.error("更新表头模型时发生异常:", e);
            showError("更新表头模型时发生异常: " + e);
        }
    }
    
    // 更新数据模型
    function updateDataModel() {
        if (tableData.length <= 1) {
            console.warn("没有数据行可显示");
            return;
        }
        
        try {
            dataModel.clear();
            
            // 获取表头行
            var headerRow = tableData[0];
            console.log("表头行包含的列数:", Object.keys(headerRow).filter(key => key.startsWith("col")).length);
            
            // 计算当前页的起始和结束索引
            var startIndex = currentPage * pageSize + 1; // +1 是因为跳过表头行
            var endIndex = Math.min(startIndex + pageSize, tableData.length);
            
            console.log("显示数据范围:", startIndex, "至", endIndex, "共", (endIndex - startIndex), "行");
            console.log("总记录数:", totalRecords, "当前页:", currentPage + 1, "总页数:", Math.ceil(totalRecords / pageSize));
            console.log("是否应显示分页控件:", totalRecords > 1000);
            
            // 只添加当前页的数据
            for (var i = startIndex; i < endIndex; i++) {
                var row = tableData[i];
                var rowData = {};
                
                // 调试输出第一行数据的所有键
                if (i === startIndex) {
                    console.log("第一行数据的所有键:", Object.keys(row).join(", "));
                    console.log("第一行数据原始内容:", JSON.stringify(row));
                }
                
                // 确保为每一列都添加数据，即使原始数据中没有
                for (var j = 1; j <= headerModel.count - 1; j++) { // 减1是因为最后一列是Parse列
                    var colKey = "col" + j;
                    
                    if (colKey in row) {
                        // 保留原始值类型，不进行字符串转换
                        rowData[colKey] = row[colKey];
                    } else {
                        rowData[colKey] = ""; // 确保每列都有值，即使是空值
                    }
                }
                
                // 添加Parse列的空数据
                rowData["colParse"] = "";
                
                dataModel.append(rowData);
            }
            
            console.log("数据模型更新完成，行数:", dataModel.count);
            
            // 强制更新表格布局
            tableNeedsUpdate = !tableNeedsUpdate;
            Qt.callLater(updateLayout);
            
            // 更新数据模型后，如果有解析规则，自动应用
            // 但不再在这里自动应用，而是依赖于applyParseRuleForTable中的应用
        } catch (e) {
            console.error("更新数据模型时发生异常:", e);
            showError("更新数据模型时发生异常: " + e);
        }
    }
    
    // 辅助函数：将对象转换为十六进制字符串
    function objectToHexString(obj) {
        if (!obj) return "";
        
        try {
            // 如果是字符串，直接返回
            if (typeof obj === 'string') {
                return obj;
            }
            
            // 如果是数组缓冲区或类似二进制数据
            if (obj instanceof ArrayBuffer || 
                (typeof obj === 'object' && obj.buffer instanceof ArrayBuffer)) {
                // 转换为十六进制
                var hex = "";
                var view = new Uint8Array(obj);
                for (var i = 0; i < view.length; i++) {
                    var val = view[i].toString(16);
                    hex += (val.length === 1 ? "0" + val : val);
                }
                return hex.toUpperCase();
            }
            
            // 如果是空对象
            if (typeof obj === 'object' && Object.keys(obj).length === 0) {
                return "";
            }
            
            // 如果以上方法都失败，尝试JSON序列化
            var jsonStr = JSON.stringify(obj);
            if (jsonStr !== "{}" && jsonStr !== "[]") {
                return jsonStr;
            }
            
            return "";
        } catch (e) {
            console.error("转换对象到十六进制字符串时出错:", e);
            return "[转换错误]";
        }
    }
    
    // 排序表名函数 - 按照tb_后面的无符号16进制格式排序
    function sortTableNames(tableNames) {
        return tableNames.sort(function(a, b) {
            // 提取tb_后面的16进制数字部分
            var getHexNumber = function(tableName) {
                var match = tableName.match(/^tb_([0-9a-fA-F]+)$/);
                if (match) {
                    // 解析为无符号32位整数
                    var hexStr = match[1];
                    var num = parseInt(hexStr, 16);

                    // 保持为无符号数（0 到 4294967295）
                    return {
                        isHex: true,
                        value: num
                    };
                } else {
                    // 不是16进制格式
                    return {
                        isHex: false,
                        value: 0,
                        originalName: tableName
                    };
                }
            };

            var resultA = getHexNumber(a);
            var resultB = getHexNumber(b);

            // 非16进制的排在前面
            if (!resultA.isHex && !resultB.isHex) {
                // 两个都不是16进制，按字符串排序
                return a.localeCompare(b);
            } else if (!resultA.isHex) {
                // A不是16进制，排在前面
                return -1;
            } else if (!resultB.isHex) {
                // B不是16进制，排在前面
                return 1;
            } else {
                // 两个都是16进制，按无符号数值排序
                return resultA.value - resultB.value;
            }
        });
    }

    // 过滤表列表
    function filterTablesList() {
        var filterText = tableFilterInput.text.toLowerCase();
        
        // 清空当前表列表
        tablesListModel.clearTables();
        
        // 检查原始表列表是否存在
        if (!originalTablesList || originalTablesList.length === 0) {
            console.log("没有原始表列表可供过滤");
            return;
        }
        
        // 如果过滤文本为空，显示所有表（已经排序）
        if (filterText.length === 0) {
            for (var i = 0; i < originalTablesList.length; i++) {
                tablesListModel.addTable(originalTablesList[i]);
            }
            return;
        }

        // 应用过滤条件，收集匹配的表名
        var matchedTables = [];
        for (var i = 0; i < originalTablesList.length; i++) {
            var tableName = originalTablesList[i];
            if (tableName.toLowerCase().indexOf(filterText) !== -1) {
                matchedTables.push(tableName);
            }
        }

        // 对匹配的表名进行排序
        matchedTables = sortTableNames(matchedTables);

        // 添加排序后的匹配表到模型
        for (var j = 0; j < matchedTables.length; j++) {
            tablesListModel.addTable(matchedTables[j]);
        }

        var matchCount = matchedTables.length;
        
        console.log("过滤后匹配的表数量:", matchCount);
    }
    
    // 导出表数据函数
    function exportTableData(filePath, mode) {
        if (!selectedTable || !isConnected) {
            showError("请先选择一个表");
            return;
        }
        
        try {
            console.log("导出表数据到:", filePath, "模式:", mode);
            
            // 检查文件扩展名
            var isCSV = filePath.toLowerCase().endsWith(".csv");
            var isExcel = filePath.toLowerCase().endsWith(".xlsx");
            
            if (!isCSV && !isExcel) {
                // 如果没有扩展名，默认添加.csv
                if (!filePath.includes(".")) {
                    filePath += ".csv";
                    isCSV = true;
                } else {
                    showError("不支持的文件格式，请使用.csv或.xlsx");
                    return;
                }
            }
            
            if (mode === "page") {
                // 导出当前页数据（包括Parse列）
                exportCurrentPageData(filePath, isCSV);
            } else if (mode === "table") {
                // 调用C++方法导出整个表
                if (dbToolCpp && typeof dbToolCpp.exportTableData === 'function') {
                    // 修改C++导出函数，传递当前的解析规则
                    var parseRule = batchParseRuleInput ? batchParseRuleInput.text : "";
                    var success = dbToolCpp.exportTableData(selectedTable, filePath, parseRule);
                    if (success) {
                        console.log("数据导出成功");
                    } else {
                        showError("导出失败");
                    }
                } else {
                    showError("导出功能未实现");
                }
            }
        } catch (e) {
            console.error("导出数据时发生错误:", e);
            showError("导出数据时发生错误: " + e);
        }
    }

    // 导出当前页数据
    function exportCurrentPageData(filePath, isCSV) {
        try {
            // 检查是否有数据
            if (dataModel.count === 0 || headerModel.count === 0) {
                showError("没有数据可导出");
                return;
            }
            
            console.log("开始导出当前页数据，行数:", dataModel.count, "列数:", headerModel.count);
            
            // 收集表头
            var headers = [];
            // 添加序号列
            headers.push("序号");
            
            // 添加所有列名
            for (var i = 0; i < headerModel.count; i++) {
                var columnName = headerModel.get(i).columnName;
                
                // 对于Parse列，如果有应用解析规则，修改表头为"Parse(规则描述)"
                if (columnName === "Parse" && batchParseRuleInput && batchParseRuleInput.text.trim() !== "") {
                    // 获取当前表的解析规则描述
                    var description = "";
                    if (selectedTable && dbToolCpp && typeof dbToolCpp.getDescriptionForTable === "function") {
                        description = dbToolCpp.getDescriptionForTable(selectedTable);
                    }
                    
                    // 如果有描述，添加到表头
                    if (description && description.length > 0) {
                        headers.push("Parse(" + description + ")");
                    } else {
                        // 如果没有描述，使用规则本身
                        headers.push("Parse(" + batchParseRuleInput.text.trim() + ")");
                    }
                } else {
                    headers.push(columnName);
                }
            }
            
            // 创建QML可以传递给C++的数据结构
            var rows = [];
            
            // 添加表头行
            var headerRow = {};
            headerRow["isHeader"] = true;
            for (var i = 0; i < headers.length; i++) {
                headerRow["col" + (i+1)] = headers[i];
            }
            rows.push(headerRow);
            
            console.log("表头行:", JSON.stringify(headerRow));
            
            // 添加数据行
            for (var rowIndex = 0; rowIndex < dataModel.count; rowIndex++) {
                var rowObj = dataModel.get(rowIndex);
                if (!rowObj || typeof rowObj !== 'object') continue;
                
                var exportRow = {};
                
                // 添加序号
                exportRow["col1"] = (rowIndex + 1 + (currentPage * pageSize)).toString();
                
                // 添加每列数据
                for (var colIndex = 0; colIndex < headerModel.count; colIndex++) {
                    var colKey = "col" + (colIndex + 1);
                    var exportKey = "col" + (colIndex + 2); // +2 因为第一列是序号
                    var columnName = headerModel.get(colIndex).columnName;
                    
                    // 对于Parse列特殊处理
                    if (columnName === "Parse") {
                        if ("colParse" in rowObj) {
                            var parseValue = rowObj["colParse"];
                            exportRow[exportKey] = parseValue ? parseValue.toString() : "";
                        } else {
                            exportRow[exportKey] = "";
                        }
                    } else {
                        // 普通列
                        if (colKey in rowObj) {
                            var value = rowObj[colKey];
                            
                            // 处理特殊类型
                            if (value === null || value === undefined) {
                                exportRow[exportKey] = "";
                            } else if (typeof value === 'object') {
                                exportRow[exportKey] = objectToHexString(value);
                            } else {
                                // 处理DataFlag、TypeId和DataId列，以16进制显示
                                if (columnName === "DataFlag" || columnName === "TypeId" || columnName === "DataId") {
                                    if (/^-?\d+(\.\d+)?$/.test(value.toString())) {
                                        var numValue = parseInt(value.toString());
                                        // 使用无符号32位整数表示（两个补码）
                                        var hexValue;
                                        if (numValue < 0) {
                                            // 对于负数，使用32位二进制补码
                                            // 0xFFFFFFFF + 1 = 2^32
                                            hexValue = (0x100000000 + numValue).toString(16).toLowerCase();
                                        } else {
                                            hexValue = numValue.toString(16).toLowerCase();
                                        }
                                        // 确保始终有8位
                                        hexValue = hexValue.padStart(8, '0');
                                        exportRow[exportKey] = "0x" + hexValue;
                                    } else {
                                        exportRow[exportKey] = value.toString();
                                    }
                                }
                                // 检查是否是DataTime或CollectionTime列
                                else if ((columnName === "DataTime" || columnName === "CollectionTime") && 
                                    /^-?\d+(\.\d+)?$/.test(value.toString())) {
                                    // 将Unix时间戳转换为UTC格式
                                    try {
                                        var timestamp = parseInt(value.toString());
                                        var date = new Date(timestamp * 1000); // Unix时间戳是秒，需要转换为毫秒
                                        
                                        // 检查日期是否有效
                                        if (!isNaN(date.getTime())) {
                                            // 显示UTC时间
                                            var year = date.getUTCFullYear();
                                            var month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                                            var day = date.getUTCDate().toString().padStart(2, '0');
                                            var hours = date.getUTCHours().toString().padStart(2, '0');
                                            var minutes = date.getUTCMinutes().toString().padStart(2, '0');
                                            var seconds = date.getUTCSeconds().toString().padStart(2, '0');
                                            exportRow[exportKey] = year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds + " UTC";
                                        } else {
                                            exportRow[exportKey] = value.toString();
                                        }
                                    } catch (e) {
                                        exportRow[exportKey] = value.toString();
                                    }
                                } else {
                                    exportRow[exportKey] = value.toString();
                                }
                            }
                        } else {
                            exportRow[exportKey] = "";
                        }
                    }
                }
                
                rows.push(exportRow);
                
                // 输出前几行数据用于调试
                if (rowIndex < 2) {
                    console.log("数据行 " + rowIndex + ":", JSON.stringify(exportRow));
                }
            }
            
            console.log("准备导出数据，总行数:", rows.length);
            
            // 调用C++方法导出数据
            if (dbToolCpp && typeof dbToolCpp.exportDataToFile === 'function') {
                var success = dbToolCpp.exportDataToFile(rows, filePath, isCSV);
                if (success) {
                    console.log("当前页数据导出成功");
                } else {
                    showError("导出当前页数据失败");
                }
            } else {
                // 如果C++方法不可用，尝试使用JavaScript实现CSV导出
                if (isCSV) {
                    exportToCSVJS(rows, filePath);
                } else {
                    showError("导出Excel需要C++支持，但该功能未实现");
                }
            }
        } catch (e) {
            console.error("导出当前页数据时发生错误:", e);
            showError("导出当前页数据时发生错误: " + e);
        }
    }
    
    // JavaScript实现的CSV导出（备用方案）
    function exportToCSVJS(rows, filePath) {
        try {
            console.log("使用JavaScript导出CSV，行数:", rows.length);
            var csvContent = "";
            
            // 获取所有可能的列键
            var allKeys = new Set();
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                for (var key in row) {
                    if (key !== "isHeader") {
                        allKeys.add(key);
                    }
                }
            }
            
            var columnKeys = Array.from(allKeys).sort();
            console.log("列键:", columnKeys.join(", "));
            
            // 处理每一行
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                var csvRow = [];
                
                // 如果是表头行，使用列名
                if (row.isHeader) {
                    for (var j = 0; j < columnKeys.length; j++) {
                        var colKey = columnKeys[j];
                        csvRow.push(row[colKey] || "");
                    }
                } else {
                    // 数据行
                    for (var j = 0; j < columnKeys.length; j++) {
                        var colKey = columnKeys[j];
                        var cellValue = row[colKey] || "";
                        
                        // 如果单元格包含逗号、引号或换行符，需要用引号包裹
                        if (typeof cellValue === 'string' && 
                            (cellValue.indexOf(',') !== -1 || 
                             cellValue.indexOf('"') !== -1 || 
                             cellValue.indexOf('\n') !== -1 || 
                             cellValue.indexOf('\r') !== -1)) {
                            // 将单元格中的引号替换为两个引号
                            cellValue = cellValue.replace(/"/g, '""');
                            // 用引号包裹
                            cellValue = '"' + cellValue + '"';
                        }
                        
                        csvRow.push(cellValue);
                    }
                }
                
                // 将行添加到CSV内容
                csvContent += csvRow.join(',') + '\n';
            }
            
            // 使用C++保存文本到文件
            if (dbToolCpp && typeof dbToolCpp.saveTextToFile === 'function') {
                var success = dbToolCpp.saveTextToFile(csvContent, filePath);
                if (success) {
                    console.log("CSV数据导出成功，内容长度:", csvContent.length);
                } else {
                    showError("导出CSV数据失败");
                }
            } else {
                showError("保存文件功能未实现");
            }
        } catch (e) {
            console.error("导出CSV时发生错误:", e);
            showError("导出CSV时发生错误: " + e);
        }
    }
    
    // 数据库未初始化对话框
    
    // 顶部工具栏 - 完全独立的组件
    Rectangle {
        id: topToolbar
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        height: 40
        color: "#f0f0f0"
        
        // 工具栏内容
        RowLayout {
            anchors.fill: parent
            anchors.leftMargin: 10
            anchors.rightMargin: 10
            spacing: 8
            
            // 打开按钮
            Rectangle {
                width: 80
                height: 30
                radius: 4
                color: openMouseArea.containsMouse ? "#e0e0e0" : "#f5f5f5"
                border.color: "#cccccc"
                
                RowLayout {
                    anchors.centerIn: parent
                    spacing: 5
                    
                    Text {
                        text: "打开"
                        font.pixelSize: 12
                        color: "#333333"
                    }
                }
                
                MouseArea {
                    id: openMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    cursorShape: Qt.PointingHandCursor
                    
                    onClicked: {
                        console.log("打开数据库文件");
                        fileDialog.open();
                    }
                }
            }
            
            // 导出按钮
            Rectangle {
                width: 80
                height: 30
                radius: 4
                color: exportMouseArea.containsMouse ? "#e0e0e0" : "#f5f5f5"
                border.color: "#cccccc"
                enabled: isConnected && selectedTable !== null
                opacity: enabled ? 1.0 : 0.5
                
                RowLayout {
                    anchors.centerIn: parent
                    spacing: 5
                    
                    Text {
                        text: "导出"
                        font.pixelSize: 12
                        color: "#333333"
                    }
                }
                
                MouseArea {
                    id: exportMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    cursorShape: parent.enabled ? Qt.PointingHandCursor : Qt.ForbiddenCursor
                    
                    onClicked: {
                        if (parent.enabled) {
                            console.log("显示导出选项");
                            exportOptionsPopup.open();
                        }
                    }
                }
            }
            
            Item { Layout.fillWidth: true } // 填充空间
        }
    }
    
    // SQL查询区域 - 完全独立的组件
    Rectangle {
        id: sqlQueryArea
        anchors.top: topToolbar.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 100 // 固定高度，不再根据脚本列表动态调整
        color: "#f0f0f0"
        border.color: "#cccccc"
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 5
            spacing: 5
            
            // 标题栏 - 修改为包含脚本按钮
            RowLayout {
                Layout.fillWidth: true
                height: 30
                spacing: 5
                
                // 脚本按钮区域 - 直接放在标题栏中
                ScrollView {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    clip: true
                    ScrollBar.horizontal.policy: ScrollBar.AsNeeded
                    ScrollBar.vertical.policy: ScrollBar.AlwaysOff
                    
                    Row {
                        id: sqlScriptsFlow
                        height: parent.height
                        spacing: 5
                        
                        // 脚本按钮将在这里动态创建
                    }
                }
                
                Item { Layout.fillWidth: true } // 填充空间
                
                // 添加执行按钮，方便用户手动执行
                Rectangle {
                    width: 30
                    height: 30
                    radius: 4
                    color: executeMouseArea.containsMouse ? "#e8f0fe" : "#f5f5f5"
                    border.color: "#dddddd"
                    enabled: isConnected
                    opacity: enabled ? 1.0 : 0.5
                    
                    Text {
                        anchors.centerIn: parent
                        text: "▶"
                        font.pixelSize: 14
                        color: "#4285f4"
                        font.bold: true
                    }
                    
                    MouseArea {
                        id: executeMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        cursorShape: parent.enabled ? Qt.PointingHandCursor : Qt.ForbiddenCursor
                        
                        onClicked: {
                            if (parent.enabled) {
                                var queryText = sqlQueryTextArea.text;
                                if (queryText.trim() !== "") {
                                    executeCustomQuery(queryText);
                                }
                            }
                        }
                    }
                    
                    ToolTip {
                        visible: executeMouseArea.containsMouse
                        text: "执行SQL查询 (Ctrl+Enter)"
                        delay: 500
                    }
                }
                
                // 添加保存按钮
                Rectangle {
                    width: 30
                    height: 30
                    radius: 4
                    color: saveMouseArea.containsMouse ? "#e8f0fe" : "#f5f5f5"
                    border.color: "#dddddd"
                    
                    Text {
                        anchors.centerIn: parent
                        text: "💾"
                        font.pixelSize: 14
                        color: "#4285f4"
                    }
                    
                    MouseArea {
                        id: saveMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        cursorShape: Qt.PointingHandCursor
                        
                        onClicked: {
                            if (sqlQueryTextArea.text.trim() !== "") {
                                scriptNameDialog.open();
                            } else {
                                showError("没有SQL查询可保存");
                            }
                        }
                    }
                    
                    ToolTip {
                        visible: saveMouseArea.containsMouse
                        text: "保存SQL查询到tools\\user_sql目录"
                        delay: 500
                    }
                }
            }
            
            // 查询输入区域
            RowLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 5
                
                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    
                    TextArea {
                        id: sqlQueryTextArea
                        placeholderText: "输入SQL查询语句，点击执行按钮或按Ctrl+Enter执行..."
                        selectByMouse: true
                        wrapMode: TextEdit.Wrap
                        font.pixelSize: 12
                        
                        property bool ignoreNextTextChange: false
                        
                        // 保留Ctrl+Enter快捷键执行
                        Keys.onPressed: function(event) {
                            if ((event.key === Qt.Key_Return || event.key === Qt.Key_Enter) && 
                                (event.modifiers & Qt.ControlModifier)) {
                                var queryText = text;
                                if (queryText.trim() !== "" && isConnected) {
                                    executeCustomQuery(queryText);
                                }
                                event.accepted = true;
                            }
                        }
                        
                        // 设置文本但不触发自动执行的函数
                        function setTextWithoutExecution(newText) {
                            ignoreNextTextChange = true;
                            text = newText;
                        }
                    }
                }
            }
        }
    }
    
    // 主布局 - 完全独立的组件
    Item {
        id: mainLayoutContainer
        anchors.top: sqlQueryArea.bottom
        anchors.topMargin: 5 // 与SQL查询区域保持间距
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        
        RowLayout {
            id: mainLayout
            anchors.fill: parent
            spacing: 0
            
            // 左侧表列表面板
            Rectangle {
                id: leftPanel
                Layout.preferredWidth: 200
                Layout.minimumWidth: 150
                Layout.maximumWidth: 300
                Layout.fillHeight: true
                color: "#f0f0f0"
                border.color: "#cccccc"
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 5
                    spacing: 5
                    
                    ColumnLayout {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        spacing: 5
                        
                        // 将标题文本替换为过滤器设置
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 5
                            
                            TextField {
                                id: tableFilterInput
                                Layout.fillWidth: true
                                Layout.preferredHeight: 22 // 减小高度
                                placeholderText: "关键字..."
                                selectByMouse: true
                                font.pixelSize: 11 // 减小字体大小
                                
                                onTextChanged: {
                                    // 根据输入的文本过滤表列表
                                    filterTablesList();
                                }
                            }
                        }
                        
                        // 显示当前数据库路径
                        Rectangle {
                            Layout.fillWidth: true
                            height: dbPathDisplay.contentHeight + 10
                            color: "#f5f5f5"
                            radius: 3
                            visible: isConnected
                            
                            Text {
                                id: dbPathDisplay
                                anchors.fill: parent
                                anchors.margins: 5
                                text: {
                                    if (dbToolCpp && isConnected) {
                                        var fullPath = dbToolCpp.getDefaultDatabasePath();
                                        // 提取文件名
                                        var lastSlashIndex = Math.max(fullPath.lastIndexOf('/'), fullPath.lastIndexOf('\\'));
                                        if (lastSlashIndex >= 0 && lastSlashIndex < fullPath.length - 1) {
                                            return fullPath.substring(lastSlashIndex + 1); // 只返回文件名
                                        }
                                        return fullPath;
                                    }
                                    return "";
                                }
                                font.pixelSize: 10
                                color: "#666666"
                                wrapMode: Text.WrapAnywhere
                                elide: Text.ElideMiddle
                            }
                            
                            // 鼠标悬停时显示全路径提示
                            MouseArea {
                                anchors.fill: parent
                                hoverEnabled: true
                                
                                ToolTip {
                                    visible: parent.containsMouse
                                    text: dbToolCpp && isConnected ? dbToolCpp.getDefaultDatabasePath() : ""
                                    delay: 500
                                }
                            }
                        }
                         
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: "#ffffff"
                            border.color: "#dddddd"
                            
                            // 简单列表模型替换原来的树状结构模型
                            ListModel {
                                id: tablesListModel
                                
                                // 添加一个表项
                                function addTable(tableName) {
                                    append({
                                        name: tableName
                                    });
                                }
                                
                                // 清除所有表
                                function clearTables() {
                                    clear();
                                }
                            }
                            
                            // 使用ScrollView包裹ListView，以支持滚动条
                            ScrollView {
                                anchors.fill: parent
                                clip: true
                                ScrollBar.vertical.policy: ScrollBar.AsNeeded // 根据需要显示滚动条
                                
                                ListView {
                                    id: tablesListView
                                    anchors.fill: parent
                                    model: tablesListModel
                                    clip: true
                                    
                                    // 空列表提示
                                    Text {
                                        anchors.centerIn: parent
                                        text: "无表数据"
                                        visible: tablesListModel.count === 0
                                        color: "#999999"
                                        font.pixelSize: 12
                                    }
                                    
                                    delegate: Rectangle {
                                        width: tablesListView.width
                                        height: 30
                                        color: tableMouseArea.containsMouse ? "#e0e0ff" : "#ffffff"
                                        
                                        Row {
                                            anchors.fill: parent
                                            anchors.margins: 5
                                            spacing: 5
                                            
                                            // 表图标
                                            Rectangle {
                                                width: 16
                                                height: 16
                                                radius: 2
                                                color: "#4caf50"  // 绿色图标，更容易看见
                                                anchors.verticalCenter: parent.verticalCenter
                                                
                                                Text {
                                                    anchors.centerIn: parent
                                                    text: "T"
                                                    color: "white"
                                                    font.pixelSize: 10
                                                    font.bold: true
                                                }
                                            }
                                            
                                            // 表名称
                                            Text {
                                                text: model.name
                                                anchors.verticalCenter: parent.verticalCenter
                                                elide: Text.ElideRight
                                                width: parent.width - 21 // 减去图标和间距
                                                color: "#333333"
                                            }
                                        }
                                        
                                        // 表项目的鼠标事件
                                        MouseArea {
                                            id: tableMouseArea
                                            anchors.fill: parent
                                            hoverEnabled: true
                                            
                                            onClicked: {
                                                // 检查是否点击了当前已选中的表
                                                if (selectedTable === model.name) {
                                                    console.log("表", model.name, "已经打开，无需重复加载");
                                                    return;
                                                }
                                                loadTableData(model.name);
                                            }
                                        }
                                        
                                        // 底部分隔线
                                        Rectangle {
                                            width: parent.width
                                            height: 1
                                            color: "#eeeeee"
                                            anchors.bottom: parent.bottom
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 显示数据库状态和路径
                        Column {
                            Layout.fillWidth: true
                            spacing: 3
                            
                            RowLayout {
                                width: parent.width
                                spacing: 5
                                
                                Text {
                                    text: isConnecting ? "连接中..." : (isConnected ? "已连接数据库" : "未连接数据库")
                                    color: isConnected ? "#4CAF50" : "#9E9E9E"
                                    font.pixelSize: 12
                                    Layout.fillWidth: true
                                }
                            }
                        }
                    }
                }
            }
            
            // 右侧数据表格面板
            Rectangle {
                id: rightPanel
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.minimumWidth: 400 // 确保右侧面板有最小宽度
                color: "#ffffff"
                
                ColumnLayout {
                    id: rightPanelLayout
                    anchors.fill: parent
                    anchors.margins: 5
                    spacing: 5
                    
                    // 表格标题
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 10
                        
                        Text {
                            text: selectedTable ? "表: " + selectedTable : "未选择表"
                            font.bold: true
                            font.pixelSize: 14
                        }
                        
                        Item { width: 20 } // 间隔
                        
                        Text {
                            text: "解析规则:"
                            font.pixelSize: 12
                            Layout.alignment: Qt.AlignVCenter
                        }
                        
                        TextField {
                            id: batchParseRuleInput
                            Layout.preferredWidth: 200
                            Layout.preferredHeight: 24
                            placeholderText: "例如: g;g;h (回车应用)"
                            selectByMouse: true
                            font.pixelSize: 12
                            Layout.alignment: Qt.AlignVCenter
                            
                            // 添加一个标志来避免重复处理
                            property bool ignoreTextChange: false
                            
                            // 修改文本变化时自动应用解析规则的逻辑
                            onTextChanged: {
                                // 如果是程序设置的文本，忽略自动应用
                                if (ignoreTextChange) {
                                    return;
                                }
                                
                                if (text.trim() === "") {
                                    // 当输入为空时，清空所有Parse列
                                    clearAllParseResults();
                                } else {
                                    // 自动应用解析规则
                                    applyBatchParseRule(text);
                                }
                            }
                            
                            // 当按下回车键时应用解析规则
                            Keys.onPressed: function(event) {
                                if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
                                    if (text.trim() !== "") {
                                        applyBatchParseRule(text);
                                    } else {
                                        clearAllParseResults();
                                    }
                                    event.accepted = true;
                                } else if (event.key === Qt.Key_Escape) {
                                    text = "";
                                    clearAllParseResults();
                                    event.accepted = true;
                                }
                            }
                        }
                        // 添加规则选择下拉列表
                        ComboBox {
                            id: parseRuleComboBox
                            Layout.preferredWidth: 120
                            Layout.preferredHeight: 24
                            Layout.alignment: Qt.AlignVCenter

                            // 解析规则选项模型
                            model: ListModel {
                                id: parseRuleModel

                                Component.onCompleted: {
                                    // 添加解析规则选项
                                    append({text: "选择规则", rule: "", description: "请选择解析规则"});
                                    append({text: "U8", rule: "a", description: "1字节转U8(无符号8位整数)"});
                                    append({text: "S8", rule: "b", description: "1字节转S8(有符号8位整数)"});
                                    append({text: "U16", rule: "c", description: "2字节转U16(无符号16位整数)"});
                                    append({text: "S16", rule: "d", description: "2字节转S16(有符号16位整数)"});
                                    append({text: "U32", rule: "e", description: "4字节转U32(无符号32位整数)"});
                                    append({text: "S32", rule: "f", description: "4字节转S32(有符号32位整数)"});
                                    append({text: "Float", rule: "g", description: "4字节转Float(浮点数)"});
                                    append({text: "Time", rule: "h", description: "4字节转Time(时间戳)"});
                                    append({text: "U64", rule: "i", description: "8字节转U64(无符号64位整数)"});
                                    append({text: "S64", rule: "j", description: "8字节转S64(有符号64位整数)"});
                                    append({text: "Double", rule: "k", description: "8字节转Double(双精度浮点数)"});
                                    append({text: "U16反序", rule: "l", description: "2字节反序转U16"});
                                    append({text: "Float反序", rule: "m", description: "4字节反序转Float"});
                                    append({text: "Time反序", rule: "n", description: "4字节反序转Time"});
                                    append({text: "Double反序", rule: "o", description: "8字节反序转Double"});
                                    append({text: "S64反序", rule: "p", description: "8字节反序转S64"});
                                }
                            }

                            // 自定义显示文本
                            textRole: "text"

                            // 当选择改变时
                            onCurrentIndexChanged: {
                                if (currentIndex > 0) { // 跳过"选择规则"选项
                                    var selectedRule = parseRuleModel.get(currentIndex).rule;
                                    var currentText = batchParseRuleInput.text.trim();

                                    // 如果当前规则输入框为空，直接设置选择的规则
                                    if (currentText === "") {
                                        batchParseRuleInput.text = selectedRule;
                                    } else {
                                        // 如果不为空，追加规则（用分号分隔）
                                        batchParseRuleInput.text = currentText + ";" + selectedRule;
                                    }

                                    // 重置下拉列表到默认选项
                                    currentIndex = 0;
                                }
                            }

                            // 自定义下拉项显示
                            delegate: ItemDelegate {
                                width: parseRuleComboBox.width
                                height: 30

                                Rectangle {
                                    anchors.fill: parent
                                    color: parent.hovered ? "#e8f0fe" : "transparent"

                                    Row {
                                        anchors.left: parent.left
                                        anchors.leftMargin: 8
                                        anchors.verticalCenter: parent.verticalCenter
                                        spacing: 8

                                        Text {
                                            text: model.text
                                            font.pixelSize: 12
                                            font.bold: index === 0
                                            color: index === 0 ? "#666666" : "#333333"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }

                                        Text {
                                            text: index === 0 ? "" : "(" + model.rule + ")"
                                            font.pixelSize: 10
                                            color: "#888888"
                                            anchors.verticalCenter: parent.verticalCenter
                                        }
                                    }
                                }

                                // 添加工具提示显示详细描述
                                ToolTip {
                                    visible: parent.hovered && index > 0
                                    text: model.description
                                    delay: 500
                                }
                            }
                        }
                        
                        Item { Layout.fillWidth: true } // 填充剩余空间
                    }
                    
                    // 表格区域
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        border.color: "#cccccc"
                        clip: true
                        
                        // 表头模型
                        ListModel {
                            id: headerModel
                        }
                        
                        // 数据模型
                        ListModel {
                            id: dataModel
                        }
                        
                        // 简单表格实现 - 重新设计布局确保表头固定
                        Item {
                            id: simpleTableView
                            anchors.fill: parent
                            
                            // 添加白色背景
                            Rectangle {
                                anchors.fill: parent
                                color: "white"
                                z: 0
                            }
                            
                            // 列宽定义 - 确保每列都有合理的宽度，只定义实际需要的列
                            property var columnWidths: [] // 动态调整的列宽数组
                            
                            Component.onCompleted: {
                                // 初始化列宽 - 根据截图调整初始宽度，增加第5列宽度
                                var initialWidths = [50, 200, 200, 100, 60, 500]; // 添加Parse列宽度为500
                                for (var i = 0; i < initialWidths.length; i++) {
                                    columnWidths[i] = initialWidths[i];
                                }
                                
                                // 初始化后立即更新布局
                                Qt.callLater(updateLayout);
                            }
                            
                            // 监听尺寸变化
                            onWidthChanged: {
                                Qt.callLater(updateLayout);
                            }
                            
                            onHeightChanged: {
                                Qt.callLater(updateLayout);
                            }
                            
                            // 表头 - 固定在顶部
                            Rectangle {
                                id: headerRow
                                width: parent.width
                                height: 30
                                color: "#a6c9e9"  // 蓝色表头背景，与截图一致
                                z: 10 // 确保表头在最上层，提高z值确保始终可见
                                anchors.top: parent.top
                                anchors.left: parent.left
                                anchors.right: parent.right
                                clip: true
                                
                                Row {
                                    id: headerRowContent
                                    height: parent.height
                                    spacing: 0
                                    x: -tableScrollView.contentItem.contentX // 绑定到表格内容的水平滚动位置
                                    width: {
                                        // 计算所有列的总宽度
                                        var totalWidth = 0;
                                        // 添加序号列宽度
                                        totalWidth += indexHeaderCell ? indexHeaderCell.width : 60;
                                        for (var i = 0; i < headerModel.count; i++) {
                                            totalWidth += (simpleTableView.columnWidths[i] || 150);
                                        }
                                        return Math.max(totalWidth, headerRow.width);
                                    }
                                    
                                    // 添加序号列
                                    Rectangle {
                                        id: indexHeaderCell
                                        width: 60
                                        height: parent.height
                                        color: "transparent"
                                        border.color: "#cccccc"
                                        border.width: 1
                                        
                                        Text {
                                            anchors.centerIn: parent
                                            text: ""  // 序号列表头通常不显示文字
                                            font.bold: true
                                            elide: Text.ElideRight
                                            width: parent.width - 10
                                            horizontalAlignment: Text.AlignHCenter
                                        }
                                        
                                        // 列宽调整控制柄
                                        Rectangle {
                                            id: indexResizer
                                            width: 5
                                            height: parent.height
                                            color: indexResizeHandle.containsMouse ? "#1976D2" : "transparent"
                                            anchors.right: parent.right
                                            z: 3 // 确保在最上层
                                            
                                            MouseArea {
                                                id: indexResizeHandle
                                                anchors.fill: parent
                                                anchors.margins: -4 // 扩大点击区域
                                                cursorShape: Qt.SizeHorCursor
                                                hoverEnabled: true
                                                
                                                property int startX: 0
                                                property int originalWidth: 0
                                                
                                                onPressed: {
                                                    startX = mouseX;
                                                    originalWidth = indexHeaderCell.width;
                                                }
                                                
                                                onPositionChanged: {
                                                    if (pressed) {
                                                        var newWidth = originalWidth + (mouseX - startX);
                                                        indexHeaderCell.width = newWidth;
                                                        // 强制更新整个表格
                                                        tableNeedsUpdate = !tableNeedsUpdate;
                                                        
                                                        // 更新所有行的序号列宽度
                                                        for (var i = 0; i < dataColumn.children.length; i++) {
                                                            if (dataColumn.children[i].updateRowWidth) {
                                                                dataColumn.children[i].updateRowWidth();
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                    Repeater {
                                        model: headerModel
                                        
                                        Rectangle {
                                            id: headerCell
                                            width: simpleTableView.columnWidths[index] || 150
                                            height: parent.height
                                            color: "transparent"
                                            border.width: 0

                                            // 列标题上的排序图标
                                            Row {
                                                anchors.centerIn: parent
                                                spacing: 4
                                                
                                                Text {
                                                    text: model.columnName
                                                    font.bold: true
                                                    font.pixelSize: 12
                                                    elide: Text.ElideRight
                                                    color: "#000000"
                                                }
                                            }
                                            
                                            // 为Parse列添加悬浮提示
                                            MouseArea {
                                                anchors.fill: parent
                                                hoverEnabled: true
                                                visible: model.columnName === "Parse"
                                                
                                                ToolTip {
                                                    visible: parent.containsMouse && model.columnName === "Parse"
                                                    text: {
                                                        if (model.columnName === "Parse" && selectedTable && dbToolCpp) {
                                                            var tooltipText = "";
                                                            
                                                            // 获取当前表的解析规则描述
                                                            var description = getDescriptionForTable(selectedTable);
                                                            if (description && description.length > 0) {
                                                                tooltipText +=  description;
                                                            }
                                                            // 如果没有任何内容，显示默认提示
                                                            if (tooltipText.length === 0) {
                                                                return "未配置默认解析规则";
                                                            }
                                                            
                                                            return tooltipText;
                                                        }
                                                        return "未配置默认解析规则";
                                                    }
                                                    delay: 500
                                                    // 增加最大宽度，确保长文本能够正常换行显示
                                                }
                                            }
                                            
                                            // 列宽调整控制柄
                                            Rectangle {
                                                id: columnResizer
                                                width: 5
                                                height: parent.height
                                                color: resizeHandle.containsMouse ? "#1976D2" : "transparent"
                                                anchors.right: parent.right
                                                z: 3 // 确保在最上层
                                                
                                                MouseArea {
                                                    id: resizeHandle
                                                    anchors.fill: parent
                                                    anchors.margins: -4 // 扩大点击区域
                                                    cursorShape: Qt.SizeHorCursor
                                                    hoverEnabled: true
                                                    
                                                    property int startX: 0
                                                    property int originalWidth: 0
                                                    property int columnIndex: index
                                                    
                                                    onPressed: {
                                                        startX = mouseX;
                                                        originalWidth = headerCell.width;
                                                    }
                                                    
                                                    onPositionChanged: {
                                                        if (pressed) {
                                                            var newWidth = originalWidth + (mouseX - startX);
                                                            simpleTableView.columnWidths[columnIndex] = newWidth;
                                                            headerCell.width = newWidth;
                                                            // 强制更新整个表格
                                                            tableNeedsUpdate = !tableNeedsUpdate;
                                                            
                                                            // 更新所有行的对应列宽度
                                                            for (var i = 0; i < dataColumn.children.length; i++) {
                                                                if (dataColumn.children[i].updateRowWidth) {
                                                                    dataColumn.children[i].updateRowWidth();
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // 数据区域 - 修改为直接放在表头下方，无间隙
                            ScrollView {
                                id: tableScrollView
                                anchors.top: headerRow.bottom
                                anchors.topMargin: 0 // 确保与表头无间隙
                                anchors.left: parent.left
                                anchors.right: parent.right
                                anchors.bottom: parent.bottom
                                clip: true
                                ScrollBar.horizontal.policy: ScrollBar.AsNeeded // 显示水平滚动条
                                ScrollBar.vertical.policy: ScrollBar.AsNeeded // 根据需要显示垂直滚动条
                                contentWidth: dataColumn.width // 确保内容宽度正确设置
                                
                                // 使用Column替代ListView以显示所有行
                                Column {
                                    id: dataColumn
                                    width: {
                                        // 计算所有列的总宽度
                                        var totalWidth = 0;
                                        // 添加序号列宽度
                                        totalWidth += indexHeaderCell ? indexHeaderCell.width : 60;
                                        for (var i = 0; i < headerModel.count; i++) {
                                            totalWidth += (simpleTableView.columnWidths[i] || 150);
                                        }
                                        // 确保总宽度至少与滚动视图一样宽
                                        var minWidth = tableScrollView ? tableScrollView.width : 0;
                                        return Math.max(totalWidth, minWidth);
                                    }
                                    spacing: 0
                                    
                                    // 监听tableNeedsUpdate属性变化，强制更新布局
                                    onWidthChanged: {
                                        // 当宽度变化时，强制更新每一行
                                        for (var i = 0; i < children.length; i++) {
                                            if (children[i].updateRowWidth) {
                                                children[i].updateRowWidth();
                                            }
                                        }
                                    }
                                    
                                    // 为每行数据创建一个行项目
                                    Repeater {
                                        model: dataModel
                                        
                                        Rectangle {
                                            id: dataRow
                                            width: dataColumn.width
                                            height: 25  // 减小行高，与截图一致
                                            // 交替行颜色，选中行为蓝色
                                            color: {
                                                if (index === currentRowIndex) {
                                                    return "#add8e6"; // 浅蓝色，表示选中行
                                                } else if (index % 2 === 0) {
                                                    return "#ffffff"; // 白色
                                                } else {
                                                    return "#f0f0f0"; // 浅灰色
                                                }
                                            }
                                            
                                            // 使用直接的行数据引用
                                            property var rowData: model
                                            
                                            // 更新行宽度的函数
                                            function updateRowWidth() {
                                                cellRow.children[0].width = indexHeaderCell.width;
                                                for (var i = 0; i < headerModel.count && i < cellRow.children.length - 1; i++) {
                                                    cellRow.children[i + 1].width = simpleTableView.columnWidths[i] || 150;
                                                }
                                            }
                                            
                                            // 监听tableNeedsUpdate属性变化
                                            Connections {
                                                target: databaseToolView
                                                function onTableNeedsUpdateChanged() {
                                                    dataRow.updateRowWidth();
                                                }
                                            }
                                            
                                            // 行点击处理
                                            MouseArea {
                                                anchors.fill: parent
                                                onClicked: {
                                                    currentRowIndex = index;
                                                }
                                            }
                                            
                                            Row {
                                                id: cellRow
                                                height: parent.height
                                                spacing: 0
                                                
                                                // 添加序号列
                                                Rectangle {
                                                    width: indexHeaderCell.width
                                                    height: parent.height
                                                    border.color: "#cccccc"
                                                    border.width: 1
                                                    color: "transparent"
                                                    
                                                    Text {
                                                        anchors.fill: parent
                                                        anchors.margins: 5
                                                        text: index + 1 // 显示从1开始的序号
                                                        elide: Text.ElideRight
                                                        verticalAlignment: Text.AlignVCenter
                                                        horizontalAlignment: Text.AlignHCenter
                                                        font.pixelSize: 12
                                                    }
                                                }
                                                
                                                Repeater {
                                                    model: headerModel.count
                                                    
                                                    Rectangle {
                                                        width: simpleTableView.columnWidths[index] || 150
                                                        height: parent.height
                                                        border.color: "#cccccc"
                                                        border.width: 1
                                                        color: "transparent"
                                                        
                                                        // 获取当前列的列名和索引
                                                        property string columnName: headerModel.get(index).columnName
                                                        property int columnIndex: index
                                                        
                                                        Text {
                                                            anchors.fill: parent
                                                            anchors.margins: 5
                                                            text: {
                                                                // 对于Parse列特殊处理
                                                                if (columnName === "Parse") {
                                                                    // 获取Parse列的数据
                                                                    if (rowData && typeof rowData === 'object' && "colParse" in rowData) {
                                                                        return rowData["colParse"];
                                                                    }
                                                                    return ""; // 如果没有数据则为空
                                                                }
                                                                
                                                                // 计算正确的列键
                                                                var colKey = "col" + (columnIndex + 1);
                                                                
                                                                // 安全地访问数据
                                                                if (rowData && typeof rowData === 'object') {
                                                                    // 检查是否存在该列的数据
                                                                    if (colKey in rowData) {
                                                                        var cellValue = rowData[colKey];
                                                                        
                                                                        // 处理不同类型的值
                                                                        if (cellValue === null || cellValue === undefined) {
                                                                            return "";
                                                                        } else if (typeof cellValue === 'object') {
                                                                            // 对于DataSet列，特殊处理二进制/十六进制数据
                                                                            if (columnName === "DataSet") {
                                                                                return objectToHexString(cellValue);
                                                                            }
                                                                            
                                                                            // 其他对象类型
                                                                            if (Object.keys(cellValue).length === 0) {
                                                                                return "";
                                                                            }
                                                                            return JSON.stringify(cellValue);
                                                                        } else if (typeof cellValue === 'string') {
                                                                            // 字符串值
                                                                            var strValue = cellValue;
                                                                            
                                                                            // 检查是否为空字符串
                                                                            if (strValue === "") {
                                                                                return "";
                                                                            }
                                                                            
                                                                            // 处理DataFlag、TypeId和DataId列，以16进制显示
                                                                            if (columnName === "DataFlag" || columnName=== "TypeId" || columnName === "DataId") {
                                                                                if (/^-?\d+(\.\d+)?$/.test(strValue)) {
                                                                                    var numValue = parseInt(strValue);
                                                                                    // 使用无符号32位整数表示（两个补码）
                                                                                    var hexValue;
                                                                                    if (numValue < 0) {
                                                                                        // 对于负数，使用32位二进制补码
                                                                                        // 0xFFFFFFFF + 1 = 2^32
                                                                                        hexValue = (0x100000000 + numValue).toString(16).toLowerCase();
                                                                                    } else {
                                                                                        hexValue = numValue.toString(16).toLowerCase();
                                                                                    }
                                                                                    // 确保始终有8位
                                                                                    hexValue = hexValue.padStart(8, '0');
                                                                                    return "0x" + hexValue;
                                                                                }
                                                                            }
                                                                            
                                                                            // 处理DataTime和CollectionTime列
                                                                            if (columnName === "DataTime" || columnName === "CollectionTime") {
                                                                                // 将Unix时间戳转换为可读格式
                                                                                if (/^-?\d+(\.\d+)?$/.test(strValue)) {
                                                                                    try {
                                                                                        var timestamp = parseInt(strValue);
                                                                                        var date = new Date(timestamp * 1000); // Unix时间戳是秒，需要转换为毫秒
                                                                                        
                                                                                        // 检查日期是否有效
                                                                                        if (isNaN(date.getTime())) {
                                                                                            return strValue; // 返回原始值
                                                                                        }
                                                                                        
                                                                                        // 显示UTC时间，并在括号中显示原始值
                                                                                        var year = date.getUTCFullYear().toString(); // 获取年份后两位
                                                                                        var month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                                                                                        var day = date.getUTCDate().toString().padStart(2, '0');
                                                                                        var hours = date.getUTCHours().toString().padStart(2, '0');
                                                                                        var minutes = date.getUTCMinutes().toString().padStart(2, '0');
                                                                                        var seconds = date.getUTCSeconds().toString().padStart(2, '0');
                                                                                        return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
                                                                                    } catch (e) {
                                                                                        return strValue; // 出错时返回原始值
                                                                                    }
                                                                                }
                                                                                return strValue;
                                                                            }
                                                                            
                                                                            // 检查是否是数值字符串，尝试转换为数值
                                                                            if (/^-?\d+(\.\d+)?$/.test(strValue)) {
                                                                                return Number(strValue);
                                                                            }
                                                                            
                                                                            // 检查是否是十六进制字符串
                                                                            if (columnName === "DataSet" || 
                                                                                strValue.startsWith("[Binary data")) {
                                                                                return strValue;
                                                                            }
                                                                            
                                                                            // 普通字符串
                                                                            return strValue;
                                                                        } else if (typeof cellValue === 'number') {
                                                                            // 处理DataFlag列的数字值为十六进制
                                                                            if (columnName === "DataFlag") {
                                                                                var hexVal = cellValue.toString(16).toLowerCase().padStart(8, '0');
                                                                                return "0x" + hexVal;
                                                                            }
                                                                            // 其他数字值直接返回
                                                                            return cellValue;
                                                                        } else {
                                                                            // 基本类型直接转换为字符串
                                                                            return String(cellValue);
                                                                        }
                                                                    }
                                                                }
                                                                return "";
                                                            }
                                                            elide: columnName === "Parse" ? Text.ElideNone : Text.ElideRight // 只有Parse列不省略，其他列包括DataSet都显示省略号
                                                            verticalAlignment: Text.AlignVCenter
                                                            horizontalAlignment: Text.AlignLeft
                                                            font.family: "Courier" // 使用等宽字体显示十六进制数据
                                                            font.pixelSize: columnName === "DataSet" || columnIndex === 4 || columnName === "Parse" ? 10 : 12 // 对于DataSet列、第5列和Parse列使用更小的字体
                                                            wrapMode: Text.NoWrap // 所有列都不允许换行
                                                            clip: true // 确保文本不会溢出单元格
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 无数据时显示提示
                        Text {
                            anchors.centerIn: parent
                            text: "没有数据"
                            visible: dataModel.count === 0 && selectedTable !== null
                            color: "#999999"
                            font.pixelSize: 14
                        }
                        
                        // 分页控件
                        Rectangle {
                            id: paginationControl
                            width: parent.width
                            height: 40
                            anchors.bottom: parent.bottom
                            anchors.horizontalCenter: parent.horizontalCenter
                            color: "transparent"
                            visible: totalRecords > 1000 // 只有当记录数超过1000时才显示
                            
                            Row {
                                anchors.centerIn: parent
                                spacing: 10
                                
                                // 首页按钮
                                Rectangle {
                                    width: 30
                                    height: 30
                                    radius: 3
                                    color: firstPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                                    border.color: "#80cccccc"
                                    
                                    Text {
                                        anchors.centerIn: parent
                                        text: "<<"
                                        font.pixelSize: 12
                                        color: "#000000"
                                        font.bold: true
                                    }
                                    
                                    MouseArea {
                                        id: firstPageMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        cursorShape: Qt.PointingHandCursor
                                        onClicked: {
                                            loadFirstPage();
                                        }
                                    }
                                }
                                
                                // 上一页按钮
                                Rectangle {
                                    width: 30
                                    height: 30
                                    radius: 3
                                    color: prevPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                                    border.color: "#80cccccc"
                                    
                                    Text {
                                        anchors.centerIn: parent
                                        text: "<"
                                        font.pixelSize: 12
                                        color: "#000000"
                                        font.bold: true
                                    }
                                    
                                    MouseArea {
                                        id: prevPageMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        cursorShape: Qt.PointingHandCursor
                                        onClicked: {
                                            loadPrevPage();
                                        }
                                    }
                                }
                                
                                // 页码信息
                                Rectangle {
                                    width: 200
                                    height: 30
                                    radius: 3
                                    color: "#80f5f5f5"
                                    border.color: "#80cccccc"
                                    
                                    Text {
                                        anchors.centerIn: parent
                                        text: {
                                            var totalPages = Math.ceil(totalRecords / pageSize) || 1;
                                            return "第 " + (currentPage + 1) + " / " + totalPages + " 页，共 " + totalRecords + " 条记录";
                                        }
                                        font.pixelSize: 12
                                        color: "#000000"
                                        font.bold: true
                                        horizontalAlignment: Text.AlignHCenter
                                    }
                                }
                                
                                // 下一页按钮
                                Rectangle {
                                    width: 30
                                    height: 30
                                    radius: 3
                                    color: nextPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                                    border.color: "#80cccccc"
                                    
                                    Text {
                                        anchors.centerIn: parent
                                        text: ">"
                                        font.pixelSize: 12
                                        color: "#000000"
                                        font.bold: true
                                    }
                                    
                                    MouseArea {
                                        id: nextPageMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        cursorShape: Qt.PointingHandCursor
                                        onClicked: {
                                            loadNextPage();
                                        }
                                    }
                                }
                                
                                // 末页按钮
                                Rectangle {
                                    width: 30
                                    height: 30
                                    radius: 3
                                    color: lastPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                                    border.color: "#80cccccc"
                                    
                                    Text {
                                        anchors.centerIn: parent
                                        text: ">>"
                                        font.pixelSize: 12
                                        color: "#000000"
                                        font.bold: true
                                    }
                                    
                                    MouseArea {
                                        id: lastPageMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        cursorShape: Qt.PointingHandCursor
                                        onClicked: {
                                            loadLastPage();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 替换位置监控定时器，使用绑定和信号处理
    
    // 移除不需要的Connections对象，因为现在使用直接绑定
    
    // 监听窗口大小变化，调整列表宽度
    Connections {
        target: tablesListView ? tablesListView.parent.parent : null // 现在父元素是ScrollView，所以是parent.parent
        enabled: tablesListView && tablesListView.parent.parent
        
        // 监听宽度变化
        function onWidthChanged() {
            if (tablesListView) {
                // 不需要手动调整宽度，ScrollView会自动处理
                Qt.callLater(updateLayout);
            }
        }
    }
    
    // 监听父窗口大小变化
    Connections {
        target: parent
        function onWidthChanged() {
            Qt.callLater(updateLayout);
        }
        
        function onHeightChanged() {
            Qt.callLater(updateLayout);
        }
    }
    
    // 在组件完成加载时进行一次初始化
    Component.onCompleted: {
        // 初始化布局
        Qt.callLater(updateLayout);
        
        // 标记已初始化
        isInitialized = true;
        
        // 延迟执行非关键初始化操作
        Qt.callLater(lazyInit);
    }
    
    // 延迟初始化函数 - 处理非关键的初始化操作
    function lazyInit() {
        if (lazyInitComplete) return;
        
        // 扫描SQL脚本文件 - 延迟执行
        scanSqlScripts();
        
        // 标记延迟初始化完成
        lazyInitComplete = true;
    }
    
    // 扫描SQL脚本文件
    function scanSqlScripts() {
        if (dbToolCpp && typeof dbToolCpp.listSqlScripts === "function") {
            sqlScriptsList = dbToolCpp.listSqlScripts();
            console.log("扫描到SQL脚本文件数量:", sqlScriptsList.length);
            // 创建SQL脚本按钮
            createSqlScriptButtons();
        } else {
            console.warn("dbToolCpp对象未初始化或不支持listSqlScripts方法");
        }
    }

    // 创建SQL脚本按钮
    function createSqlScriptButtons() {
        // 清除现有的按钮
        if (sqlScriptsFlow) {
            for (var i = sqlScriptsFlow.children.length - 1; i >= 0; i--) {
                if (sqlScriptsFlow.children[i].objectName === "scriptButton" || 
                    sqlScriptsFlow.children[i].objectName === "refreshButton") {
                    sqlScriptsFlow.children[i].destroy();
                }
            }
        }
        
        // 为每个脚本创建一个按钮
        for (var i = 0; i < sqlScriptsList.length; i++) {
            var scriptName = sqlScriptsList[i];
            createScriptButton(scriptName);
        }
    }

    // 创建单个脚本按钮
    function createScriptButton(scriptName) {
        // 使用Qt.createQmlObject直接创建按钮
        var buttonCode = 
            'import QtQuick 2.12; import QtQuick.Controls 2.12; ' +
            'Rectangle { ' +
            '    id: scriptButton; ' +
            '    width: scriptText.width + 16; ' +
            '    height: 24; ' +
            '    color: scriptMouseArea.containsMouse ? "#e8f0fe" : "#f5f5f5"; ' +
            '    border.color: "#dddddd"; ' +
            '    radius: 4; ' +
            '    property string scriptName: "' + scriptName + '"; ' +
            '    objectName: "scriptButton"; ' +
            '    anchors.verticalCenter: parent ? parent.verticalCenter : undefined; ' +
            '    ' +
            '    Text { ' +
            '        id: scriptText; ' +
            '        anchors.centerIn: parent; ' +
            '        text: "' + scriptName + '"; ' +
            '        font.pixelSize: 11; ' +
            '        color: "#333333"; ' +
            '    } ' +
            '    ' +
            '    MouseArea { ' +
            '        id: scriptMouseArea; ' +
            '        anchors.fill: parent; ' +
            '        hoverEnabled: true; ' +
            '        cursorShape: Qt.PointingHandCursor; ' +
            '        acceptedButtons: Qt.LeftButton | Qt.RightButton; ' + // 接受左键和右键
            '        ' +
            '        onClicked: function(mouse) { ' +
            '            if (mouse.button === Qt.LeftButton) { ' +
            '                loadSqlScript("' + scriptName + '"); ' +
            '            } else if (mouse.button === Qt.RightButton) { ' +
            '                scriptContextMenu.scriptName = "' + scriptName + '"; ' +
            '                scriptContextMenu.popup(); ' +
            '            } ' +
            '        } ' +
            '    } ' +
            '    ' +
            '    ToolTip { ' +
            '        visible: scriptMouseArea.containsMouse; ' +
            '        text: "执行 ' + scriptName + ' 脚本"; ' +
            '        delay: 500; ' +
            '    } ' +
            '} ';
        
        try {
            var button = Qt.createQmlObject(buttonCode, sqlScriptsFlow, "dynamicButton_" + scriptName);
            if (!button) {
                console.error("无法创建按钮对象");
            }
        } catch (e) {
            console.error("创建按钮时出错:", e);
        }
    }

    // 加载SQL脚本内容
    function loadSqlScript(scriptName) {
        if (dbToolCpp && typeof dbToolCpp.loadSqlScript === "function") {
            var scriptContent = dbToolCpp.loadSqlScript(scriptName);
            if (scriptContent && scriptContent.length > 0) {
                if (sqlQueryTextArea) {
                    sqlQueryTextArea.text = scriptContent;
                }
            } else {
                showError("无法加载SQL脚本: " + scriptName);
            }
        } else {
            showError("加载SQL脚本功能未实现");
        }
    }
    
    // 重置模块状态
    function resetState() {
        console.log("重置数据库工具模块状态");
        
        // 断开数据库连接
        if (isConnected && dbToolCpp) {
            console.log("断开数据库连接");
            dbToolCpp.disconnectFromDatabase();
            
            // 清空数据库路径
            dbToolCpp.setDatabasePath("");
        }
        
        // 重置状态变量
        isConnected = false;
        selectedTable = null;
        tableData = [];
        isConnecting = false;
        currentRowIndex = -1;
        hasManuallyAdjustedColumns = false; // 重置列宽调整标志
        
        // 清空表格数据
        tablesListModel.clearTables();
        headerModel.clear();
        dataModel.clear();
        
        // 清空原始表列表
        originalTablesList = [];
    }
    
    // 添加缺少的updateLayout函数
    function updateLayout() {
        // 更新表格布局
        if (headerRowContent && dataColumn) {
            // 确保表头和数据行具有相同的宽度
            var totalWidth = 0;
            // 添加序号列宽度
            totalWidth += indexHeaderCell ? indexHeaderCell.width : 60;
            for (var i = 0; i < headerModel.count; i++) {
                totalWidth += (simpleTableView.columnWidths[i] || 150);
            }
            
            // 更新所有行的宽度
            for (var j = 0; j < dataColumn.children.length; j++) {
                if (dataColumn.children[j].updateRowWidth) {
                    dataColumn.children[j].updateRowWidth();
                }
            }
            
            // 确保滚动视图内容宽度正确设置
            if (tableScrollView) {
                tableScrollView.contentWidth = totalWidth;
            }
        }
        
        // 强制重新计算表格布局
        if (simpleTableView) {
            simpleTableView.width = simpleTableView.width + 0.1;
            simpleTableView.width = simpleTableView.width - 0.1;
        }
    }
    
    // 执行自定义SQL查询
    function executeCustomQuery(sqlQuery) {
        if (!isConnected || !dbToolCpp) {
            showError("未连接到数据库");
            return;
        }
        
        try {
            console.log("执行SQL查询:", sqlQuery);
            
            // 检查dbToolCpp对象是否有executeCustomQuery方法
            if (typeof dbToolCpp.executeCustomQuery !== "function") {
                showError("SQL查询功能未实现");
                return;
            }
            
            // 重置分页
            currentPage = 0;
            
            // 保存原始查询
            currentQuery = sqlQuery;
            
            // 调用C++方法执行查询 - 不添加LIMIT
            tableData = dbToolCpp.executeCustomQuery(sqlQuery);
            
            // 检查返回结果
            if (!tableData || tableData.length === 0) {
                console.log("查询没有返回结果");
                // 清空表格
                headerModel.clear();
                dataModel.clear();
                totalRecords = 0;
                
                // 隐藏分页控件
                if (newPaginationControl) {
                    newPaginationControl.visible = false;
                }
                return;
            }
            
            // 检查是否有错误
            if (tableData.length > 0 && tableData[0].error) {
                showError("SQL查询错误: " + tableData[0].message);
                return;
            }
            
            // 设置总记录数
            totalRecords = tableData.length > 0 ? tableData.length - 1 : 0; // 减去表头行
            console.log("查询总记录数:", totalRecords);
            
            // 更新表头和数据
            selectedTable = "SQL查询结果";
            updateHeaderModel();
            updateDataModel();
            
            // 强制更新布局
            Qt.callLater(updateLayout);
            
            // 检查是否需要显示分页控件
            console.log("是否应显示分页控件:", totalRecords > 1000, "总记录数:", totalRecords);
            // 分页控件的可见性由绑定自动控制
            
            // SQL查询后，如果有解析规则，自动应用
            if (batchParseRuleInput && batchParseRuleInput.text.trim() !== "") {
                Qt.callLater(function() {
                    applyBatchParseRule(batchParseRuleInput.text);
                });
            }
        } catch (e) {
            console.error("执行SQL查询时发生异常:", e);
            showError("执行SQL查询时发生异常: " + e);
        }
    }
    
    // 加载下一页数据
    function loadNextPage() {
        if (!isConnected || !currentQuery) {
            return;
        }
        
        var totalPages = Math.ceil(totalRecords / pageSize);
        if (currentPage < totalPages - 1) {
            currentPage++;
            loadCurrentPage();
        }
    }
    
    // 加载上一页数据
    function loadPrevPage() {
        if (!isConnected || !currentQuery || currentPage <= 0) {
            return;
        }
        
        currentPage--;
        loadCurrentPage();
    }
    
    // 加载首页数据
    function loadFirstPage() {
        if (!isConnected || !currentQuery || currentPage === 0) {
            return;
        }
        
        currentPage = 0;
        loadCurrentPage();
    }
    
    // 加载末页数据
    function loadLastPage() {
        if (!isConnected || !currentQuery) {
            return;
        }
        
        var totalPages = Math.ceil(totalRecords / pageSize);
        if (currentPage < totalPages - 1) {
            currentPage = totalPages - 1;
            loadCurrentPage();
        }
    }
    
    // 加载当前页数据
    function loadCurrentPage() {
        if (!isConnected || !currentQuery) {
            return;
        }
        
        try {
            console.log("加载第", (currentPage + 1), "页数据");
            
            // 不需要重新查询，只需更新数据模型显示当前页数据
            updateDataModel();
            
            // 强制更新布局
            Qt.callLater(updateLayout);
            
            // 翻页后重新应用当前解析规则
            if (batchParseRuleInput && batchParseRuleInput.text.trim() !== "") {
                // 直接应用，不需要通过文本变化事件
                applyBatchParseRule(batchParseRuleInput.text);
            }
            
        } catch (e) {
            console.error("加载分页数据时发生异常:", e);
            showError("加载分页数据时发生异常: " + e);
        }
    }
    
    // 辅助函数：更新主布局位置
    
    // 脚本名称输入对话框 - 使用Rectangle替代Dialog
    Rectangle {
        id: scriptNameDialog
        width: 300
        height: 150
        color: "#f0f0f0"
        border.color: "#cccccc"
        radius: 5
        
        // 默认不可见
        visible: false
        
        // 定位在屏幕中央
        property bool isOpen: false
        
        // 打开和关闭函数
        function open() {
            isOpen = true;
            visible = true;
            scriptNameInput.text = "";
            scriptNameInput.forceActiveFocus();
        }
        
        function close() {
            isOpen = false;
            visible = false;
        }
        
        function accept() {
            var scriptName = scriptNameInput.text.trim();
            if (scriptName !== "") {
                // 确保文件名有.sql后缀
                if (!scriptName.toLowerCase().endsWith(".sql")) {
                    scriptName += ".sql";
                }
                
                // 构建完整路径 - 使用可执行文件目录下的tools/user_sql
                var fullPath = "tools/user_sql/" + scriptName;
                console.log("保存SQL查询到:", fullPath);
                
                saveQueryToFile(fullPath);
            } else {
                showError("脚本名称不能为空");
            }
            close();
        }
        
        // 显示时定位到屏幕中央
        onVisibleChanged: {
            if (visible) {
                x = (parent.width - width) / 2;
                y = (parent.height - height) / 2;
            }
        }
        
        // 标题栏
        Rectangle {
            id: dialogTitleBar
            width: parent.width
            height: 30
            color: "#e0e0e0"
            radius: 5
            
            Text {
                anchors.left: parent.left
                anchors.leftMargin: 10
                anchors.verticalCenter: parent.verticalCenter
                text: "保存SQL脚本"
                font.bold: true
                font.pixelSize: 12
            }
            
            // 关闭按钮
            Rectangle {
                width: 20
                height: 20
                anchors.right: parent.right
                anchors.rightMargin: 5
                anchors.verticalCenter: parent.verticalCenter
                color: closeMouseArea.containsMouse ? "#ff6666" : "transparent"
                radius: 10
                
                Text {
                    anchors.centerIn: parent
                    text: "×"
                    font.pixelSize: 14
                    color: closeMouseArea.containsMouse ? "white" : "#666666"
                }
                
                MouseArea {
                    id: closeMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: {
                        scriptNameDialog.close();
                    }
                }
            }
        }
        
        // 内容区域
        Item {
            anchors.top: dialogTitleBar.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.bottom: dialogButtonBar.top
            anchors.margins: 10
            
            Text {
                id: dialogLabel
                anchors.top: parent.top
                anchors.left: parent.left
                text: "请输入SQL脚本名称:"
                font.pixelSize: 12
            }
            
            TextField {
                id: scriptNameInput
                anchors.top: dialogLabel.bottom
                anchors.topMargin: 10
                anchors.left: parent.left
                anchors.right: parent.right
                height: 30
                placeholderText: "例如: my_query.sql"
                selectByMouse: true
                
                // 按回车键确认
                Keys.onPressed: function(event) {
                    if (event.key === Qt.Key_Return || event.key === Qt.Key_Enter) {
                        scriptNameDialog.accept();
                        event.accepted = true;
                    } else if (event.key === Qt.Key_Escape) {
                        scriptNameDialog.close();
                        event.accepted = true;
                    }
                }
            }
        }
        
        // 按钮区域
        Rectangle {
            id: dialogButtonBar
            width: parent.width
            height: 40
            anchors.bottom: parent.bottom
            color: "#e0e0e0"
            
            Row {
                anchors.right: parent.right
                anchors.rightMargin: 10
                anchors.verticalCenter: parent.verticalCenter
                spacing: 10
                
                // 取消按钮
                Rectangle {
                    width: 60
                    height: 25
                    radius: 3
                    color: cancelMouseArea.containsMouse ? "#e0e0e0" : "#f5f5f5"
                    border.color: "#cccccc"
                    
                    Text {
                        anchors.centerIn: parent
                        text: "取消"
                        font.pixelSize: 12
                    }
                    
                    MouseArea {
                        id: cancelMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        onClicked: {
                            scriptNameDialog.close();
                        }
                    }
                }
                
                // 确定按钮
                Rectangle {
                    width: 60
                    height: 25
                    radius: 3
                    color: okMouseArea.containsMouse ? "#4CAF50" : "#5cb85c"
                    
                    Text {
                        anchors.centerIn: parent
                        text: "确定"
                        color: "white"
                        font.pixelSize: 12
                    }
                    
                    MouseArea {
                        id: okMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        onClicked: {
                            scriptNameDialog.accept();
                        }
                    }
                }
            }
        }
    }

    // 保存SQL查询函数
    function saveSqlQuery() {
        // 检查SQL文本是否为空
        if (!sqlQueryTextArea || sqlQueryTextArea.text.trim() === "") {
            showError("没有SQL查询可保存");
            return;
        }
        
        // 打开脚本名称对话框
        scriptNameDialog.open();
        
        // 聚焦到输入框
        scriptNameInput.forceActiveFocus();
    }
    
    // 保存查询到文件
    function saveQueryToFile(filePath) {
        try {
            if (!dbToolCpp || typeof dbToolCpp.saveTextToFile !== "function") {
                showError("保存功能未实现");
                return;
            }
            
            var success = dbToolCpp.saveTextToFile(sqlQueryTextArea.text, filePath);
            if (success) {
                console.log("SQL查询已保存到:", filePath);
                // 保存成功后重新扫描SQL脚本文件
                Qt.callLater(scanSqlScripts);
            } else {
                showError("保存SQL查询失败");
            }
        } catch (e) {
            console.error("保存SQL查询时发生异常:", e);
            showError("保存SQL查询时发生异常: " + e);
        }
    }

    // 添加解析函数
    function parseDataSet(hexString, parseType) {
        if (!hexString || hexString.trim() === "") {
            return "无数据可解析";
        }
        
        try {
            // 使用C++的DataParser类进行解析
            if (dbToolCpp && dbToolCpp.dataParser) {
                return dbToolCpp.dataParser.parseHexData(hexString, parseType);
            } else {
                return "解析器未初始化";
            }
        } catch (e) {
            console.error("转换对象到十六进制字符串时出错:", e);
            return "[转换错误]";
        }
    }
    
    // 将解析结果设置到Parse列
    function setParseResult(rowIndex, result) {
        if (rowIndex >= 0 && rowIndex < dataModel.count) {
            // 更新数据模型中的Parse列
            dataModel.setProperty(rowIndex, "colParse", result);
        }
    }
    
    // 添加键盘快捷键处理
    function handleParseKeyPress(event, rowIndex) {
        // 此功能已移除
    }
    
    // 清除所有行的解析结果
    function clearAllParseResults() {
        for (var i = 0; i < dataModel.count; i++) {
            setParseResult(i, "");
        }
    }
    
    // 应用批量解析规则
    function applyBatchParseRule(ruleText) {
        if (!ruleText || ruleText.trim() === "") {
            console.log("解析规则为空，不应用");
            clearAllParseResults();
            return;
        }
        
        try {
            console.log("应用批量解析规则:", ruleText);
            
            // 找到DataSet列的索引
            var dataSetColIndex = -1;
            for (var i = 0; i < headerModel.count; i++) {
                if (headerModel.get(i).columnName === "DataSet") {
                    dataSetColIndex = i;
                    break;
                }
            }
            
            if (dataSetColIndex < 0) {
                console.warn("未找到DataSet列");
                return;
            }
            
            // 对每一行应用规则
            for (var rowIndex = 0; rowIndex < dataModel.count; rowIndex++) {
                var colKey = "col" + (dataSetColIndex + 1);
                var hexData = "";
                
                // 获取当前行的DataSet数据
                if (colKey in dataModel.get(rowIndex)) {
                    hexData = dataModel.get(rowIndex)[colKey];
                }
                
                if (!hexData || hexData.trim() === "") {
                    continue; // 跳过没有数据的行
                }
                
                // 使用C++的DataParser类进行批量解析
                if (dbToolCpp && dbToolCpp.dataParser) {
                    var parseResult = dbToolCpp.dataParser.batchParseHexData(hexData, ruleText);
                    setParseResult(rowIndex, parseResult);
                } else {
                    setParseResult(rowIndex, "解析器未初始化");
                }
            }
        } catch (e) {
            console.error("应用时出错:", e);
            showError("应用批量解析规则时出错: " + e.message);
        }
    }

    // 添加到主布局中，在topToolbar部分之后

    // 导出选项弹出菜单
    Popup {
        id: exportOptionsPopup
        width: 180
        height: 100
        x: exportMouseArea.parent.x
        y: exportMouseArea.parent.y + exportMouseArea.parent.height
        padding: 8
        
        contentItem: Column {
            spacing: 6
            
            // 导出当前页按钮
            Rectangle {
                width: parent.width
                height: 34
                color: exportPageMouseArea.containsMouse ? "#e8f0fe" : "#ffffff"
                border.color: exportPageMouseArea.containsMouse ? "#4285f4" : "transparent"
                radius: 4
                
                Row {
                    anchors.centerIn: parent
                    spacing: 8
                    
                    // 图标
                    Rectangle {
                        width: 16
                        height: 16
                        radius: 2
                        color: "transparent"
                        anchors.verticalCenter: parent.verticalCenter
                        
                        Text {
                            anchors.centerIn: parent
                            text: "📄"
                            font.pixelSize: 12
                            color: "#4285f4"
                        }
                    }
                    
                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        text: "导出当前页"
                        font.pixelSize: 13
                        color: "#333333"
                    }
                }
                
                MouseArea {
                    id: exportPageMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    
                    onClicked: {
                        exportOptionsPopup.close();
                        exportFileDialog.exportMode = "page";
                        
                        // 设置默认文件名
                        var fileName = selectedTable || "未命名表";
                        fileName += "_第" + (currentPage + 1) + "页.csv";
                        exportFileDialog.defaultFileName = fileName;
                        
                        // 打开文件对话框
                        console.log("导出当前页，默认文件名:", fileName);
                        exportFileDialog.open();
                    }
                }
            }
            
            // 导出整个表按钮
            Rectangle {
                width: parent.width
                height: 34
                color: exportTableMouseArea.containsMouse ? "#e8f0fe" : "#ffffff"
                border.color: exportTableMouseArea.containsMouse ? "#4285f4" : "transparent"
                radius: 4
                
                Row {
                    anchors.centerIn: parent
                    spacing: 8
                    
                    // 图标
                    Rectangle {
                        width: 16
                        height: 16
                        radius: 2
                        color: "transparent"
                        anchors.verticalCenter: parent.verticalCenter
                        
                        Text {
                            anchors.centerIn: parent
                            text: "📊"
                            font.pixelSize: 12
                            color: "#4285f4"
                        }
                    }
                    
                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        text: "导出整个表"
                        font.pixelSize: 13
                        color: "#333333"
                    }
                }
                
                MouseArea {
                    id: exportTableMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    
                    onClicked: {
                        exportOptionsPopup.close();
                        exportFileDialog.exportMode = "table";
                        
                        // 设置默认文件名
                        var fileName = selectedTable || "未命名表";
                        fileName += ".csv";
                        exportFileDialog.defaultFileName = fileName;
                        
                        // 打开文件对话框
                        console.log("导出整个表，默认文件名:", fileName);
                        exportFileDialog.open();
                    }
                }
            }
        }
        
        background: Rectangle {
            color: "#ffffff"
            border.color: "#dddddd"
            radius: 6
            layer.enabled: true
            layer.effect: DropShadow {
                transparentBorder: true
                horizontalOffset: 0
                verticalOffset: 2
                radius: 8.0
                samples: 17
                color: "#20000000"
            }
        }
    }

    // 获取表的解析规则描述
    function getDescriptionForTable(tableName) {
        if (!tableName || !dbToolCpp || typeof dbToolCpp.getDescriptionForTable !== "function") {
            return "";
        }
        
        try {
            // 检查缓存
            if (_descriptionCache[tableName]) {
                console.log("从内存缓存获取表", tableName, "的解析规则描述");
                return _descriptionCache[tableName];
            }
            
            // 调用C++方法获取描述
            var description = dbToolCpp.getDescriptionForTable(tableName);
            
            // 保存到缓存
            _descriptionCache[tableName] = description;
            
            return description;
        } catch (e) {
            console.error("获取解析规则描述时出错:", e);
            return "";
        }
    }

    // 监听termDiskPath变化，自动连接默认数据库
    onTermDiskPathChanged: {
        console.log("DatabaseToolView - termDiskPath变化:", termDiskPath);

        if (termDiskPath && termDiskPath.length > 0 && dbToolCpp) {
            // 构建默认数据库路径
            var defaultDbPath = termDiskPath + "/data0/record.db3";
            console.log("尝试自动连接默认数据库:", defaultDbPath);

            // 检查文件是否存在
            if (dbToolCpp.checkFileExists(defaultDbPath)) {
                console.log("找到默认数据库文件，自动连接");

                // 如果已经连接到数据库，先断开连接
                if (isConnected) {
                    console.log("断开当前数据库连接");
                    dbToolCpp.disconnectFromDatabase();
                    isConnected = false;
                    selectedTable = null;
                    tableData = [];

                    // 清空表格数据
                    tablesListModel.clearTables();
                    headerModel.clear();
                    dataModel.clear();
                }

                // 设置数据库路径并连接
                dbToolCpp.setDatabasePath(defaultDbPath);
                connectToDatabase();
            } else {
                console.log("默认数据库文件不存在:", defaultDbPath);
            }
        }
    }

    // 监听dbToolCpp属性变化
    onDbToolCppChanged: {
        if (dbToolCpp) {
            console.log("dbToolCpp对象已设置，连接信号");

            // 连接解析规则加载完成信号
            dbToolCpp.parseRulesLoaded.connect(function(success) {
                parseRulesLoaded = success;
                console.log("解析规则加载" + (success ? "成功" : "失败"));
            });

            // 如果termDiskPath已经存在，尝试自动连接
            if (termDiskPath && termDiskPath.length > 0) {
                Qt.callLater(function() {
                    var defaultDbPath = termDiskPath + "/data0/record.db3";
                    console.log("dbToolCpp设置后，尝试自动连接默认数据库:", defaultDbPath);

                    if (dbToolCpp.checkFileExists(defaultDbPath)) {
                        console.log("找到默认数据库文件，自动连接");
                        dbToolCpp.setDatabasePath(defaultDbPath);
                        connectToDatabase();
                    }
                });
            }

            // 不在这里立即加载解析规则，改为按需加载
        }
    }

    // 添加脚本右键菜单
    Menu {
        id: scriptContextMenu
        property string scriptName: ""
        
        topPadding: 4
        bottomPadding: 4
        
        delegate: MenuItem {
            id: menuItem
            implicitHeight: 32
            
            background: Rectangle {
                implicitWidth: 160
                color: menuItem.highlighted ? "#e8f0fe" : "transparent"
                radius: 4
            }
            
            contentItem: Row {
                spacing: 8
                leftPadding: 8
                
                // 图标
                Text {
                    text: "🗑️"
                    font.pixelSize: 14
                    color: "#f44336"
                    anchors.verticalCenter: parent.verticalCenter
                }
                
                Text {
                    text: menuItem.text
                    font.pixelSize: 13
                    color: "#333333"
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }
        
        background: Rectangle {
            implicitWidth: 160
            color: "#ffffff"
            border.color: "#dddddd"
            radius: 6
            layer.enabled: true
            layer.effect: DropShadow {
                transparentBorder: true
                horizontalOffset: 0
                verticalOffset: 2
                radius: 8.0
                samples: 17
                color: "#20000000"
            }
        }
        
        MenuItem {
            text: "删除"
            onTriggered: {
                if (scriptContextMenu.scriptName) {
                    deleteScriptConfirmDialog.scriptName = scriptContextMenu.scriptName;
                    deleteScriptConfirmDialog.open();
                }
            }
        }
    }
    
    // 删除脚本确认对话框
    MessageDialog {
        id: deleteScriptConfirmDialog
        title: "确认删除"
        text: "确定要删除脚本 '" + scriptName + "' 吗？"
        standardButtons: StandardButton.Yes | StandardButton.No
        property string scriptName: ""
        
        onYes: {
            deleteScript(scriptName);
        }
    }
    
    // 删除脚本函数
    function deleteScript(scriptName) {
        if (!scriptName || scriptName.trim() === "") {
            return;
        }
        
        try {
            // 构建脚本文件路径
            var scriptPath = "tools/user_sql/" + scriptName;
            console.log("删除脚本:", scriptPath);
            
            // 调用C++函数删除文件
            if (dbToolCpp && typeof dbToolCpp.deleteFile === "function") {
                var success = dbToolCpp.deleteFile(scriptPath);
                if (success) {
                    console.log("脚本删除成功");
                    // 重新扫描SQL脚本文件
                    scanSqlScripts();
                } else {
                    showError("删除脚本失败");
                }
            } else {
                showError("删除文件功能未实现");
            }
        } catch (e) {
            console.error("删除脚本时发生异常:", e);
            showError("删除脚本时发生异常: " + e);
        }
    }

    // 确保分页控件存在
    function ensurePaginationControlExists() {
        // 如果分页控件已存在，直接返回
        if (newPaginationControl) return;
        
        // 动态创建分页控件
        var paginationCode = `
            Rectangle {
                id: newPaginationControl
                width: parent.width
                height: 40
                anchors.bottom: parent.bottom
                anchors.horizontalCenter: parent.horizontalCenter
                color: "transparent"
                visible: false
                
                Row {
                    anchors.centerIn: parent
                    spacing: 10
                    
                    // 首页按钮
                    Rectangle {
                        width: 30
                        height: 30
                        radius: 3
                        color: newFirstPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                        border.color: "#80cccccc"
                        
                        Text {
                            anchors.centerIn: parent
                            text: "<<"
                            font.pixelSize: 12
                            color: "#000000"
                            font.bold: true
                        }
                        
                        MouseArea {
                            id: newFirstPageMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                                loadFirstPage();
                            }
                        }
                    }
                    
                    // 上一页按钮
                    Rectangle {
                        width: 30
                        height: 30
                        radius: 3
                        color: newPrevPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                        border.color: "#80cccccc"
                        
                        Text {
                            anchors.centerIn: parent
                            text: "<"
                            font.pixelSize: 12
                            color: "#000000"
                            font.bold: true
                        }
                        
                        MouseArea {
                            id: newPrevPageMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                                loadPrevPage();
                            }
                        }
                    }
                    
                    // 页码信息
                    Rectangle {
                        width: 200
                        height: 30
                        radius: 3
                        color: "#80f5f5f5"
                        border.color: "#80cccccc"
                        
                        Text {
                            anchors.centerIn: parent
                            text: {
                                var totalPages = Math.ceil(totalRecords / pageSize) || 1;
                                return "第 " + (currentPage + 1) + " / " + totalPages + " 页，共 " + totalRecords + " 条记录";
                            }
                            font.pixelSize: 12
                            color: "#000000"
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                        }
                    }
                    
                    // 下一页按钮
                    Rectangle {
                        width: 30
                        height: 30
                        radius: 3
                        color: newNextPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                        border.color: "#80cccccc"
                        
                        Text {
                            anchors.centerIn: parent
                            text: ">"
                            font.pixelSize: 12
                            color: "#000000"
                            font.bold: true
                        }
                        
                        MouseArea {
                            id: newNextPageMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                                loadNextPage();
                            }
                        }
                    }
                    
                    // 末页按钮
                    Rectangle {
                        width: 30
                        height: 30
                        radius: 3
                        color: newLastPageMouseArea.containsMouse ? "#80e0e0e0" : "#80f5f5f5"
                        border.color: "#80cccccc"
                        
                        Text {
                            anchors.centerIn: parent
                            text: ">>"
                            font.pixelSize: 12
                            color: "#000000"
                            font.bold: true
                        }
                        
                        MouseArea {
                            id: newLastPageMouseArea
                            anchors.fill: parent
                            hoverEnabled: true
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                                loadLastPage();
                            }
                        }
                    }
                }
            }
        `;
        
        try {
            // 在表格区域内创建分页控件
            var container = simpleTableView;
            var component = Qt.createQmlObject(paginationCode, container, "dynamicPagination");
            newPaginationControl = component; // 存储创建的分页控件引用
            console.log("分页控件已动态创建");
        } catch (e) {
            console.error("创建分页控件时出错:", e);
        }
    }

    // 应用表的解析规则
    function applyParseRuleForTable(tableName) {
        if (!tableName || !dbToolCpp || typeof dbToolCpp.getParseRuleForTable !== "function") {
            return;
        }
        
        // 获取解析规则 - 只调用一次C++方法
        var parseRule = dbToolCpp.getParseRuleForTable(tableName);
        if (parseRule && parseRule.length > 0) {
            console.log("自动应用表", tableName, "的解析规则:", parseRule);
            
            // 设置解析规则输入框
            if (batchParseRuleInput) {
                // 设置标志，避免触发onTextChanged处理
                batchParseRuleInput.ignoreTextChange = true;
                batchParseRuleInput.text = parseRule;
                
                // 手动应用解析规则
                applyBatchParseRule(parseRule);
                
                // 重置标志
                batchParseRuleInput.ignoreTextChange = false;
            }
        } else {
            console.log("未找到表", tableName, "的默认解析规则，清空解析规则输入框");
            // 清空解析规则输入框
            if (batchParseRuleInput) {
                // 设置标志，避免触发onTextChanged处理
                batchParseRuleInput.ignoreTextChange = true;
                batchParseRuleInput.text = "";
                batchParseRuleInput.ignoreTextChange = false;
            }
            // 清空所有解析结果
            clearAllParseResults();
        }
    }
}
