#include "nwupparser.h"
#include <QDebug>

NWUPParser::NWUPParser(QObject *parent)
    : QObject(parent)
    , m_dataConfig(DataIdentifierConfig::instance())
{
    initializeMappings();
}

NWUPParser::~NWUPParser()
{
}

void NWUPParser::setDataConfig(DataIdentifierConfig* config)
{
    m_dataConfig = config;
}

void NWUPParser::initializeMappings()
{
    // 初始化链路层功能码映射 (PRM=1)
    m_functionCodeMap[0] = "备用";
    m_functionCodeMap[1] = "复位命令";
    m_functionCodeMap[2] = "备用";
    m_functionCodeMap[3] = "备用";
    m_functionCodeMap[4] = "用户数据";
    m_functionCodeMap[5] = "备用";
    m_functionCodeMap[6] = "备用";
    m_functionCodeMap[7] = "备用";
    m_functionCodeMap[8] = "备用";
    m_functionCodeMap[9] = "链路测试";
    m_functionCodeMap[10] = "请求1级数据";
    m_functionCodeMap[11] = "请求2级数据";
    m_functionCodeMap[12] = "备用";
    m_functionCodeMap[13] = "备用";
    m_functionCodeMap[14] = "备用";
    m_functionCodeMap[15] = "备用";

    // 初始化应用层功能码映射
    m_applicationFunctionMap[0x00] = "确认/否定";
    m_applicationFunctionMap[0x02] = "链路接口检测";
    m_applicationFunctionMap[0x04] = "写参数";
    m_applicationFunctionMap[0x06] = "身份认证";
    m_applicationFunctionMap[0x0A] = "读参数";
    m_applicationFunctionMap[0x0C] = "读当前数据";
    m_applicationFunctionMap[0x0D] = "读历史数据";
    m_applicationFunctionMap[0x0E] = "读事件记录";
    m_applicationFunctionMap[0x0F] = "文件传输";
    m_applicationFunctionMap[0x10] = "中继转发";
    m_applicationFunctionMap[0x12] = "读任务数据";
    m_applicationFunctionMap[0x13] = "读告警数据";
    m_applicationFunctionMap[0x14] = "级联命令";
    m_applicationFunctionMap[0x15] = "用户自定义数据";
    m_applicationFunctionMap[0x16] = "数据安全传输";
    m_applicationFunctionMap[0x17] = "数据转发加密";
}

ProtocolParseResult NWUPParser::parseFrame(const QByteArray &frameData)
{
    if (frameData.isEmpty()) {
        return createErrorResult("帧数据为空");
    }

    if (frameData.size() < 12) {
        return createErrorResult("帧数据长度不足，最小需要12字节");
    }

    // 验证帧格式
    if (!validateFrame(frameData)) {
        return createErrorResult("帧格式验证失败");
    }

    QVariantMap parsedData;
    QString summary;
    QString detailInfo;

    try {
        // 解析帧结构
        parsedData["frameLength"] = frameData.size();
        parsedData["rawFrame"] = formatHexString(frameData);

        // 1. 解析帧头 (68H L L 68H)
        QVariantMap frameHeader = parseFrameHeader(frameData);
        parsedData["frameHeader"] = frameHeader;

        // 2. 解析控制域
        quint8 controlByte = static_cast<quint8>(frameData.at(8));
        QVariantMap controlInfo = parseControlField(controlByte);
        parsedData["controlField"] = controlInfo;

        // 3. 解析地址域 (7字节)
        QByteArray addressData = frameData.mid(9, 7);
        QVariantMap addressInfo = parseAddressField(addressData);
        parsedData["addressField"] = addressInfo;

        // 4. 解析应用层数据
        int userDataLength = frameHeader["userDataLength"].toInt();
        if (userDataLength > 8) { // 控制域1字节 + 地址域7字节 = 8字节
            int appDataLength = userDataLength - 8;
            QByteArray appData = frameData.mid(16, appDataLength);
            QVariantMap appInfo = parseApplicationLayer(appData);
            parsedData["applicationLayer"] = appInfo;

            // 生成摘要信息
            summary = QString("南网上行协议 地址:%1 功能:%2 应用功能:%3")
                      .arg(addressInfo["terminalAddress"].toString())
                      .arg(controlInfo["functionDescription"].toString())
                      .arg(appInfo["afnDescription"].toString());
        } else {
            summary = QString("南网上行协议 地址:%1 功能:%2")
                      .arg(addressInfo["terminalAddress"].toString())
                      .arg(controlInfo["functionDescription"].toString());
        }

        // 5. 验证校验和
        bool checksumValid = verifyChecksum(frameData);
        parsedData["checksumValid"] = checksumValid;

        // 生成详细信息
        detailInfo = QString("帧长度: %1字节\n用户数据长度: %2字节\n地址域: %3\n控制域: %4\n校验和: %5")
                     .arg(frameData.size())
                     .arg(userDataLength)
                     .arg(addressInfo["description"].toString())
                     .arg(controlInfo["description"].toString())
                     .arg(checksumValid ? "正确" : "错误");

        return createSuccessResult(parsedData, summary, detailInfo);

    } catch (const std::exception &e) {
        return createErrorResult(QString("解析异常: %1").arg(e.what()));
    } catch (...) {
        return createErrorResult("未知解析异常");
    }
}

bool NWUPParser::validateFrame(const QByteArray &frameData)
{
    if (frameData.size() < 12) {
        return false;
    }

    // 检查起始字符 68H
    if (static_cast<quint8>(frameData.at(0)) != 0x68) {
        return false;
    }

    // 检查第二个起始字符 68H
    if (static_cast<quint8>(frameData.at(3)) != 0x68) {
        return false;
    }

    // 检查长度域是否一致
    quint8 length1 = static_cast<quint8>(frameData.at(1));
    quint8 length2 = static_cast<quint8>(frameData.at(2));
    if (length1 != length2) {
        return false;
    }

    // 检查结束字符 16H
    if (static_cast<quint8>(frameData.at(frameData.size() - 1)) != 0x16) {
        return false;
    }

    // 检查帧长度是否符合长度域定义
    int expectedLength = 6 + length1; // 固定头部6字节 + 用户数据长度 + 校验和1字节 + 结束符1字节
    if (frameData.size() != expectedLength) {
        return false;
    }

    return true;
}

QVariantMap NWUPParser::parseFrameHeader(const QByteArray &frameData)
{
    QVariantMap header;

    // 起始字符1
    quint8 start1 = static_cast<quint8>(frameData.at(0));
    header["startChar1"] = QString("0x%1").arg(start1, 2, 16, QChar('0')).toUpper();

    // 长度域
    QByteArray lengthData = frameData.mid(1, 2);
    QVariantMap lengthInfo = parseLengthField(lengthData);
    header["lengthField"] = lengthInfo;
    header["userDataLength"] = lengthInfo["length"].toInt();

    // 起始字符2
    quint8 start2 = static_cast<quint8>(frameData.at(3));
    header["startChar2"] = QString("0x%1").arg(start2, 2, 16, QChar('0')).toUpper();

    header["valid"] = (start1 == 0x68 && start2 == 0x68);

    return header;
}

QVariantMap NWUPParser::parseLengthField(const QByteArray &lengthData)
{
    QVariantMap lengthInfo;

    if (lengthData.size() < 2) {
        lengthInfo["valid"] = false;
        lengthInfo["error"] = "长度域数据不足";
        return lengthInfo;
    }

    quint8 length1 = static_cast<quint8>(lengthData.at(0));
    quint8 length2 = static_cast<quint8>(lengthData.at(1));

    lengthInfo["length1"] = length1;
    lengthInfo["length2"] = length2;
    lengthInfo["length"] = length1;
    lengthInfo["valid"] = (length1 == length2);
    lengthInfo["hex"] = formatHexString(lengthData);

    if (length1 != length2) {
        lengthInfo["error"] = "两个长度域不一致";
    }

    return lengthInfo;
}

QVariantMap NWUPParser::parseControlField(quint8 controlData)
{
    QVariantMap controlInfo;

    controlInfo["raw"] = QString("0x%1").arg(controlData, 2, 16, QChar('0')).toUpper();
    controlInfo["binary"] = QString("%1").arg(controlData, 8, 2, QChar('0'));

    // 解析各个位
    bool dir = (controlData & 0x80) != 0;  // D7位：传输方向位
    bool prm = (controlData & 0x40) != 0;  // D6位：启动标志位
    bool fcb_acd = (controlData & 0x20) != 0;  // D5位：帧计数位FCB/要求访问位ACD
    bool fcv = (controlData & 0x10) != 0;  // D4位：帧计数有效位FCV
    quint8 functionCode = controlData & 0x0F;  // D3-D0位：功能码

    controlInfo["dir"] = dir;
    controlInfo["prm"] = prm;
    controlInfo["fcb_acd"] = fcb_acd;
    controlInfo["fcv"] = fcv;
    controlInfo["functionCode"] = functionCode;

    // 方向描述
    controlInfo["dirDescription"] = dir ? "上行(终端->主站)" : "下行(主站->终端)";
    controlInfo["prmDescription"] = prm ? "启动站" : "从动站";

    if (dir) {
        // 上行报文
        controlInfo["fcb_acd_description"] = fcb_acd ? "有告警数据等待访问" : "无告警数据等待访问";
        controlInfo["fcv_description"] = "保留";
    } else {
        // 下行报文
        controlInfo["fcb_acd_description"] = QString("帧计数位FCB=%1").arg(fcb_acd ? 1 : 0);
        controlInfo["fcv_description"] = fcv ? "FCB位有效" : "FCB位无效";
    }

    // 功能码描述
    QString funcDesc = getFunctionDescription(functionCode);
    controlInfo["functionDescription"] = funcDesc;

    controlInfo["description"] = QString("DIR=%1 PRM=%2 FCB/ACD=%3 FCV=%4 功能码=%5(%6)")
                                 .arg(dir ? 1 : 0)
                                 .arg(prm ? 1 : 0)
                                 .arg(fcb_acd ? 1 : 0)
                                 .arg(fcv ? 1 : 0)
                                 .arg(functionCode)
                                 .arg(funcDesc);

    return controlInfo;
}

QVariantMap NWUPParser::parseAddressField(const QByteArray &addressData)
{
    QVariantMap addressInfo;

    if (addressData.size() < 7) {
        addressInfo["valid"] = false;
        addressInfo["error"] = "地址域数据不足，需要7字节";
        return addressInfo;
    }

    addressInfo["raw"] = formatHexString(addressData);

    // 解析省地市区县码A1 (3字节BCD，低字节在前)
    QByteArray a1Data = addressData.mid(0, 3);
    QString a1Hex = formatHexString(a1Data, "");
    // 按低字节在前的顺序解析
    QString countyCode = QString("%1").arg(static_cast<quint8>(a1Data.at(0)), 2, 16, QChar('0')).toUpper();
    QString cityCode = QString("%1").arg(static_cast<quint8>(a1Data.at(1)), 2, 16, QChar('0')).toUpper();
    QString provinceCode = QString("%1").arg(static_cast<quint8>(a1Data.at(2)), 2, 16, QChar('0')).toUpper();
    
    addressInfo["provinceCode"] = provinceCode;
    addressInfo["cityCode"] = cityCode;
    addressInfo["countyCode"] = countyCode;
    addressInfo["areaCode"] = provinceCode + cityCode + countyCode;

    // 解析终端地址A2 (3字节BIN，低字节在前)
    QByteArray a2Data = addressData.mid(3, 3);
    quint32 terminalAddr = static_cast<quint8>(a2Data.at(0)) | 
                          (static_cast<quint8>(a2Data.at(1)) << 8) |
                          (static_cast<quint8>(a2Data.at(2)) << 16);
    
    addressInfo["terminalAddress"] = QString::number(terminalAddr);
    addressInfo["terminalAddressHex"] = formatHexString(a2Data);

    // 解析主站地址A3 (1字节BIN)
    quint8 masterAddr = static_cast<quint8>(addressData.at(6));
    addressInfo["masterAddress"] = masterAddr;
    addressInfo["masterAddressHex"] = QString("0x%1").arg(masterAddr, 2, 16, QChar('0')).toUpper();

    // 特殊地址判断
    if (terminalAddr == 0x000000) {
        addressInfo["terminalAddressType"] = "无效地址";
    } else if (terminalAddr == 0xFFFFFF) {
        addressInfo["terminalAddressType"] = "系统广播地址";
    } else {
        addressInfo["terminalAddressType"] = "正常地址";
    }

    addressInfo["description"] = QString("区域码:%1 终端地址:%2 主站地址:%3")
                                .arg(addressInfo["areaCode"].toString())
                                .arg(terminalAddr)
                                .arg(masterAddr);

    addressInfo["valid"] = true;
    return addressInfo;
}

QVariantMap NWUPParser::parseApplicationLayer(const QByteArray &appData)
{
    QVariantMap appInfo;

    if (appData.isEmpty()) {
        appInfo["valid"] = false;
        appInfo["error"] = "应用层数据为空";
        return appInfo;
    }

    appInfo["raw"] = formatHexString(appData);
    appInfo["length"] = appData.size();

    // 解析应用层功能码AFN
    quint8 afn = static_cast<quint8>(appData.at(0));
    QVariantMap afnInfo = parseApplicationFunctionCode(afn);
    appInfo["afn"] = afnInfo;
    appInfo["afnDescription"] = afnInfo["description"].toString();

    // 解析帧序列域SEQ
    if (appData.size() > 1) {
        quint8 seq = static_cast<quint8>(appData.at(1));
        QVariantMap seqInfo = parseSequenceField(seq);
        appInfo["seq"] = seqInfo;
    }

    // 根据AFN解析具体的应用层内容
    if (appData.size() > 2) {
        QByteArray specificData = appData.mid(2);
        QVariantMap specificInfo = parseSpecificFunction(afn, specificData);
        appInfo["specificData"] = specificInfo;
    }

    appInfo["valid"] = true;
    return appInfo;
}

QVariantMap NWUPParser::parseApplicationFunctionCode(quint8 afn)
{
    QVariantMap afnInfo;

    afnInfo["code"] = afn;
    afnInfo["hex"] = QString("0x%1").arg(afn, 2, 16, QChar('0')).toUpper();
    
    QString description = getApplicationFunctionDescription(afn);
    afnInfo["description"] = description;

    return afnInfo;
}

QVariantMap NWUPParser::parseSequenceField(quint8 seqData)
{
    QVariantMap seqInfo;

    seqInfo["raw"] = QString("0x%1").arg(seqData, 2, 16, QChar('0')).toUpper();
    seqInfo["binary"] = QString("%1").arg(seqData, 8, 2, QChar('0'));

    // 解析各个位
    bool tpv = (seqData & 0x80) != 0;   // D7位：帧时间标签有效位
    bool fir = (seqData & 0x40) != 0;   // D6位：首帧标志
    bool fin = (seqData & 0x20) != 0;   // D5位：末帧标志
    bool con = (seqData & 0x10) != 0;   // D4位：请求确认标志
    quint8 pseq = seqData & 0x0F;       // D3-D0位：帧序列号

    seqInfo["tpv"] = tpv;
    seqInfo["fir"] = fir;
    seqInfo["fin"] = fin;
    seqInfo["con"] = con;
    seqInfo["pseq"] = pseq;

    seqInfo["tpvDescription"] = tpv ? "带时间标签" : "无时间标签";
    seqInfo["conDescription"] = con ? "请求确认" : "无需确认";

    // 帧类型判断
    QString frameType;
    if (fir && fin) {
        frameType = "单帧";
    } else if (fir && !fin) {
        frameType = "多帧：第1帧";
    } else if (!fir && fin) {
        frameType = "多帧：结束帧";
    } else {
        frameType = "多帧：中间帧";
    }
    seqInfo["frameType"] = frameType;

    seqInfo["description"] = QString("TpV=%1 %2 CON=%3 PSEQ=%4")
                            .arg(tpv ? 1 : 0)
                            .arg(frameType)
                            .arg(con ? 1 : 0)
                            .arg(pseq);

    return seqInfo;
}

QString NWUPParser::getFunctionDescription(quint8 functionCode)
{
    return m_functionCodeMap.value(functionCode, QString("未知功能码(%1)").arg(functionCode));
}

QString NWUPParser::getApplicationFunctionDescription(quint8 afn)
{
    return m_applicationFunctionMap.value(afn, QString("未知应用功能码(0x%1)").arg(afn, 2, 16, QChar('0')).toUpper());
}

QString NWUPParser::formatHexString(const QByteArray &data, const QString &separator)
{
    QString result;
    for (int i = 0; i < data.size(); ++i) {
        if (i > 0 && !separator.isEmpty()) {
            result += separator;
        }
        result += QString("%1").arg(static_cast<quint8>(data.at(i)), 2, 16, QChar('0')).toUpper();
    }
    return result;
}

quint8 NWUPParser::calculateChecksum(const QByteArray &data)
{
    quint8 checksum = 0;
    for (int i = 0; i < data.size(); ++i) {
        checksum += static_cast<quint8>(data.at(i));
    }
    return checksum;
}

bool NWUPParser::verifyChecksum(const QByteArray &frameData)
{
    if (frameData.size() < 3) {
        return false;
    }

    // 校验和覆盖从控制域开始到数据内容结束的所有字节
    QByteArray dataToCheck = frameData.mid(4, frameData.size() - 6); // 从第5个字节到倒数第2个字节
    quint8 calculatedCS = calculateChecksum(dataToCheck);
    quint8 frameCS = static_cast<quint8>(frameData.at(frameData.size() - 2));

    return calculatedCS == frameCS;
}

ProtocolParseResult NWUPParser::createErrorResult(const QString &errorMessage)
{
    ProtocolParseResult result;
    result.isValid = false;
    result.protocolType = ProtocolType::NW_UP;
    result.protocolName = "南网上行协议";
    result.errorMessage = errorMessage;
    return result;
}

ProtocolParseResult NWUPParser::createSuccessResult(const QVariantMap &parsedData,
                                                   const QString &summary,
                                                   const QString &detailInfo)
{
    ProtocolParseResult result;
    result.isValid = true;
    result.protocolType = ProtocolType::NW_UP;
    result.protocolName = "南网上行协议";
    result.parsedData = parsedData;
    result.summary = summary;
    result.detailInfo = detailInfo;
    return result;
}

QVariantMap NWUPParser::parseSpecificFunction(quint8 afn, const QByteArray &appData)
{
    QVariantMap result;

    switch (afn) {
    case 0x00:
        return parseConfirmDenyMessage(appData);
    case 0x02:
        return parseLinkInterfaceTest(appData);
    case 0x04:
        return parseWriteParameter(appData);
    case 0x06:
        return parseSecurityAuth(appData);
    case 0x0A:
        return parseReadParameter(appData);
    case 0x0C:
        return parseReadCurrentData(appData);
    case 0x0D:
        return parseReadHistoryData(appData);
    case 0x0E:
        return parseReadEventRecord(appData);
    case 0x0F:
        return parseFileTransfer(appData);
    case 0x10:
        return parseRelayForward(appData);
    case 0x12:
        return parseReadTaskData(appData);
    case 0x13:
        return parseReadAlarmData(appData);
    case 0x15:
        return parseUserCustomData(appData);
    case 0x16:
        return parseDataSecurityTransfer(appData);
    case 0x17:
        return parseDataEncryption(appData);
    default:
        result["type"] = "未支持的功能码";
        result["data"] = formatHexString(appData);
        break;
    }

    return result;
}

// 实现具体功能码解析方法
QVariantMap NWUPParser::parseConfirmDenyMessage(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "确认/否定报文";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 2) {
        result["error"] = "数据长度不足";
        return result;
    }
    
    // 解析AFN和SEQ已经在上层完成，这里从信息体开始解析
    int offset = 0;
    QVariantList dataItems;
    
    while (offset + 7 <= appData.size()) { // DA(2) + DI(4) + 内容(1) = 7字节最小
        QVariantMap item;
        
        // 解析信息点标识DA (2字节)
        QByteArray daData = appData.mid(offset, 2);
        QVariantMap daInfo = parseDataAddress(daData);
        item["dataAddress"] = daInfo;
        offset += 2;
        
        // 解析数据标识编码DI (4字节)
        QByteArray diData = appData.mid(offset, 4);
        QVariantMap diInfo = parseDataIdentifier(diData);
        item["dataIdentifier"] = diInfo;
        offset += 4;
        
        // 解析确认/否定内容 (1字节)
        if (offset < appData.size()) {
            quint8 confirmFlag = static_cast<quint8>(appData.at(offset));
            item["confirmFlag"] = confirmFlag;
            item["confirmDescription"] = (confirmFlag == 0x00) ? "确认" : "否定";
            offset += 1;
        }
        
        dataItems.append(item);
        
        // 检查是否还有更多数据项
        if (offset >= appData.size()) {
            break;
        }
    }
    
    result["dataItems"] = dataItems;
    result["itemCount"] = dataItems.size();
    
    return result;
}

QVariantMap NWUPParser::parseLinkInterfaceTest(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "链路接口检测";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 6) {
        result["error"] = "数据长度不足";
        return result;
    }
    
    int offset = 0;
    
    // 解析信息点标识DA (2字节，通常为0000H)
    QByteArray daData = appData.mid(offset, 2);
    QVariantMap daInfo = parseDataAddress(daData);
    result["dataAddress"] = daInfo;
    offset += 2;
    
    // 解析数据标识编码DI (4字节)
    QByteArray diData = appData.mid(offset, 4);
    QVariantMap diInfo = parseDataIdentifier(diData);
    result["dataIdentifier"] = diInfo;
    offset += 4;
    
    // 解析数据标识内容
    if (offset < appData.size()) {
        QByteArray contentData = appData.mid(offset);
        quint32 dataId = diInfo["dataId"].toUInt();
        
        // 统一使用配置文件解析数据内容
        QVariantMap contentResult = parseDataContent(contentData, dataId, 0x02);
        result["contentData"] = contentResult;
    }
    
    return result;
}



QVariantMap NWUPParser::parseReadCurrentData(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "读当前数据";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 6) {
        result["error"] = "数据长度不足";
        return result;
    }
    
    int offset = 0;
    QVariantList dataItems;
    
    // 解析多个数据项，每个数据项包含DA + DI + 数据内容（上行）或仅DA + DI（下行）
    while (offset + 6 <= appData.size()) { // 至少需要DA(2) + DI(4) = 6字节
        QVariantMap item;
        
        // 解析信息点标识DA (2字节)
        QByteArray daData = appData.mid(offset, 2);
        QVariantMap daInfo = parseDataAddress(daData);
        item["dataAddress"] = daInfo;
        offset += 2;
        
        // 解析数据标识编码DI (4字节)
        QByteArray diData = appData.mid(offset, 4);
        QVariantMap diInfo = parseDataIdentifier(diData);
        item["dataIdentifier"] = diInfo;
        offset += 4;
        
        // 判断是否有数据内容（上行报文会有数据内容）
        quint32 dataId = diInfo["dataId"].toUInt();
        
        // 根据数据标识获取预期的数据长度
        int expectedDataLength = getDataLengthByDataId(dataId);
        
        if (expectedDataLength > 0 && offset + expectedDataLength <= appData.size()) {
            // 解析数据内容
            QByteArray contentData = appData.mid(offset, expectedDataLength);
            QVariantMap contentResult = parseDataContent(contentData, dataId, 0x0C);
            item["dataContent"] = contentResult;
            offset += expectedDataLength;
        }
        
        dataItems.append(item);
        
        // 检查是否还有更多数据项
        if (offset >= appData.size()) {
            break;
        }
    }
    
    result["dataItems"] = dataItems;
    result["itemCount"] = dataItems.size();
    
    return result;
}

int NWUPParser::getDataLengthByDataId(quint32 dataId)
{
    // 完全依赖配置文件获取数据长度
    if (m_dataConfig) {
        QString dataIdStr = QString("%1").arg(dataId, 8, 16, QChar('0')).toUpper();
        DataItemConfig dataItem = m_dataConfig->getDataItemConfig(dataIdStr.toUInt(nullptr, 16), ProtocolType::NW_UP);
        if (dataItem.length > 0) {
            return dataItem.length;
        }
    }
    
    // 如果配置文件中没有找到，返回0表示未知长度
    return 0;
}

QVariantMap NWUPParser::parseWriteParameter(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "写参数";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 6) {
        result["error"] = "数据长度不足";
        return result;
    }
    
    int offset = 0;
    QVariantList dataItems;
    
    // 写参数报文格式：多个 DA + DI + 数据内容 或 DA + DI + ERR
    while (offset + 6 <= appData.size()) {
        QVariantMap item;
        
        // 解析信息点标识DA (2字节)
        QByteArray daData = appData.mid(offset, 2);
        QVariantMap daInfo = parseDataAddress(daData);
        item["dataAddress"] = daInfo;
        offset += 2;
        
        // 解析数据标识编码DI (4字节)
        QByteArray diData = appData.mid(offset, 4);
        QVariantMap diInfo = parseDataIdentifier(diData);
        item["dataIdentifier"] = diInfo;
        offset += 4;
        
        // 根据数据标识获取预期的数据长度
        quint32 dataId = diInfo["dataId"].toUInt();
        int expectedDataLength = getDataLengthByDataId(dataId);
        
        if (expectedDataLength > 0 && offset + expectedDataLength <= appData.size()) {
            // 下行报文：解析参数数据内容
            QByteArray contentData = appData.mid(offset, expectedDataLength);
            QVariantMap contentResult = parseDataContent(contentData, dataId, 0x04);
            item["parameterData"] = contentResult;
            offset += expectedDataLength;
        } else if (offset + 1 <= appData.size()) {
            // 上行报文：解析错误码
            quint8 errorCode = static_cast<quint8>(appData.at(offset));
            item["errorCode"] = errorCode;
            item["errorDescription"] = parseErrorCode(errorCode)["description"].toString();
            offset += 1;
        }
        
        dataItems.append(item);
        
        if (offset >= appData.size()) {
            break;
        }
    }
    
    result["dataItems"] = dataItems;
    result["itemCount"] = dataItems.size();
    
    return result;
}

QVariantMap NWUPParser::parseSecurityAuth(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "安全认证";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 6) {
        result["error"] = "数据长度不足";
        return result;
    }
    
    int offset = 0;
    
    // 解析信息点标识DA (2字节，通常为0000H)
    QByteArray daData = appData.mid(offset, 2);
    QVariantMap daInfo = parseDataAddress(daData);
    result["dataAddress"] = daInfo;
    offset += 2;
    
    // 解析数据标识编码DI (4字节)
    QByteArray diData = appData.mid(offset, 4);
    QVariantMap diInfo = parseDataIdentifier(diData);
    result["dataIdentifier"] = diInfo;
    offset += 4;
    
    // 解析认证数据内容
    if (offset < appData.size()) {
        QByteArray contentData = appData.mid(offset);
        quint32 dataId = diInfo["dataId"].toUInt();
        QVariantMap contentResult = parseDataContent(contentData, dataId, 0x06);
        result["authData"] = contentResult;
    }
    
    return result;
}

QVariantMap NWUPParser::parseReadParameter(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "读参数";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 6) {
        result["error"] = "数据长度不足";
        return result;
    }
    
    int offset = 0;
    QVariantList dataItems;
    
    // 读参数报文格式类似读当前数据
    while (offset + 6 <= appData.size()) {
        QVariantMap item;
        
        // 解析信息点标识DA (2字节)
        QByteArray daData = appData.mid(offset, 2);
        QVariantMap daInfo = parseDataAddress(daData);
        item["dataAddress"] = daInfo;
        offset += 2;
        
        // 解析数据标识编码DI (4字节)
        QByteArray diData = appData.mid(offset, 4);
        QVariantMap diInfo = parseDataIdentifier(diData);
        item["dataIdentifier"] = diInfo;
        offset += 4;
        
        // 判断是否有参数数据内容（上行报文会有）
        quint32 dataId = diInfo["dataId"].toUInt();
        int expectedDataLength = getDataLengthByDataId(dataId);
        
        if (expectedDataLength > 0 && offset + expectedDataLength <= appData.size()) {
            QByteArray contentData = appData.mid(offset, expectedDataLength);
            QVariantMap contentResult = parseDataContent(contentData, dataId, 0x0A);
            item["parameterValue"] = contentResult;
            offset += expectedDataLength;
        }
        
        dataItems.append(item);
        
        if (offset >= appData.size()) {
            break;
        }
    }
    
    result["dataItems"] = dataItems;
    result["itemCount"] = dataItems.size();
    
    return result;
}

QVariantMap NWUPParser::parseReadHistoryData(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "读历史数据";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 17) { // DA(2) + DI(4) + 起始时间(5) + 结束时间(5) + 密度(1) = 17字节最小
        result["error"] = "数据长度不足";
        return result;
    }
    
    int offset = 0;
    QVariantList dataItems;
    
    while (offset + 17 <= appData.size()) {
        QVariantMap item;
        
        // 解析信息点标识DA (2字节)
        QByteArray daData = appData.mid(offset, 2);
        QVariantMap daInfo = parseDataAddress(daData);
        item["dataAddress"] = daInfo;
        offset += 2;
        
        // 解析数据标识编码DI (4字节)
        QByteArray diData = appData.mid(offset, 4);
        QVariantMap diInfo = parseDataIdentifier(diData);
        item["dataIdentifier"] = diInfo;
        offset += 4;
        
        // 检查是否有时间域（下行报文有，上行报文可能没有）
        if (offset + 11 <= appData.size()) {
            // 解析数据起始时间 (5字节)
            QByteArray startTimeData = appData.mid(offset, 5);
            QString startTime = parseTimeData(startTimeData);
            item["startTime"] = startTime;
            offset += 5;
            
            // 解析数据结束时间 (5字节)
            QByteArray endTimeData = appData.mid(offset, 5);
            QString endTime = parseTimeData(endTimeData);
            item["endTime"] = endTime;
            offset += 5;
            
            // 解析数据密度 (1字节)
            quint8 density = static_cast<quint8>(appData.at(offset));
            item["dataDensity"] = density;
            item["densityDescription"] = getDataDensityDescription(density);
            offset += 1;
        }
        
        // 解析历史数据内容（上行报文会有）
        quint32 dataId = diInfo["dataId"].toUInt();
        int expectedDataLength = getDataLengthByDataId(dataId);
        
        if (expectedDataLength > 0 && offset + expectedDataLength + 5 <= appData.size()) {
            // 数据内容 + 数据时间
            QByteArray contentData = appData.mid(offset, expectedDataLength);
            QVariantMap contentResult = parseDataContent(contentData, dataId, 0x0D);
            item["historyData"] = contentResult;
            offset += expectedDataLength;
            
            // 数据时间 (5字节)
            QByteArray dataTimeData = appData.mid(offset, 5);
            QString dataTime = parseTimeData(dataTimeData);
            item["dataTime"] = dataTime;
            offset += 5;
        }
        
        dataItems.append(item);
        
        if (offset >= appData.size()) {
            break;
        }
    }
    
    result["dataItems"] = dataItems;
    result["itemCount"] = dataItems.size();
    
    return result;
}

QVariantMap NWUPParser::parseReadEventRecord(const QByteArray &appData)
{
    QVariantMap result;
    result["type"] = "读事件记录";
    result["raw"] = formatHexString(appData);
    
    if (appData.size() < 16) { // DA(2) + DI(4) + 起始时间(5) + 结束时间(5) = 16字节最小
        result["error"] = "数据长度不足";
        return result;
    }
    
    int offset = 0;
    QVariantList dataItems;
    
    while (offset + 16 <= appData.size()) {
        QVariantMap item;
        
        // 解析信息点标识DA (2字节)
        QByteArray daData = appData.mid(offset, 2);
        QVariantMap daInfo = parseDataAddress(daData);
        item["dataAddress"] = daInfo;
        offset += 2;
        
        // 解析数据标识编码DI (4字节)
        QByteArray diData = appData.mid(offset, 4);
        QVariantMap diInfo = parseDataIdentifier(diData);
        item["dataIdentifier"] = diInfo;
        offset += 4;
        
        // 检查是否有时间域（下行报文有）
        if (offset + 10 <= appData.size()) {
            // 解析数据起始时间 (5字节)
            QByteArray startTimeData = appData.mid(offset, 5);
            QString startTime = parseTimeData(startTimeData);
            item["startTime"] = startTime;
            offset += 5;
            
            // 解析数据结束时间 (5字节)
            QByteArray endTimeData = appData.mid(offset, 5);
            QString endTime = parseTimeData(endTimeData);
            item["endTime"] = endTime;
            offset += 5;
        }
        
        // 解析事件记录内容（上行报文会有）
        quint32 dataId = diInfo["dataId"].toUInt();
        int expectedDataLength = getDataLengthByDataId(dataId);
        
        if (expectedDataLength > 0 && offset + expectedDataLength <= appData.size()) {
            QByteArray contentData = appData.mid(offset, expectedDataLength);
            QVariantMap contentResult = parseDataContent(contentData, dataId, 0x0E);
            item["eventData"] = contentResult;
            offset += expectedDataLength;
        }
        
        dataItems.append(item);
        
        if (offset >= appData.size()) {
            break;
        }
    }
    
    result["dataItems"] = dataItems;
    result["itemCount"] = dataItems.size();
    
    return result;
}

QVariantMap NWUPParser::parseFileTransfer(const QByteArray &appData) { 
    QVariantMap result; result["type"] = "文件传输"; result["raw"] = formatHexString(appData); return result; 
}

QVariantMap NWUPParser::parseRelayForward(const QByteArray &appData) { 
    QVariantMap result; result["type"] = "中继转发"; result["raw"] = formatHexString(appData); return result; 
}

QVariantMap NWUPParser::parseReadTaskData(const QByteArray &appData) { 
    QVariantMap result; result["type"] = "读任务数据"; result["raw"] = formatHexString(appData); return result; 
}

QVariantMap NWUPParser::parseReadAlarmData(const QByteArray &appData) { 
    QVariantMap result; result["type"] = "读告警数据"; result["raw"] = formatHexString(appData); return result; 
}

QVariantMap NWUPParser::parseUserCustomData(const QByteArray &appData) { 
    QVariantMap result; result["type"] = "用户自定义数据"; result["raw"] = formatHexString(appData); return result; 
}

QVariantMap NWUPParser::parseDataSecurityTransfer(const QByteArray &appData) { 
    QVariantMap result; result["type"] = "数据安全传输"; result["raw"] = formatHexString(appData); return result; 
}

QVariantMap NWUPParser::parseDataEncryption(const QByteArray &appData) { 
    QVariantMap result; result["type"] = "数据转加密"; result["raw"] = formatHexString(appData); return result; 
}

QVariantMap NWUPParser::parseDataAddress(const QByteArray &daData)
{
    QVariantMap daInfo;

    if (daData.size() < 2) {
        daInfo["valid"] = false;
        daInfo["error"] = "信息点标识数据不足，需要2字节";
        return daInfo;
    }

    daInfo["raw"] = formatHexString(daData);

    // DA由DA1和DA2组成
    quint8 da1 = static_cast<quint8>(daData.at(0));  // 信息点元
    quint8 da2 = static_cast<quint8>(daData.at(1));  // 信息点组

    daInfo["da1"] = da1;
    daInfo["da2"] = da2;
    daInfo["da1Hex"] = QString("0x%1").arg(da1, 2, 16, QChar('0')).toUpper();
    daInfo["da2Hex"] = QString("0x%1").arg(da2, 2, 16, QChar('0')).toUpper();

    // 计算信息点编号
    if (da1 == 0 && da2 == 0) {
        daInfo["pointNumber"] = 0;
        daInfo["description"] = "终端测量点p0";
    } else if (da1 == 0xFF && da2 == 0xFF) {
        daInfo["pointNumber"] = -1;
        daInfo["description"] = "除终端信息点外的所有测量点";
    } else {
        // 根据DA1和DA2计算具体的测量点
        QStringList points;
        for (int bit = 0; bit < 8; ++bit) {
            if (da1 & (1 << bit)) {
                int pointNum = da2 * 8 + bit + 1;
                points.append(QString("p%1").arg(pointNum));
            }
        }
        daInfo["points"] = points;
        daInfo["description"] = QString("测量点: %1").arg(points.join(", "));
    }

    daInfo["valid"] = true;
    return daInfo;
}

QVariantMap NWUPParser::parseDataIdentifier(const QByteArray &diData)
{
    QVariantMap diInfo;

    if (diData.size() < 4) {
        diInfo["valid"] = false;
        diInfo["error"] = "数据标识编码数据不足，需要4字节";
        return diInfo;
    }

    diInfo["raw"] = formatHexString(diData);

    // DI由4个字节组成：DI3 DI2 DI1 DI0
    quint8 di0 = static_cast<quint8>(diData.at(0));
    quint8 di1 = static_cast<quint8>(diData.at(1));
    quint8 di2 = static_cast<quint8>(diData.at(2));
    quint8 di3 = static_cast<quint8>(diData.at(3));

    diInfo["di0"] = di0;
    diInfo["di1"] = di1;
    diInfo["di2"] = di2;
    diInfo["di3"] = di3;

    // 组合成32位数据标识
    quint32 dataId = di0 | (di1 << 8) | (di2 << 16) | (di3 << 24);
    diInfo["dataId"] = dataId;
    diInfo["dataIdHex"] = QString("0x%1").arg(dataId, 8, 16, QChar('0')).toUpper();

    // 获取数据标识描述
    QString description = getDataIdentifierDescription(dataId);
    diInfo["description"] = description;

    diInfo["valid"] = true;
    return diInfo;
}

QVariantMap NWUPParser::parseDataTimeField(const QByteArray &timeData)
{
    QVariantMap timeInfo;

    if (timeData.size() < 5) {
        timeInfo["valid"] = false;
        timeInfo["error"] = "数据时间域不足，需要5字节";
        return timeInfo;
    }

    timeInfo["raw"] = formatHexString(timeData);

    // 解析时间数据（BCD编码：年年月日时分）
    QString timeString = parseTimeData(timeData);
    timeInfo["timeString"] = timeString;

    // 分别解析各个时间字段
    quint8 year = static_cast<quint8>(timeData.at(0));
    quint8 month = static_cast<quint8>(timeData.at(1));
    quint8 day = static_cast<quint8>(timeData.at(2));
    quint8 hour = static_cast<quint8>(timeData.at(3));
    quint8 minute = static_cast<quint8>(timeData.at(4));

    timeInfo["year"] = QString("20%1").arg(year, 2, 16, QChar('0'));
    timeInfo["month"] = QString("%1").arg(month, 2, 16, QChar('0'));
    timeInfo["day"] = QString("%1").arg(day, 2, 16, QChar('0'));
    timeInfo["hour"] = QString("%1").arg(hour, 2, 16, QChar('0'));
    timeInfo["minute"] = QString("%1").arg(minute, 2, 16, QChar('0'));

    timeInfo["valid"] = true;
    return timeInfo;
}

QVariantMap NWUPParser::parseMessageAuthCode(const QByteArray &pwData)
{
    QVariantMap pwInfo;

    if (pwData.size() < 16) {
        pwInfo["valid"] = false;
        pwInfo["error"] = "消息认证码数据不足，需要16字节";
        return pwInfo;
    }

    pwInfo["raw"] = formatHexString(pwData);
    pwInfo["length"] = pwData.size();

    // MAC值（前4字节）
    QByteArray macData = pwData.mid(0, 4);
    pwInfo["mac"] = formatHexString(macData);

    // 其他认证信息（后12字节）
    QByteArray authData = pwData.mid(4, 12);
    pwInfo["authData"] = formatHexString(authData);

    pwInfo["description"] = "消息认证码(MAC + 认证信息)";
    pwInfo["valid"] = true;
    return pwInfo;
}

QVariantMap NWUPParser::parseTimeTag(const QByteArray &tpData)
{
    QVariantMap tpInfo;

    if (tpData.size() < 5) {
        tpInfo["valid"] = false;
        tpInfo["error"] = "时间标签数据不足，需要5字节";
        return tpInfo;
    }

    tpInfo["raw"] = formatHexString(tpData);

    // 启动帧发送时标（4字节：日时分秒）
    QByteArray timeStampData = tpData.mid(0, 4);
    tpInfo["timeStamp"] = formatHexString(timeStampData);

    // 允许发送传输延时时间（1字节，单位：分）
    quint8 delayTime = static_cast<quint8>(tpData.at(4));
    tpInfo["delayTime"] = delayTime;
    tpInfo["delayTimeDescription"] = QString("%1分钟").arg(delayTime);

    tpInfo["description"] = QString("发送时标:%1 延时:%2分钟")
                           .arg(formatHexString(timeStampData))
                           .arg(delayTime);

    tpInfo["valid"] = true;
    return tpInfo;
}

QVariantMap NWUPParser::parseErrorCode(quint8 errorCode)
{
    QVariantMap errorInfo;

    errorInfo["code"] = errorCode;
    errorInfo["hex"] = QString("0x%1").arg(errorCode, 2, 16, QChar('0')).toUpper();

    // 错误码描述
    QString description;
    switch (errorCode) {
    case 0x00:
        description = "正确";
        break;
    case 0x01:
        description = "中继命令没有返回";
        break;
    case 0x02:
        description = "设置内容非法";
        break;
    case 0x03:
        description = "密码权限不足";
        break;
    case 0x04:
        description = "无此数据项";
        break;
    case 0x05:
        description = "命令时间失效";
        break;
    case 0x06:
        description = "目标地址不存在";
        break;
    case 0x07:
        description = "校验失败";
        break;
    default:
        description = QString("未知错误码(0x%1)").arg(errorCode, 2, 16, QChar('0')).toUpper();
        break;
    }

    errorInfo["description"] = description;
    return errorInfo;
}

QString NWUPParser::parseBCDValue(const QByteArray &bcdData, int decimalPlaces)
{
    QString result;
    
    for (int i = bcdData.size() - 1; i >= 0; --i) {
        quint8 byte = static_cast<quint8>(bcdData.at(i));
        quint8 high = (byte >> 4) & 0x0F;
        quint8 low = byte & 0x0F;
        
        if (high > 9 || low > 9) {
            return "无效BCD数据";
        }
        
        result += QString::number(high);
        result += QString::number(low);
    }
    
    // 插入小数点
    if (decimalPlaces > 0 && result.length() > decimalPlaces) {
        int pointPos = result.length() - decimalPlaces;
        result.insert(pointPos, '.');
    }
    
    return result;
}

QString NWUPParser::parseTimeData(const QByteArray &timeData)
{
    if (timeData.size() < 5) {
        return "时间数据不足";
    }

    // BCD编码：年年月日时分
    quint8 year = static_cast<quint8>(timeData.at(0));
    quint8 month = static_cast<quint8>(timeData.at(1));
    quint8 day = static_cast<quint8>(timeData.at(2));
    quint8 hour = static_cast<quint8>(timeData.at(3));
    quint8 minute = static_cast<quint8>(timeData.at(4));

    // 转换BCD到十进制
    int yearDec = ((year >> 4) & 0x0F) * 10 + (year & 0x0F);
    int monthDec = ((month >> 4) & 0x0F) * 10 + (month & 0x0F);
    int dayDec = ((day >> 4) & 0x0F) * 10 + (day & 0x0F);
    int hourDec = ((hour >> 4) & 0x0F) * 10 + (hour & 0x0F);
    int minuteDec = ((minute >> 4) & 0x0F) * 10 + (minute & 0x0F);

    return QString("20%1-%2-%3 %4:%5")
           .arg(yearDec, 2, 10, QChar('0'))
           .arg(monthDec, 2, 10, QChar('0'))
           .arg(dayDec, 2, 10, QChar('0'))
           .arg(hourDec, 2, 10, QChar('0'))
           .arg(minuteDec, 2, 10, QChar('0'));
}

QString NWUPParser::getDataIdentifierDescription(quint32 dataId)
{
    // 从配置文件中获取数据标识描述
    if (m_dataConfig) {
        QString dataIdStr = QString("%1").arg(dataId, 8, 16, QChar('0')).toUpper();
        DataItemConfig dataItem = m_dataConfig->getDataItemConfig(dataIdStr.toUInt(nullptr, 16), ProtocolType::NW_UP);
        if (!dataItem.name.isEmpty()) {
            return dataItem.name;
        }
    }

    // 基础的数据标识描述（作为后备）
    switch (dataId) {
    case 0x00010000:
        return "当前正向有功总电能";
    case 0x00020000:
        return "当前反向有功总电能";
    case 0x00030000:
        return "当前组合无功1总电能";
    case 0x00040000:
        return "当前组合无功2总电能";
    case 0x00050000:
        return "当前第一象限无功总电能";
    case 0x00060000:
        return "当前第二象限无功总电能";
    case 0x00070000:
        return "当前第三象限无功总电能";
    case 0x00080000:
        return "当前第四象限无功总电能";
    case 0x00090000:
        return "当前正向视在总电能";
    case 0x000A0000:
        return "当前反向视在总电能";
    case 0x02010100:
        return "A相电压";
    case 0x02010200:
        return "B相电压";
    case 0x02010300:
        return "C相电压";
    case 0x02020100:
        return "A相电流";
    case 0x02020200:
        return "B相电流";
    case 0x02020300:
        return "C相电流";
    case 0x02030000:
        return "瞬时总有功功率";
    case 0x02030100:
        return "瞬时A相有功功率";
    case 0x02030200:
        return "瞬时B相有功功率";
    case 0x02030300:
        return "瞬时C相有功功率";
    case 0x01010000:
        return "当前正向有功总最大需量";
    case 0x01020000:
        return "当前反向有功总最大需量";
    case 0x04000401:
        return "终端通信地址";
    case 0x04000501:
        return "终端上行通信端口参数";
    case 0x04000601:
        return "终端事件记录配置参数";
    case 0xEE010000:
        return "终端停电事件记录";
    case 0xEE020000:
        return "终端上电事件记录";
    case 0xEE030000:
        return "终端时钟超差事件记录";
    case 0xF0000001:
        return "文件传输启动";
    case 0xF0000002:
        return "传输文件内容";
    case 0xF0000003:
        return "文件信息查询";
    case 0xF1000000:
        return "终端登录";
    case 0xF1000001:
        return "终端心跳";
    case 0xF1000002:
        return "终端退出";
    case 0xF2000001:
        return "身份认证请求";
    case 0xF2000002:
        return "身份认证响应";
    case 0xE0000301:
        return "任务1数据";
    case 0xE0000300:
        return "任务参数配置";
    case 0xF3000000:
        return "终端告警信息";
    case 0xE0000000:
        return "全确认/否定响应";
    case 0xF4000000:
        return "厂家自定义数据";
    case 0xF5000000:
        return "加密数据传输";
    default:
        return QString("数据标识:0x%1").arg(dataId, 8, 16, QChar('0')).toUpper();
    }
}

QVariantMap NWUPParser::parseDataContent(const QByteArray &dataContent, quint32 dataId, quint8 afn)
{
    QVariantMap result;
    result["raw"] = formatHexString(dataContent);
    result["length"] = dataContent.size();
    
    // 从配置文件中获取数据项信息
    if (m_dataConfig) {
        QString dataIdStr = QString("%1").arg(dataId, 8, 16, QChar('0')).toUpper();
        DataItemConfig dataItem = m_dataConfig->getDataItemConfig(dataIdStr.toUInt(nullptr, 16), ProtocolType::NW_UP);
        
        if (!dataItem.name.isEmpty()) {
            result["dataItemName"] = dataItem.name;
            result["description"] = dataItem.description;
            result["unit"] = dataItem.unit;
            result["encoding"] = dataItem.encoding;
            
            // 根据数据项格式解析数据内容
            if (dataItem.format.contains(".")) {
                // 数值格式（如0.01, 0.1, 0.001等）
                QString parsedValue = parseDataByFormat(dataContent, dataItem.format, dataItem.encoding);
                result["value"] = parsedValue;
                result["formattedValue"] = parsedValue + " " + dataItem.unit;
            }
            else if (dataItem.format.contains("YY")) {
                // 时间格式
                QString timeValue = parseTimeDataByFormat(dataContent, dataItem.format);
                result["timeValue"] = timeValue;
                result["formattedValue"] = timeValue;
            }
            else if (dataItem.format == "DEMAND") {
                // 最大需量格式（包含数值和时间）
                result["demandData"] = parseDemandData(dataContent, dataItem);
            }
            else if (dataItem.format == "EVENT") {
                // 事件记录格式
                result["eventData"] = parseEventData(dataContent, dataItem);
            }
            else if (dataItem.format == "FILE") {
                // 文件传输格式
                result["fileData"] = parseFileData(dataContent, dataItem);
            }
            else if (dataItem.format == "TASK") {
                // 任务数据格式
                result["taskData"] = parseTaskData(dataContent, dataItem);
            }
            else {
                // 其他格式，按原始数据显示
                result["value"] = formatHexString(dataContent);
                result["formattedValue"] = formatHexString(dataContent);
            }
        }
        else {
            // 配置文件中没有找到对应的数据项
            result["error"] = "未找到数据标识配置";
            result["value"] = formatHexString(dataContent);
        }
    }
    else {
        // 没有配置文件，使用基础解析
        result["error"] = "配置文件未加载";
        result["value"] = formatHexString(dataContent);
    }
    
    return result;
}

QString NWUPParser::parseDataByFormat(const QByteArray &data, const QString &format, const QString &encoding)
{
    if (encoding == "BCD") {
        // 解析小数点位置
        int decimalPlaces = 0;
        if (format.contains(".")) {
            QString decimalPart = format.split(".").last();
            decimalPlaces = decimalPart.length();
        }
        return parseBCDValue(data, decimalPlaces);
    }
    else if (encoding == "ASCII") {
        return QString::fromLatin1(data);
    }
    else if (encoding == "BIN") {
        return formatHexString(data);
    }
    else {
        return formatHexString(data);
    }
}

QString NWUPParser::parseTimeDataByFormat(const QByteArray &data, const QString &format)
{
    if (format == "YYMMDDhhmm" && data.size() >= 5) {
        return parseTimeData(data);
    }
    else if (format == "YYMMDDhhmmss" && data.size() >= 6) {
        // 解析6字节时间数据（年月日时分秒）
        quint8 year = static_cast<quint8>(data.at(0));
        quint8 month = static_cast<quint8>(data.at(1));
        quint8 day = static_cast<quint8>(data.at(2));
        quint8 hour = static_cast<quint8>(data.at(3));
        quint8 minute = static_cast<quint8>(data.at(4));
        quint8 second = static_cast<quint8>(data.at(5));
        
        // 转换BCD到十进制
        int yearDec = ((year >> 4) & 0x0F) * 10 + (year & 0x0F);
        int monthDec = ((month >> 4) & 0x0F) * 10 + (month & 0x0F);
        int dayDec = ((day >> 4) & 0x0F) * 10 + (day & 0x0F);
        int hourDec = ((hour >> 4) & 0x0F) * 10 + (hour & 0x0F);
        int minuteDec = ((minute >> 4) & 0x0F) * 10 + (minute & 0x0F);
        int secondDec = ((second >> 4) & 0x0F) * 10 + (second & 0x0F);
        
        return QString("20%1-%2-%3 %4:%5:%6")
               .arg(yearDec, 2, 10, QChar('0'))
               .arg(monthDec, 2, 10, QChar('0'))
               .arg(dayDec, 2, 10, QChar('0'))
               .arg(hourDec, 2, 10, QChar('0'))
               .arg(minuteDec, 2, 10, QChar('0'))
               .arg(secondDec, 2, 10, QChar('0'));
    }
    else if (format == "YYMMDDWW" && data.size() >= 4) {
        // 解析日期和星期
        quint8 year = static_cast<quint8>(data.at(0));
        quint8 month = static_cast<quint8>(data.at(1));
        quint8 day = static_cast<quint8>(data.at(2));
        quint8 week = static_cast<quint8>(data.at(3));
        
        int yearDec = ((year >> 4) & 0x0F) * 10 + (year & 0x0F);
        int monthDec = ((month >> 4) & 0x0F) * 10 + (month & 0x0F);
        int dayDec = ((day >> 4) & 0x0F) * 10 + (day & 0x0F);
        int weekDec = ((week >> 4) & 0x0F) * 10 + (week & 0x0F);
        
        QStringList weekNames = {"日", "一", "二", "三", "四", "五", "六"};
        QString weekName = (weekDec >= 0 && weekDec < weekNames.size()) ? weekNames[weekDec] : QString::number(weekDec);
        
        return QString("20%1-%2-%3 星期%4")
               .arg(yearDec, 2, 10, QChar('0'))
               .arg(monthDec, 2, 10, QChar('0'))
               .arg(dayDec, 2, 10, QChar('0'))
               .arg(weekName);
    }
    else {
        return formatHexString(data);
    }
}

QVariantMap NWUPParser::parseDemandData(const QByteArray &data, const DataItemConfig &dataItem)
{
    QVariantMap result;
    
    // 解析最大需量数据（通常包含需量值和发生时间）
    if (data.size() >= 8) {
        // 前3字节为需量值，后5字节为发生时间
        QByteArray demandValue = data.mid(0, 3);
        QByteArray demandTime = data.mid(3, 5);
        
        QString valueStr = parseBCDValue(demandValue, 4); // 0.0001精度
        QString timeStr = parseTimeData(demandTime);
        
        result["demandValue"] = valueStr;
        result["demandTime"] = timeStr;
        result["formattedValue"] = QString("%1 kW (发生时间: %2)").arg(valueStr).arg(timeStr);
    }
    else {
        result["error"] = "最大需量数据长度不足";
        result["raw"] = formatHexString(data);
    }
    
    return result;
}

QVariantMap NWUPParser::parseEventData(const QByteArray &data, const DataItemConfig &dataItem)
{
    QVariantMap result;
    QVariantList fields;
    
    // 根据配置文件中的字段定义解析事件数据
    for (const auto &field : dataItem.fields) {
        QVariantMap fieldData;
        fieldData["name"] = field.name;
        fieldData["format"] = field.format;
        fieldData["unit"] = field.unit;
        
        if (field.offset + field.length <= data.size()) {
            QByteArray fieldBytes = data.mid(field.offset, field.length);
            
            if (field.format.contains("YY")) {
                fieldData["value"] = parseTimeDataByFormat(fieldBytes, field.format);
            }
            else {
                fieldData["value"] = parseBCDValue(fieldBytes, 0);
            }
            
            fieldData["raw"] = formatHexString(fieldBytes);
        }
        else {
            fieldData["error"] = "数据长度不足";
        }
        
        fields.append(fieldData);
    }
    
    result["fields"] = fields;
    return result;
}

QVariantMap NWUPParser::parseFileData(const QByteArray &data, const DataItemConfig &dataItem)
{
    QVariantMap result;
    
    // 文件传输数据解析
    result["type"] = "文件传输数据";
    result["raw"] = formatHexString(data);
    result["size"] = data.size();
    
    // TODO: 根据具体的文件传输协议格式进行解析
    
    return result;
}

QVariantMap NWUPParser::parseTaskData(const QByteArray &data, const DataItemConfig &dataItem)
{
    QVariantMap result;
    
    // 任务数据解析
    result["type"] = "任务数据";
    result["raw"] = formatHexString(data);
    result["size"] = data.size();
    
    // TODO: 根据具体的任务数据格式进行解析
    
    return result;
}

QString NWUPParser::getDataDensityDescription(quint8 density)
{
    // 根据协议文档定义数据密度描述
    switch (density) {
    case 0:
        return "终端历史数据存储密度";
    case 1:
        return "1分钟";
    case 2:
        return "5分钟";
    case 3:
        return "15分钟";
    case 4:
        return "30分钟";
    case 5:
        return "60分钟";
    case 6:
        return "1日";
    case 7:
        return "1月";
    case 8:
        return "结算日";
    default:
        return QString("备用(%1)").arg(density);
    }
}