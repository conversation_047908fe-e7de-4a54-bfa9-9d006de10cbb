import QtQuick 2.12

/**
 * @brief DL/T645协议显示模块
 * 专门处理DL/T645-2007和DL/T645-1997协议的树形显示
 */
BaseDisplayModule {
    id: dlt645Module
    
    /**
     * @brief 构建DL/T645协议特定的树形结构
     * @param parsedData 解析后的数据
     * @param level 层级
     */
    function buildProtocolSpecificTree(parsedData, level) {
        if (!parsedData) {
            addTreeItemWithVisibility("解析错误", "", "没有可显示的协议数据", level, false, false, true)
            return
        }
        
        console.log("DLT645DisplayModule: buildProtocolSpecificTree - parsedData:", JSON.stringify(parsedData))
        
        // 获取原始报文用于提取原始字节数据
        var rawMessage = getRawMessage()
        
        // 构建DL/T645协议帧结构
        buildDL645FrameTree(parsedData, level, rawMessage)
    }
    
    /**
     * @brief 构建DL/T645帧结构树
     */
    function buildDL645FrameTree(parsedData, level, rawMessage) {
        // 2级：帧起始符 - 显示原始报文第1字节
        var startFrameRaw = extractBytes(rawMessage, 1, 1)
        addTreeItemWithVisibility("帧起始符", startFrameRaw, "帧起始标识符", level, false, false, true)

        // 2级：地址域 - 显示原始报文第2-7字节
        var addressRaw = extractBytes(rawMessage, 2, 6)
        var addressDesc = "电表通信地址：" + (parsedData.address || "")
        addTreeItemWithVisibility("地址域", addressRaw, addressDesc, level, false, false, true)

        // 2级：帧起始符（第二个）- 显示原始报文第8字节
        var startFrame2Raw = extractBytes(rawMessage, 8, 1)
        addTreeItemWithVisibility("帧起始符", startFrame2Raw, "第二个帧起始标识符", level, false, false, true)

        // 2级：控制码（有子项）- 显示原始报文第9字节
        var controlCodeRaw = extractBytes(rawMessage, 9, 1)
        var controlDesc = ""
        if (parsedData.controlCode) {
            controlDesc = parsedData.controlCode.description || "控制码字段"
        }
        addTreeItemWithVisibility("控制码", controlCodeRaw, controlDesc, level, true, true, true)

        // 3级：控制码子项 - 使用后端解析的控制码信息
        buildControlCodeTree(parsedData, level + 1)

        // 2级：数据域长度 - 显示原始报文第10字节
        var dataLengthRaw = extractBytes(rawMessage, 10, 1)
        var dataLengthValue = parsedData.dataLength || 0
        var dataLengthDesc = "数据域字节长度：" + dataLengthValue + "字节"
        addTreeItemWithVisibility("数据域长度", dataLengthRaw, dataLengthDesc, level, false, false, true)

        // 2级：数据域（有子项）- 不显示原始数据，因为有子项
        addTreeItemWithVisibility("数据域", "", "协议数据域", level, true, true, true)

        // 3级：数据域子项
        buildDataFieldTree(parsedData, level + 1, rawMessage)

        // 2级：校验码 - 显示原始报文倒数第2字节
        var checksumRaw = rawMessage.length >= 4 ? rawMessage.substring(rawMessage.length - 4, rawMessage.length - 2) : ""
        addTreeItemWithVisibility("校验码", checksumRaw, "校验和", level, false, false, true)

        // 2级：结束符 - 显示原始报文最后1字节
        var endFrameRaw = rawMessage.length >= 2 ? rawMessage.substring(rawMessage.length - 2) : "16"
        addTreeItemWithVisibility("结束符", endFrameRaw, "帧结束标识符", level, false, false, true)
    }
    
    /**
     * @brief 构建控制码树
     */
    function buildControlCodeTree(parsedData, level) {
        // 直接使用后端解析的控制码信息
        if (parsedData.controlCode) {
            var controlInfo = parsedData.controlCode

            // 3级：D7传送方向 - 使用后端解析的direction信息
            var d7Value = controlInfo.isResponse ? "1" : "0"
            var d7Desc = controlInfo.direction || (controlInfo.isResponse ? "从站应答" : "主站命令")
            addTreeItemWithVisibility("D7传送方向", d7Value, d7Desc, level, false, false, true)

            // 3级：D6从站应答标志 - 使用后端解析的errorFlag信息
            var d6Value = controlInfo.hasError ? "1" : "0"
            var d6Desc = controlInfo.errorFlag || (controlInfo.hasError ? "异常应答" : "正确应答")
            addTreeItemWithVisibility("D6从站应答标志", d6Value, d6Desc, level, false, false, true)

            // 3级：D5后续帧标志 - 使用后端解析的followFrameFlag信息
            var d5Value = controlInfo.hasFollowFrame ? "1" : "0"
            var d5Desc = controlInfo.followFrameFlag || (controlInfo.hasFollowFrame ? "有后续帧" : "无后续帧")
            addTreeItemWithVisibility("D5后续帧标志", d5Value, d5Desc, level, false, false, true)

            // 3级：D4-D0功能码 - 使用后端解析的功能码信息
            var funcCode = controlInfo.functionCode || 0
            var funcCodeHex = funcCode.toString(16).toUpperCase().padStart(2, '0')
            var funcDesc = controlInfo.functionDescription || getFunctionCodeDescription(funcCode)
            addTreeItemWithVisibility("D4-D0功能码", funcCodeHex, funcDesc, level, false, false, true)
        }
    }
    
    /**
     * @brief 获取功能码描述
     */
    function getFunctionCodeDescription(funcCode) {
        switch (funcCode) {
            case 0x08: return "广播校时"
            case 0x11: return "读数据"
            case 0x12: return "读后续数据"
            case 0x13: return "读通信地址"
            case 0x14: return "写数据"
            case 0x15: return "写通信地址"
            case 0x16: return "冻结命令"
            case 0x17: return "更改通信速率"
            case 0x18: return "修改密码"
            case 0x19: return "最大需量清零"
            default: return "功能码：" + funcCode.toString(16).toUpperCase()
        }
    }

    /**
     * @brief 构建数据域树
     */
    function buildDataFieldTree(parsedData, level, rawMessage) {
        console.log("buildDataFieldTree - parsedData:", JSON.stringify(parsedData))

        // 直接使用后端解析的数据域信息
        if (parsedData.dataFieldInfo) {
            var dataFieldInfo = parsedData.dataFieldInfo
            console.log("dataFieldInfo found:", JSON.stringify(dataFieldInfo))

            // 3级：数据标识编码 - 显示原始报文中的数据标识字节（第11-14字节）
            var dataIdRaw = ""
            if (parsedData.rawDataField) {
                // 使用后端提供的原始数据域字段，前4个字节是数据标识
                var rawDataBytes = parsedData.rawDataField.split(" ")
                if (rawDataBytes.length >= 4) {
                    dataIdRaw = rawDataBytes.slice(0, 4).join(" ")
                }
            } else {
                // 备用方案：直接从原始报文中提取数据标识字节
                dataIdRaw = extractBytes(rawMessage, 11, 4)
            }

            // 构建数据标识编码的描述，包含解析后的值
            var dataIdDesc = "数据标识ID"
            if (dataFieldInfo.dataIdentifier) {
                var dataIdValue = dataFieldInfo.dataIdentifier.value || ""
                var dataIdDescription = dataFieldInfo.dataIdentifier.description || ""
                if (dataIdValue) {
                    // 移除16进制前缀
                    var cleanDataIdValue = removeHexPrefix(dataIdValue)
                    dataIdDesc = "数据标识ID：" + cleanDataIdValue
                    if (dataIdDescription) {
                        dataIdDesc += "（" + dataIdDescription + "）"
                    }
                }
            }
            addTreeItemWithVisibility("数据标识编码", dataIdRaw, dataIdDesc, level, false, false, true)

            // 3级：数据块、复合数据或数据项名称 - 直接使用后端解析的名称和类型信息
            var dataName = ""
            var isBlock = (dataFieldInfo.dataIdentifier && dataFieldInfo.dataIdentifier.isDataBlock) || dataFieldInfo.format === "BLOCK"
            var isComplex = dataFieldInfo.format === "COMPLEX"

            if (dataFieldInfo.dataIdentifier && dataFieldInfo.dataIdentifier.description) {
                // 优先使用dataIdentifier.description获取名称
                dataName = dataFieldInfo.dataIdentifier.description
            } else {
                dataName = dataFieldInfo.name || dataFieldInfo.dataName || dataFieldInfo.type || "未知数据项"
            }

            // 根据功能码类型选择不同的显示方式
            if (dataFieldInfo.type === "读数据请求") {
                // 读数据请求
                addTreeItemWithVisibility(dataName, "", "读数据请求", level, true, true, true)
                buildReadDataRequestItems(dataFieldInfo, level + 1, parsedData)
            } else if (dataFieldInfo.type === "广播校时") {
                // 广播校时
                addTreeItemWithVisibility("广播校时", "", "设置电表时间", level, true, true, true)
                buildTimeCalibrationItems(dataFieldInfo, level + 1, parsedData)
            } else if (dataFieldInfo.type === "写数据请求") {
                // 写数据请求
                addTreeItemWithVisibility(dataName, "", "写数据请求", level, true, true, true)
                buildWriteDataRequestItems(dataFieldInfo, level + 1, parsedData)
            } else if (dataFieldInfo.type === "冻结命令") {
                // 冻结命令
                addTreeItemWithVisibility("冻结命令", "", "数据冻结操作", level, true, true, true)
                buildFreezeCommandItems(dataFieldInfo, level + 1, parsedData)
            } else if (isBlock) {
                // 数据块应答：显示具体数据项
                addTreeItemWithVisibility(dataName, "", "数据块", level, true, true, true)
                buildDataBlockItems(dataFieldInfo, level + 1, parsedData)
            } else if (dataFieldInfo.isVariable) {
                // 如果是可变长度数据项，有子项 - 不显示原始数据，因为有子项
                var itemCount = dataFieldInfo.parsedValue ? dataFieldInfo.parsedValue.totalItems || 0 : 0
                var description = "0-" + (itemCount - 1)
                addTreeItemWithVisibility(dataName, "", description, level, true, true, true)
                buildVariableDataItems(dataFieldInfo, level + 1, parsedData)
            } else if (isComplex) {
                // 如果是复合数据格式，有子项 - 不显示原始数据，因为有子项
                addTreeItemWithVisibility(dataName, "", "复合数据", level, true, true, true)
                buildComplexDataItems(dataFieldInfo, level + 1, parsedData)
            } else {
                // 如果是数据项，无子项 - 显示原始数据内容
                var dataContentRaw = ""
                if (rawMessage && rawMessage.length >= 28) {
                    // 数据内容从第15字节开始，长度根据数据域长度确定
                    var dataLength = parsedData.dataLength || 0
                    var startPos = 28 // 跳过帧头、地址、控制码、长度、数据标识
                    var endPos = startPos + (dataLength - 4) * 2 // 减去数据标识的4字节
                    if (endPos <= rawMessage.length - 4) { // 减去校验码和结束符
                        dataContentRaw = rawMessage.substring(startPos, endPos).match(/.{2}/g).join(" ")
                    }
                }
                var itemValue = dataFieldInfo.parsedValue || dataFieldInfo.value || ""
                var unit = dataFieldInfo.unit || ""
                var itemDesc = itemValue + (unit ? " " + unit : "")
                addTreeItemWithVisibility(dataName, dataContentRaw, itemDesc, level, false, false, true)
            }
        } else {
            console.log("No dataFieldInfo found, adding placeholder")
            addTreeItemWithVisibility("数据标识编码", "", "数据标识ID", level, false, false, true)
            addTreeItemWithVisibility("数据项", "", "解析中...", level, false, false, true)
        }
    }

    /**
     * @brief 构建数据块项目
     */
    function buildDataBlockItems(dataFieldInfo, level, parsedData) {
        console.log("buildDataBlockItems - dataFieldInfo:", JSON.stringify(dataFieldInfo))

        // 直接使用后端解析的数据项列表
        if (dataFieldInfo.parsedValue && dataFieldInfo.parsedValue.items) {
            var items = dataFieldInfo.parsedValue.items
            console.log("Found items:", JSON.stringify(items))

            // 4级：具体的数据项（如A相电压）- 显示原始数据字节
            for (var i = 0; i < items.length; i++) {
                var item = items[i]
                var itemName = item.name || ("项目" + (item.order || (i + 1)))

                // 获取原始数据字节 - 从rawDataField中提取对应位置的数据（加了33H的原始数据）
                var itemData = ""
                if (parsedData.rawDataField) {
                    // 使用rawDataField中的原始数据（这是报文中真正的原始字节）
                    var rawDataBytes = parsedData.rawDataField.split(" ")
                    // 数据项从第5个字节开始（跳过数据标识的4字节）
                    var startIndex = 4 + (i * (item.length || 2))
                    var endIndex = startIndex + (item.length || 2)
                    if (startIndex < rawDataBytes.length && endIndex <= rawDataBytes.length) {
                        itemData = rawDataBytes.slice(startIndex, endIndex).join(" ")
                    }
                }

                // 如果没有从rawDataField获取到，使用后端提供的rawData作为备用
                if (!itemData) {
                    itemData = item.rawData || ""
                }

                // 检查是否为复杂格式数据项
                if (item.isComplex && item.fields && item.fields.length > 0) {
                    // 复杂格式数据项：显示为可展开的节点，包含多个字段
                    console.log("Adding complex item:", itemName, "with", item.fields.length, "fields")
                    addTreeItemWithVisibility(itemName, itemData, "复合数据", level, true, true, true)

                    // 递归显示复杂格式的字段
                    buildComplexDataItemFields(item, level + 1)
                } else {
                    // 简单格式数据项：直接显示解析值
                    var itemValue = item.parsedValue || ""
                    var unit = item.unit || ""
                    var itemDesc = itemValue + (unit ? " " + unit : "")

                    console.log("Adding simple item:", itemName, itemData, itemDesc)
                    addTreeItemWithVisibility(itemName, itemData, itemDesc, level, false, false, true)
                }
            }
        } else {
            console.log("No parsedValue.items found")
            // 添加占位符
            addTreeItemWithVisibility("数据项", "", "解析中...", level, false, false, true)
        }
    }

    /**
     * @brief 构建数据块中复杂格式数据项的字段
     */
    function buildComplexDataItemFields(complexItem, level) {
        console.log("buildComplexDataItemFields - complexItem:", JSON.stringify(complexItem))

        if (complexItem.parsedValue && complexItem.parsedValue.fields) {
            var fields = complexItem.parsedValue.fields
            console.log("Found complex item fields:", JSON.stringify(fields))

            // 5级：复杂格式数据项的具体字段
            for (var i = 0; i < fields.length; i++) {
                var field = fields[i]
                var fieldName = field.name || ("字段" + (i + 1))
                var fieldData = field.rawData || ""
                var fieldValue = field.parsedValue || ""
                var unit = field.unit || ""
                var fieldDesc = fieldValue + (unit ? " " + unit : "")

                console.log("Adding complex item field:", fieldName, fieldData, fieldDesc)
                addTreeItemWithVisibility(fieldName, fieldData, fieldDesc, level, false, false, true)
            }
        } else {
            console.log("No parsedValue.fields found in complex item")
            // 添加占位符
            addTreeItemWithVisibility("字段", "", "解析中...", level, false, false, true)
        }
    }

    /**
     * @brief 构建可变长度数据项目
     */
    function buildVariableDataItems(dataFieldInfo, level, parsedData) {
        console.log("buildVariableDataItems - dataFieldInfo:", JSON.stringify(dataFieldInfo))

        // 直接使用后端解析的数据项列表
        if (dataFieldInfo.parsedValue && dataFieldInfo.parsedValue.items) {
            var items = dataFieldInfo.parsedValue.items
            console.log("Found variable items:", JSON.stringify(items))

            // 4级：可变长度数据项的各个子项
            for (var i = 0; i < items.length; i++) {
                var item = items[i]
                var itemName = item.name || ("项目" + i)
                var itemData = item.rawData || ""

                // 检查是否为复杂格式数据项
                if (item.isComplex && item.fields && item.fields.length > 0) {
                    // 复杂格式数据项：显示为可展开的节点，包含多个字段
                    console.log("Adding variable complex item:", itemName, "with", item.fields.length, "fields")
                    addTreeItemWithVisibility(itemName, itemData, "复合数据", level, true, true, true)

                    // 递归显示复杂格式的字段
                    buildComplexDataItemFields(item, level + 1)
                } else {
                    // 简单格式数据项：直接显示解析值
                    var itemValue = item.parsedValue || ""
                    var unit = item.unit || ""
                    var itemDesc = itemValue + (unit ? " " + unit : "")

                    console.log("Adding variable simple item:", itemName, itemData, itemDesc)
                    addTreeItemWithVisibility(itemName, itemData, itemDesc, level, false, false, true)
                }
            }
        } else {
            console.log("No parsedValue.items found in variable data")
            // 添加占位符
            addTreeItemWithVisibility("数据项", "", "解析中...", level, false, false, true)
        }
    }

    /**
     * @brief 构建复合数据项目
     */
    function buildComplexDataItems(dataFieldInfo, level, parsedData) {
        console.log("buildComplexDataItems - dataFieldInfo:", JSON.stringify(dataFieldInfo))

        // 直接使用后端解析的字段列表
        if (dataFieldInfo.parsedValue && dataFieldInfo.parsedValue.fields) {
            var fields = dataFieldInfo.parsedValue.fields
            console.log("Found complex fields:", JSON.stringify(fields))

            // 4级：具体的字段（如发生时刻、结束时刻等）- 显示原始数据字节
            var currentOffset = 0
            for (var i = 0; i < fields.length; i++) {
                var field = fields[i]
                var fieldName = field.name || ("字段" + (i + 1))

                // 获取原始数据字节 - 从rawDataField中提取对应位置的数据
                var fieldData = ""
                if (parsedData.rawDataField) {
                    // 使用rawDataField中的原始数据（这是报文中真正的原始字节）
                    var rawDataBytes = parsedData.rawDataField.split(" ")
                    // 字段从第5个字节开始（跳过数据标识的4字节）
                    var startIndex = 4 + currentOffset
                    var fieldLength = field.length || 4
                    var endIndex = startIndex + fieldLength
                    if (startIndex < rawDataBytes.length && endIndex <= rawDataBytes.length) {
                        fieldData = rawDataBytes.slice(startIndex, endIndex).join(" ")
                    }
                    currentOffset += fieldLength
                }

                // 如果没有从rawDataField获取到，使用后端提供的rawData作为备用
                if (!fieldData) {
                    fieldData = field.rawData || ""
                }

                var fieldValue = field.parsedValue || ""
                var unit = field.unit || ""
                var fieldDesc = fieldValue + (unit ? " " + unit : "")

                // 不重复显示字段描述，因为字段名称已经包含了描述信息

                console.log("Adding complex field:", fieldName, fieldData, fieldDesc)
                addTreeItemWithVisibility(fieldName, fieldData, fieldDesc, level, false, false, true)
            }
        } else {
            console.log("No parsedValue.fields found")
            // 添加占位符
            addTreeItemWithVisibility("字段", "", "解析中...", level, false, false, true)
        }
    }

    /**
     * @brief 构建读数据请求项目
     */
    function buildReadDataRequestItems(dataFieldInfo, level, parsedData) {
        console.log("buildReadDataRequestItems - dataFieldInfo:", JSON.stringify(dataFieldInfo))

        // 显示块数（帧格式2和3）- 在数据列显示真正的原始数据
        if (dataFieldInfo.blockCount !== undefined) {
            var rawBlockValue = "??"
            var blockDesc = "负荷记录块数：" + dataFieldInfo.blockCount

            // 尝试从parsedData中获取原始数据域
            if (parsedData && parsedData.rawDataField) {
                var rawDataBytes = parsedData.rawDataField.split(' ')
                if (rawDataBytes.length > 4) {
                    rawBlockValue = rawDataBytes[4]
                    blockDesc += " (原始值：" + rawBlockValue + ")"
                }
            }
            addTreeItemWithVisibility("块数N", rawBlockValue, blockDesc, level, false, false, true)
        }

        // 显示时间数据原始字节（帧格式3）- 在数据列显示真正的原始数据
        if (dataFieldInfo.timeData) {
            var rawTimeStr = "?? ?? ?? ?? ??"
            var timeDesc = "时间格式 (mm hh DD MM YY)"

            // 尝试从parsedData中获取原始数据域
            if (parsedData && parsedData.rawDataField) {
                var rawDataBytes = parsedData.rawDataField.split(' ')
                var rawTimeData = []
                for (var i = 5; i < 10 && i < rawDataBytes.length; i++) {
                    rawTimeData.push(rawDataBytes[i])
                }
                if (rawTimeData.length === 5) {
                    rawTimeStr = rawTimeData.join(' ')
                }
            }

            if (dataFieldInfo.specifiedTime) {
                timeDesc += " - " + dataFieldInfo.specifiedTime
            }
            addTreeItemWithVisibility("时间数据", rawTimeStr, timeDesc, level, false, false, true)
        }

        // 显示错误信息
        if (dataFieldInfo.error) {
            addTreeItemWithVisibility("错误", "", dataFieldInfo.error, level, false, false, true)
        }

        // 显示时间错误信息
        if (dataFieldInfo.timeError) {
            addTreeItemWithVisibility("时间错误", "", dataFieldInfo.timeError, level, false, false, true)
        }
    }

    /**
     * @brief 构建广播校时项目
     */
    function buildTimeCalibrationItems(dataFieldInfo, level, parsedData) {
        console.log("buildTimeCalibrationItems - dataFieldInfo:", JSON.stringify(dataFieldInfo))

        // 显示校时时间
        if (dataFieldInfo.calibrationTime) {
            addTreeItemWithVisibility("校时时间", "", dataFieldInfo.calibrationTime, level, false, false, true)
        }

        // 显示星期
        if (dataFieldInfo.weekday) {
            addTreeItemWithVisibility("星期", "", dataFieldInfo.weekday + " (代码:" + dataFieldInfo.weekdayCode + ")", level, false, false, true)
        }

        // 显示各个时间字段
        if (dataFieldInfo.timeFields) {
            var fields = dataFieldInfo.timeFields
            addTreeItemWithVisibility("时间字段", "", "BCD码解析结果", level, true, true, true)

            addTreeItemWithVisibility("秒", "", fields.second + " 秒", level + 1, false, false, true)
            addTreeItemWithVisibility("分", "", fields.minute + " 分", level + 1, false, false, true)
            addTreeItemWithVisibility("时", "", fields.hour + " 时", level + 1, false, false, true)
            addTreeItemWithVisibility("日", "", fields.day + " 日", level + 1, false, false, true)
            addTreeItemWithVisibility("月", "", fields.month + " 月", level + 1, false, false, true)
            addTreeItemWithVisibility("年", "", fields.year + " 年", level + 1, false, false, true)
            addTreeItemWithVisibility("星期", "", fields.weekday, level + 1, false, false, true)
        }
    }

    /**
     * @brief 构建写数据请求项目
     */
    function buildWriteDataRequestItems(dataFieldInfo, level, parsedData) {
        console.log("buildWriteDataRequestItems - dataFieldInfo:", JSON.stringify(dataFieldInfo))

        // 显示帧类型
        if (dataFieldInfo.frameType) {
            addTreeItemWithVisibility("帧类型", "", dataFieldInfo.frameType, level, false, false, true)
        }

        // 显示密码信息
        if (dataFieldInfo.password) {
            addTreeItemWithVisibility("密码", dataFieldInfo.password, dataFieldInfo.passwordNote || "编程密码", level, false, false, true)
        }

        // 显示操作者代码
        if (dataFieldInfo.operatorCode) {
            var operatorDesc = dataFieldInfo.operatorCodeNote || "操作者身份标识"
            addTreeItemWithVisibility("操作者代码", dataFieldInfo.operatorCode, operatorDesc, level, false, false, true)
        }

        // 显示数据内容
        if (dataFieldInfo.dataContent) {
            var contentDesc = "数据长度：" + dataFieldInfo.dataContentLength + "字节"
            if (dataFieldInfo.dataName) {
                contentDesc += " (" + dataFieldInfo.dataName + ")"
            }
            addTreeItemWithVisibility("数据内容", dataFieldInfo.dataContent, contentDesc, level, false, false, true)
        }

        // 显示解析后的数据内容
        if (dataFieldInfo.parsedDataContent) {
            var unit = dataFieldInfo.unit ? " " + dataFieldInfo.unit : ""
            addTreeItemWithVisibility("解析值", "", dataFieldInfo.parsedDataContent + unit, level, false, false, true)
        }

        // 显示安全级别
        if (dataFieldInfo.securityLevel) {
            addTreeItemWithVisibility("安全级别", "", dataFieldInfo.securityLevel, level, false, false, true)
        }
    }

    /**
     * @brief 构建冻结命令项目
     */
    function buildFreezeCommandItems(dataFieldInfo, level, parsedData) {
        console.log("buildFreezeCommandItems - dataFieldInfo:", JSON.stringify(dataFieldInfo))

        // 显示冻结类型
        if (dataFieldInfo.freezeType) {
            addTreeItemWithVisibility("冻结类型", "", dataFieldInfo.freezeType, level, false, false, true)
        }

        // 显示冻结时间
        if (dataFieldInfo.freezeTime) {
            addTreeItemWithVisibility("冻结时间", "", dataFieldInfo.freezeTime, level, false, false, true)
        }

        // 显示时间字段（定时冻结）
        if (dataFieldInfo.timeFields) {
            var fields = dataFieldInfo.timeFields
            addTreeItemWithVisibility("时间字段", "", "BCD码解析结果", level, true, true, true)

            addTreeItemWithVisibility("月", "", fields.month + " 月", level + 1, false, false, true)
            addTreeItemWithVisibility("日", "", fields.day + " 日", level + 1, false, false, true)
            addTreeItemWithVisibility("时", "", fields.hour + " 时", level + 1, false, false, true)
            addTreeItemWithVisibility("分", "", fields.minute + " 分", level + 1, false, false, true)
        }

        // 显示说明
        if (dataFieldInfo.note) {
            addTreeItemWithVisibility("说明", "", dataFieldInfo.note, level, false, false, true)
        }
    }


}
