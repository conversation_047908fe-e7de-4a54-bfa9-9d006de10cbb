#include "dataparser.h"
#include <QDebug>
#include <QRegularExpression>

DataParser::DataParser(QObject *parent) : QObject(parent)
{
}

DataParser::~DataParser()
{
}

QByteArray DataParser::hexStringToByteArray(const QString &hexString)
{
    // Remove spaces, 0x prefix, and ... suffix
    QString cleanHex = hexString;
    cleanHex.remove(QRegularExpression("\\s+"));
    cleanHex.remove(QRegularExpression("^0x", QRegularExpression::CaseInsensitiveOption));
    cleanHex.remove(QRegularExpression("\\.\\.\\.\\s*$"));
    
    // Check if the string is valid hex
    if (!QRegularExpression("^[0-9A-Fa-f]*$").match(cleanHex).hasMatch()) {
        qWarning() << "Invalid hex string:" << hexString;
        return QByteArray();
    }
    
    // Ensure even length (each byte is 2 hex characters)
    if (cleanHex.length() % 2 != 0) {
        cleanHex.prepend("0");
    }
    
    // Convert to byte array
    QByteArray result;
    for (int i = 0; i < cleanHex.length(); i += 2) {
        bool ok;
        quint8 byte = cleanHex.midRef(i, 2).toUInt(&ok, 16);
        if (ok) {
            result.append(static_cast<char>(byte));
        } else {
            qWarning() << "Failed to convert hex to byte at position" << i;
        }
    }
    
    return result;
}

quint8 DataParser::parseU8(const QByteArray &data, int offset)
{
    if (data.size() <= offset) {
        qWarning() << "Data too small for U8";
        return 0;
    }
    
    return static_cast<quint8>(data.at(offset));
}

qint8 DataParser::parseS8(const QByteArray &data, int offset)
{
    if (data.size() <= offset) {
        qWarning() << "Data too small for S8";
        return 0;
    }
    
    return static_cast<qint8>(data.at(offset));
}

quint16 DataParser::parseU16(const QByteArray &data, int offset)
{
    if (data.size() < offset + 2) {
        qWarning() << "Data too small for U16";
        return 0;
    }
    
    // Big-endian: MSB first
    quint16 result = static_cast<quint8>(data.at(offset)) << 8;
    result |= static_cast<quint8>(data.at(offset + 1));
    
    return result;
}

qint16 DataParser::parseS16(const QByteArray &data, int offset)
{
    return static_cast<qint16>(parseU16(data, offset));
}

quint32 DataParser::parseU32(const QByteArray &data, int offset)
{
    if (data.size() < offset + 4) {
        qWarning() << "Data too small for U32";
        return 0;
    }
    
    // Big-endian: MSB first
    quint32 result = static_cast<quint8>(data.at(offset)) << 24;
    result |= static_cast<quint8>(data.at(offset + 1)) << 16;
    result |= static_cast<quint8>(data.at(offset + 2)) << 8;
    result |= static_cast<quint8>(data.at(offset + 3));
    
    return result;
}

qint32 DataParser::parseS32(const QByteArray &data, int offset)
{
    return static_cast<qint32>(parseU32(data, offset));
}

float DataParser::parseFloat(const QByteArray &data, int offset)
{
    if (data.size() < offset + 4) {
        qWarning() << "Data too small for float";
        return 0.0f;
    }
    
    // Big-endian: Copy bytes in correct order to ensure proper alignment
    union {
        quint32 i;
        float f;
    } u;
    
    u.i = parseU32(data, offset);
    return u.f;
}

QDateTime DataParser::parseTime(const QByteArray &data, int offset)
{
    if (data.size() < offset + 4) {
        qWarning() << "Data too small for time";
        return QDateTime();
    }
    
    quint32 timestamp = parseU32(data, offset);
    QDateTime dateTime = QDateTime::fromSecsSinceEpoch(timestamp);
    
    return dateTime;
}

quint64 DataParser::parseU64(const QByteArray &data, int offset)
{
    if (data.size() < offset + 8) {
        qWarning() << "Data too small for U64";
        return 0;
    }
    
    // Big-endian: MSB first
    quint64 result = static_cast<quint64>(static_cast<quint8>(data.at(offset))) << 56;
    result |= static_cast<quint64>(static_cast<quint8>(data.at(offset + 1))) << 48;
    result |= static_cast<quint64>(static_cast<quint8>(data.at(offset + 2))) << 40;
    result |= static_cast<quint64>(static_cast<quint8>(data.at(offset + 3))) << 32;
    result |= static_cast<quint64>(static_cast<quint8>(data.at(offset + 4))) << 24;
    result |= static_cast<quint64>(static_cast<quint8>(data.at(offset + 5))) << 16;
    result |= static_cast<quint64>(static_cast<quint8>(data.at(offset + 6))) << 8;
    result |= static_cast<quint64>(static_cast<quint8>(data.at(offset + 7)));
    
    return result;
}

qint64 DataParser::parseS64(const QByteArray &data, int offset)
{
    return static_cast<qint64>(parseU64(data, offset));
}

double DataParser::parseDouble(const QByteArray &data, int offset)
{
    if (data.size() < offset + 8) {
        qWarning() << "Data too small for double";
        return 0.0;
    }
    
    // Big-endian: Copy bytes in correct order to ensure proper alignment
    union {
        quint64 i;
        double d;
    } u;
    
    u.i = parseU64(data, offset);
    return u.d;
}

// Reverse byte order parsing methods
quint16 DataParser::parseU16Rev(const QByteArray &data, int offset)
{
    if (data.size() < offset + 2) {
        qWarning() << "Data too small for U16Rev";
        return 0;
    }
    
    // Reversed byte order but still big-endian interpretation
    quint16 result = static_cast<quint8>(data.at(offset + 1)) << 8;
    result |= static_cast<quint8>(data.at(offset));
    
    return result;
}

qint16 DataParser::parseS16Rev(const QByteArray &data, int offset)
{
    return static_cast<qint16>(parseU16Rev(data, offset));
}

float DataParser::parseFloatRev(const QByteArray &data, int offset)
{
    if (data.size() < offset + 4) {
        qWarning() << "Data too small for floatRev";
        return 0.0f;
    }
    
    // Create a new byte array with reversed bytes
    QByteArray reversed;
    reversed.append(data.at(offset + 3));
    reversed.append(data.at(offset + 2));
    reversed.append(data.at(offset + 1));
    reversed.append(data.at(offset));
    
    // Use the standard float parsing on the reversed bytes
    return parseFloat(reversed, 0);
}

QDateTime DataParser::parseTimeRev(const QByteArray &data, int offset)
{
    if (data.size() < offset + 4) {
        qWarning() << "Data too small for timeRev";
        return QDateTime();
    }
    
    // Create a new byte array with reversed bytes
    QByteArray reversed;
    reversed.append(data.at(offset + 3));
    reversed.append(data.at(offset + 2));
    reversed.append(data.at(offset + 1));
    reversed.append(data.at(offset));
    
    // Use the standard time parsing on the reversed bytes
    return parseTime(reversed, 0);
}

double DataParser::parseDoubleRev(const QByteArray &data, int offset)
{
    if (data.size() < offset + 8) {
        qWarning() << "Data too small for doubleRev";
        return 0.0;
    }
    
    // Create a new byte array with reversed bytes
    QByteArray reversed;
    reversed.append(data.at(offset + 7));
    reversed.append(data.at(offset + 6));
    reversed.append(data.at(offset + 5));
    reversed.append(data.at(offset + 4));
    reversed.append(data.at(offset + 3));
    reversed.append(data.at(offset + 2));
    reversed.append(data.at(offset + 1));
    reversed.append(data.at(offset));
    
    // Use the standard double parsing on the reversed bytes
    return parseDouble(reversed, 0);
}

qint64 DataParser::parseS64Rev(const QByteArray &data, int offset)
{
    if (data.size() < offset + 8) {
        qWarning() << "Data too small for S64Rev";
        return 0;
    }
    
    // Create a new byte array with reversed bytes
    QByteArray reversed;
    reversed.append(data.at(offset + 7));
    reversed.append(data.at(offset + 6));
    reversed.append(data.at(offset + 5));
    reversed.append(data.at(offset + 4));
    reversed.append(data.at(offset + 3));
    reversed.append(data.at(offset + 2));
    reversed.append(data.at(offset + 1));
    reversed.append(data.at(offset));
    
    // Use the standard S64 parsing on the reversed bytes
    return parseS64(reversed, 0);
}

QVariant DataParser::parseHexData(const QString &hexString, const QString &parseType)
{
    if (hexString.isEmpty()) {
        return QVariant("无数据可解析");
    }
    
    QByteArray data = hexStringToByteArray(hexString);
    if (data.isEmpty()) {
        return QVariant("无效的十六进制数据");
    }
    
    if (parseType == "u8") {
        if (data.size() < 1) return QVariant("数据不足");
        return QVariant(parseU8(data));
    }
    else if (parseType == "s8") {
        if (data.size() < 1) return QVariant("数据不足");
        return QVariant(parseS8(data));
    }
    else if (parseType == "u16") {
        if (data.size() < 2) return QVariant("数据不足");
        return QVariant(parseU16(data));
    }
    else if (parseType == "s16") {
        if (data.size() < 2) return QVariant("数据不足");
        return QVariant(parseS16(data));
    }
    else if (parseType == "u32") {
        if (data.size() < 4) return QVariant("数据不足");
        return QVariant(parseU32(data));
    }
    else if (parseType == "s32") {
        if (data.size() < 4) return QVariant("数据不足");
        return QVariant(parseS32(data));
    }
    else if (parseType == "float") {
        if (data.size() < 4) return QVariant("数据不足");
        return QVariant(parseFloat(data));
    }
    else if (parseType == "time") {
        if (data.size() < 4) return QVariant("数据不足");
        QDateTime dt = parseTime(data);
        return QVariant(dt.toString("yyyy-MM-dd hh:mm:ss"));
    }
    else if (parseType == "u64") {
        if (data.size() < 8) return QVariant("数据不足");
        return QVariant(QString::number(parseU64(data)));
    }
    else if (parseType == "s64") {
        if (data.size() < 8) return QVariant("数据不足");
        return QVariant(QString::number(parseS64(data)));
    }
    else if (parseType == "double") {
        if (data.size() < 8) return QVariant("数据不足");
        return QVariant(parseDouble(data));
    }
    // Add reverse byte order parsing types
    else if (parseType == "u16_rev") {
        if (data.size() < 2) return QVariant("数据不足");
        return QVariant(parseU16Rev(data));
    }
    else if (parseType == "s16_rev") {
        if (data.size() < 2) return QVariant("数据不足");
        return QVariant(parseS16Rev(data));
    }
    else if (parseType == "float_rev") {
        if (data.size() < 4) return QVariant("数据不足");
        return QVariant(parseFloatRev(data));
    }
    else if (parseType == "time_rev") {
        if (data.size() < 4) return QVariant("数据不足");
        QDateTime dt = parseTimeRev(data);
        return QVariant(dt.toString("yyyy-MM-dd hh:mm:ss"));
    }
    else if (parseType == "double_rev") {
        if (data.size() < 8) return QVariant("数据不足");
        return QVariant(parseDoubleRev(data));
    }
    else if (parseType == "s64_rev") {
        if (data.size() < 8) return QVariant("数据不足");
        return QVariant(QString::number(parseS64Rev(data)));
    }
    else {
        return QVariant("未知的解析类型: " + parseType);
    }
}

QString DataParser::batchParseHexData(const QString &hexString, const QString &ruleString)
{
    if (hexString.isEmpty() || ruleString.isEmpty()) {
        return QString();
    }
    
    QByteArray data = hexStringToByteArray(hexString);
    if (data.isEmpty()) {
        return "无效的十六进制数据";
    }
    
    QStringList rules = ruleString.split(";");
    QStringList results;
    int byteOffset = 0;
    
    for (const QString &rule : rules) {
        QString trimmedRule = rule.trimmed().toLower();
        if (trimmedRule.isEmpty()) continue;
        
        int bytesNeeded = 0;
        QChar ruleChar = trimmedRule.at(0);
        
        // Determine bytes needed based on rule
        switch (ruleChar.toLatin1()) {
            case 'a': // u8
            case 'b': // s8
                bytesNeeded = 1;
                break;
            case 'c': // u16
            case 'd': // s16
            case 'l': // u16_rev
                bytesNeeded = 2;
                break;
            case 'e': // u32
            case 'f': // s32
            case 'g': // float
            case 'h': // time
            case 'm': // float_rev
            case 'n': // time_rev
                bytesNeeded = 4;
                break;
            case 'i': // u64
            case 'j': // s64
            case 'k': // double
            case 'o': // double_rev
            case 'p': // s64_rev
                bytesNeeded = 8;
                break;
            default:
                results.append("未知规则: " + trimmedRule);
                continue;
        }
        
        // Check if we have enough bytes
        if (byteOffset + bytesNeeded > data.size()) {
            results.append("数据不足");
            break;
        }
        
        // Parse according to rule
        switch (ruleChar.toLatin1()) {
            case 'a': // u8
                results.append(QString::number(parseU8(data, byteOffset)));
                break;
            case 'b': // s8
                results.append(QString::number(parseS8(data, byteOffset)));
                break;
            case 'c': // u16
                results.append(QString::number(parseU16(data, byteOffset)));
                break;
            case 'd': // s16
                results.append(QString::number(parseS16(data, byteOffset)));
                break;
            case 'e': // u32
                results.append(QString::number(parseU32(data, byteOffset)));
                break;
            case 'f': // s32
                results.append(QString::number(parseS32(data, byteOffset)));
                break;
            case 'g': // float
                results.append(QString::number(parseFloat(data, byteOffset), 'f', 2));
                break;
            case 'h': // time
                {
                    QDateTime dt = parseTime(data, byteOffset);
                    if (dt.isValid()) {
                        results.append(dt.toString("yyyyMMdd hh:mm:ss"));
                    } else {
                        results.append("无效时间");
                    }
                }
                break;
            case 'i': // u64
                results.append(QString::number(parseU64(data, byteOffset)));
                break;
            case 'j': // s64
                results.append(QString::number(parseS64(data, byteOffset)));
                break;
            case 'k': // double
                results.append(QString::number(parseDouble(data, byteOffset), 'f', 2));
                break;
            // Add reverse byte order parsing cases
            case 'l': // u16_rev
                results.append(QString::number(parseU16Rev(data, byteOffset)));
                break;
            case 'm': // float_rev
                results.append(QString::number(parseFloatRev(data, byteOffset), 'f', 2));
                break;
            case 'n': // time_rev
                {
                    QDateTime dt = parseTimeRev(data, byteOffset);
                    if (dt.isValid()) {
                        results.append(dt.toString("yyyyMMdd hh:mm:ss"));
                    } else {
                        results.append("无效时间");
                    }
                }
                break;
            case 'o': // double_rev
                results.append(QString::number(parseDoubleRev(data, byteOffset), 'f', 2));
                break;
            case 'p': // s64_rev
                results.append(QString::number(parseS64Rev(data, byteOffset)));
                break;
        }
        
        byteOffset += bytesNeeded;
    }
    
    return results.join("; ");
} 