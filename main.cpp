#include <QGuiApplication>
#include <QApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QDebug>
#include <QUrl>
#include <QQmlEngine>
#include <QCoreApplication>
#include <QIcon>
#include <QObject>

// 导入功能模块头文件
#include "LogAnalyzer/loganalyzer.h"
#include "ParamViewer/paramviewer.h"
#include "DatabaseTool/databasetool.h"
#include "DatabaseTool/dataparser.h"
#include "ProtocolParser/messageparser.h"
#include "ProtocolParser/protocoltypes.h"
#include "ProtocolParser/dataidentifierconfig.h"
#include "ProtocolParser/dlt645parser.h"
#include "archivehandler.h"

int main(int argc, char *argv[])
{
    // 启用高DPI缩放，默认情况下Qt应用程序不启用
    // QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);

    // 设置应用程序的组织名称和域名以及应用程序名称
    QCoreApplication::setOrganizationName("CSG");
    QCoreApplication::setOrganizationDomain("csg.com");
    QCoreApplication::setApplicationName("ManageTools_NW");

    QApplication app(argc, argv);
    app.setWindowIcon(QIcon(":/icon.png"));
    // 初始化DataIdentifierConfig单例并测试复合数据格式
    DataIdentifierConfig* config = DataIdentifierConfig::instance();
    bool loadResult = config->loadFromXml("config/dlt645_2007_config.xml", ProtocolType::DLT645_2007);
    if (!loadResult) {
        qWarning() << "DLT645-2007配置文件加载失败:" << config->getLastError();
    }

    ArchiveHandler archiveHandler;

    // 创建QML引擎并将指针赋值给全局变量
    QQmlApplicationEngine engine;
    g_enginePtr = &engine;

    // 使用局部引擎变量
    engine.rootContext()->setContextProperty("archiveHandler", &archiveHandler);

    // 创建DatabaseTool实例并暴露给QML
    DatabaseTool dbTool;
    engine.rootContext()->setContextProperty("dbToolCpp", &dbTool);

    // 注册DatabaseTool类型到QML
    qmlRegisterType<DatabaseTool>("com.nw.databasetool", 1, 0, "DatabaseToolCpp");

    // 注册DataParser类型到QML
    qmlRegisterType<DataParser>("com.nw.databasetool", 1, 0, "DataParser");

    // 注册MessageParser类型到QML
    qmlRegisterType<MessageParser>("com.nw.protocolparser", 1, 0, "MessageParser");

    // 注册协议解析相关的元类型，使其可以在QML中使用
    qRegisterMetaType<ProtocolType>("ProtocolType");
    qRegisterMetaType<ProtocolParseResult>("ProtocolParseResult");
    qRegisterMetaType<ByteSegmentResult>("ByteSegmentResult");
    qRegisterMetaType<FrameParseResult>("FrameParseResult");

    // DataIdentifierConfig单例已在上面初始化
    qDebug() << "主程序启动：DataIdentifierConfig单例已初始化完成";

    const QUrl url(QStringLiteral("qrc:/main.qml"));
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);

    // 连接extractionCompleted信号到QML的extractArchive方法
    QObject::connect(&archiveHandler, &ArchiveHandler::extractionCompleted,
                     [](const QString &termDiskPath) {
        if (g_enginePtr) {
            QObject *rootObject = g_enginePtr->rootObjects().first();
            if (rootObject) {
                QMetaObject::invokeMethod(rootObject, "extractArchive",
                                      Q_ARG(QVariant, termDiskPath));
            }
        }
    });

    // 连接extractionFailed信号到QML的handleExtractionFailed方法
    QObject::connect(&archiveHandler, &ArchiveHandler::extractionFailed,
                     [](const QString &errorMessage) {
        if (g_enginePtr) {
            QObject *rootObject = g_enginePtr->rootObjects().first();
            if (rootObject) {
                QMetaObject::invokeMethod(rootObject, "handleExtractionFailed",
                                      Q_ARG(QVariant, errorMessage));
            }
        }
    });

    // 连接logFileReadCompleted信号到QML的handleLogFileContent方法
    QObject::connect(&archiveHandler, &ArchiveHandler::logFileReadCompleted,
                     [](const QString &filePath, const QString &content) {
        if (g_enginePtr) {
            QObject *rootObject = g_enginePtr->rootObjects().first();
            if (rootObject) {
                QMetaObject::invokeMethod(rootObject, "handleLogFileContent",
                                      Q_ARG(QVariant, filePath),
                                      Q_ARG(QVariant, content));
            }
        }
    });

    // 连接logProcessingCompleted信号到QML的logProcessingCompleted方法
    QObject::connect(&archiveHandler, &ArchiveHandler::logProcessingCompleted,
                     [](const QString &logsDir) {
        if (g_enginePtr) {
            QObject *rootObject = g_enginePtr->rootObjects().first();
            if (rootObject) {
                QMetaObject::invokeMethod(rootObject, "logProcessingCompleted",
                                      Q_ARG(QVariant, logsDir));
            }
        }
    });

    // 连接lastExtractionReusedChanged信号到处理函数
    QObject::connect(&archiveHandler, &ArchiveHandler::lastExtractionReusedChanged,
                     handleLastExtractionReusedChanged);

    // 连接批处理开始和结束信号
    QObject::connect(&archiveHandler, &ArchiveHandler::batchProcessStarted,
                     [](const QString &message) {
        QMetaObject::invokeMethod(g_enginePtr->rootObjects().first(), "handleBatchProcessStarted",
                                 Qt::QueuedConnection,
                                 Q_ARG(QVariant, message));
    });

    QObject::connect(&archiveHandler, &ArchiveHandler::batchProcessFinished,
                     [](bool success, const QString &message) {
        QMetaObject::invokeMethod(g_enginePtr->rootObjects().first(), "handleBatchProcessFinished",
                                 Qt::QueuedConnection,
                                 Q_ARG(QVariant, success),
                                 Q_ARG(QVariant, message));
    });

    engine.load(url);

    return app.exec();
}