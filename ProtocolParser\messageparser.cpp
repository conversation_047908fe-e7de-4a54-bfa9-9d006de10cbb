#include "messageparser.h"
#include "protocoldetector.h"
#include "dlt645parser.h"
#include "nwupparser.h"

#include <QDebug>
#include <QRegularExpression>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTextEdit>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QSplitter>
#include <QGroupBox>
#include <QFileInfo>
#include <QCoreApplication>
#include <QProcess>
#include <QDir>
#include <QFile>

MessageParser::MessageParser(QObject *parent)
    : QObject(parent)
    , m_detector(std::make_unique<ProtocolDetector>(this))
    , m_dlt645Parser(std::make_unique<DLT645Parser>(this))
    , m_nwupParser(std::make_unique<NWUPParser>(this))
    , m_verboseLogging(false)
{
    emitLog("Info", "MessageParser initialized");

    // 不再需要设置配置文件路径，因为DataIdentifierConfig已经是单例，在主程序启动时已经初始化
    emitLog("Info", "使用全局DataIdentifierConfig单例，无需重复加载配置");
}

MessageParser::~MessageParser()
{
    emitLog("Info", "MessageParser destroyed");
}

ProtocolParseResult MessageParser::parseMessage(const QString &rawMessage)
{
    emitLog("Info", QString("Starting to parse message: %1").arg(rawMessage.left(50) + "..."));

    // 第一步：字节划分
    ByteSegmentResult segmentResult = segmentBytes(rawMessage);
    if (!segmentResult.isValid) {
        ProtocolParseResult result;
        result.errorMessage = segmentResult.errorMessage;
        emitLog("Error", result.errorMessage);
        return result;
    }

    // 第二步：帧结构解析和协议类型判断
    FrameParseResult frameResult = parseFrame(segmentResult.byteArray);
    if (!frameResult.isValid) {
        ProtocolParseResult result;
        result.errorMessage = frameResult.errorMessage;
        emitLog("Error", result.errorMessage);
        return result;
    }

    // 第三步：协议解析
    ProtocolParseResult protocolResult = parseProtocol(frameResult);

    emitLog("Info", QString("Parse completed. Protocol: %1, Valid: %2")
            .arg(ProtocolTypeUtils::protocolTypeToString(protocolResult.protocolType))
            .arg(protocolResult.isValid));

    return protocolResult;
}

ByteSegmentResult MessageParser::segmentBytes(const QString &rawMessage)
{
    ByteSegmentResult result;
    
    if (rawMessage.isEmpty()) {
        result.errorMessage = "输入报文为空";
        return result;
    }

    // 清理输入字符串
    QString cleanHex = cleanHexString(rawMessage);
    emitLog("Debug", QString("Cleaned hex string: %1").arg(cleanHex));

    // 验证十六进制字符串
    if (!isValidHexString(cleanHex)) {
        result.errorMessage = "输入包含无效的十六进制字符";
        return result;
    }

    // 确保偶数长度
    if (cleanHex.length() % 2 != 0) {
        cleanHex.prepend("0");
        emitLog("Debug", "Added leading zero for even length");
    }

    // 转换为字节数组
    result.byteArray = hexStringToByteArray(cleanHex);
    if (result.byteArray.isEmpty()) {
        result.errorMessage = "十六进制字符串转换失败";
        return result;
    }

    // 生成十六进制字节列表
    result.hexBytes = byteArrayToHexList(result.byteArray);
    result.totalBytes = result.byteArray.size();
    result.isValid = true;

    emitLog("Debug", QString("Successfully segmented %1 bytes").arg(result.totalBytes));
    return result;
}

FrameParseResult MessageParser::parseFrame(const QByteArray &byteArray)
{
    if (byteArray.isEmpty()) {
        FrameParseResult result;
        result.errorMessage = "字节数组为空";
        return result;
    }

    emitLog("Debug", QString("Parsing frame with %1 bytes").arg(byteArray.size()));
    
    // 使用协议检测器进行帧解析
    return m_detector->detectProtocol(byteArray);
}

ProtocolParseResult MessageParser::parseProtocol(const FrameParseResult &frameResult)
{
    if (!frameResult.isValid) {
        ProtocolParseResult result;
        result.errorMessage = frameResult.errorMessage;
        return result;
    }

    emitLog("Debug", QString("Parsing protocol: %1")
            .arg(ProtocolTypeUtils::protocolTypeToString(frameResult.protocolType)));

    // 根据协议类型选择解析器
    switch (frameResult.protocolType) {
    case ProtocolType::DLT645_2007:
        return m_dlt645Parser->parseFrame(frameResult.frameData);
    
    case ProtocolType::NW_UP:
        return m_nwupParser->parseFrame(frameResult.frameData);
    
    case ProtocolType::DLT645_1997:
        // TODO: 实现DL/T645-1997解析器
        {
            ProtocolParseResult result;
            result.errorMessage = "DL/T645-1997协议解析器尚未实现";
            return result;
        }
    
    default:
        {
            ProtocolParseResult result;
            result.errorMessage = "不支持的协议类型";
            return result;
        }
    }
}

void MessageParser::setVerboseLogging(bool enabled)
{
    m_verboseLogging = enabled;
    emitLog("Info", QString("Verbose logging %1").arg(enabled ? "enabled" : "disabled"));
}

QStringList MessageParser::getSupportedProtocols() const
{
    return {
        "DL/T645-2007",
        "DL/T645-1997"
    };
}

ProtocolParseResult MessageParser::parseWithProtocol(const QString &rawMessage, 
                                                    const QString &protocolType)
{
    emitLog("Info", QString("Parsing with specified protocol: %1").arg(protocolType));
    
    // 先进行字节划分
    ByteSegmentResult segmentResult = segmentBytes(rawMessage);
    if (!segmentResult.isValid) {
        ProtocolParseResult result;
        result.errorMessage = segmentResult.errorMessage;
        return result;
    }

    // 创建指定协议类型的帧结果
    FrameParseResult frameResult;
    frameResult.isValid = true;
    frameResult.protocolType = ProtocolTypeUtils::stringToProtocolType(protocolType);
    frameResult.rawData = segmentResult.byteArray;
    frameResult.frameData = segmentResult.byteArray;
    frameResult.frameStart = 0;
    frameResult.frameEnd = segmentResult.byteArray.size() - 1;
    frameResult.frameLength = segmentResult.byteArray.size();

    // 使用指定协议进行解析
    return parseProtocol(frameResult);
}

QString MessageParser::cleanHexString(const QString &input)
{
    QString result = input;
    
    // 移除空格、制表符、换行符
    result.remove(QRegularExpression("\\s+"));
    
    // 移除0x前缀（不区分大小写）
    result.remove(QRegularExpression("^0x", QRegularExpression::CaseInsensitiveOption));
    
    // 移除...后缀
    result.remove(QRegularExpression("\\.\\.\\.\\s*$"));
    
    // 转换为大写
    result = result.toUpper();
    
    return result;
}

bool MessageParser::isValidHexString(const QString &hexString)
{
    if (hexString.isEmpty()) {
        return false;
    }
    
    // 检查是否只包含有效的十六进制字符
    QRegularExpression hexRegex("^[0-9A-F]*$");
    return hexRegex.match(hexString).hasMatch();
}

QByteArray MessageParser::hexStringToByteArray(const QString &hexString)
{
    QByteArray result;
    
    for (int i = 0; i < hexString.length(); i += 2) {
        bool ok;
        quint8 byte = hexString.mid(i, 2).toUInt(&ok, 16);
        if (ok) {
            result.append(static_cast<char>(byte));
        } else {
            emitLog("Warning", QString("Failed to convert hex byte at position %1").arg(i));
            return QByteArray(); // 返回空数组表示失败
        }
    }
    
    return result;
}

QStringList MessageParser::byteArrayToHexList(const QByteArray &byteArray)
{
    QStringList result;
    
    for (int i = 0; i < byteArray.size(); ++i) {
        quint8 byte = static_cast<quint8>(byteArray.at(i));
        result.append(QString("%1").arg(byte, 2, 16, QChar('0')).toUpper());
    }
    
    return result;
}

void MessageParser::emitLog(const QString &level, const QString &message)
{
    if (m_verboseLogging || level != "Debug") {
        emit parseLog(level, message);
    }
}



QWidget* MessageParser::createProtocolParserWidget()
{
    QWidget* widget = new QWidget();
    widget->setWindowTitle("协议解析器");
    widget->resize(1000, 700);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(widget);

    // 创建输入区域
    QGroupBox* inputGroup = new QGroupBox("报文输入");
    QVBoxLayout* inputLayout = new QVBoxLayout(inputGroup);

    // 输入框和按钮
    QHBoxLayout* inputControlLayout = new QHBoxLayout();
    QLineEdit* inputEdit = new QLineEdit();
    inputEdit->setPlaceholderText("请输入十六进制报文，如：68 AA AA AA AA AA AA 68 11 04 33 32 34 35 84 16");

    QPushButton* parseButton = new QPushButton("解析");
    QPushButton* clearButton = new QPushButton("清空");

    inputControlLayout->addWidget(inputEdit, 1);
    inputControlLayout->addWidget(parseButton);
    inputControlLayout->addWidget(clearButton);

    inputLayout->addLayout(inputControlLayout);

    // 协议选择
    QHBoxLayout* protocolLayout = new QHBoxLayout();
    QLabel* protocolLabel = new QLabel("协议类型:");
    QComboBox* protocolCombo = new QComboBox();
    protocolCombo->addItem("自动检测", "auto");
    protocolCombo->addItem("DL/T645-2007", "DL/T645-2007");

    protocolLayout->addWidget(protocolLabel);
    protocolLayout->addWidget(protocolCombo);
    protocolLayout->addStretch();

    inputLayout->addLayout(protocolLayout);

    // 创建结果显示区域
    QSplitter* splitter = new QSplitter(Qt::Horizontal);

    // 左侧：字节划分结果
    QGroupBox* byteGroup = new QGroupBox("字节划分");
    QVBoxLayout* byteLayout = new QVBoxLayout(byteGroup);
    QTextEdit* byteEdit = new QTextEdit();
    byteEdit->setReadOnly(true);
    byteEdit->setMaximumHeight(200);
    byteLayout->addWidget(byteEdit);

    // 右侧：解析结果
    QGroupBox* resultGroup = new QGroupBox("解析结果");
    QVBoxLayout* resultLayout = new QVBoxLayout(resultGroup);
    QTextEdit* resultEdit = new QTextEdit();
    resultEdit->setReadOnly(true);
    resultLayout->addWidget(resultEdit);

    splitter->addWidget(byteGroup);
    splitter->addWidget(resultGroup);
    splitter->setStretchFactor(0, 1);
    splitter->setStretchFactor(1, 2);

    // 添加到主布局
    mainLayout->addWidget(inputGroup);
    mainLayout->addWidget(splitter, 1);

    // 连接信号槽
    QObject::connect(parseButton, &QPushButton::clicked, [=]() {
        QString input = inputEdit->text().trimmed();
        if (input.isEmpty()) {
            resultEdit->setPlainText("请输入报文数据");
            return;
        }

        // 显示字节划分结果
        ByteSegmentResult segmentResult = segmentBytes(input);
        if (segmentResult.isValid) {
            byteEdit->setPlainText(QString("原始输入: %1\n\n字节数组: %2\n\n十六进制列表:\n%3")
                                  .arg(input)
                                  .arg(QString(segmentResult.byteArray.toHex(' ').toUpper()))
                                  .arg(segmentResult.hexBytes.join(" ")));
        } else {
            byteEdit->setPlainText(QString("字节划分失败: %1").arg(segmentResult.errorMessage));
            resultEdit->setPlainText("");
            return;
        }

        // 执行解析
        ProtocolParseResult parseResult;
        QString selectedProtocol = protocolCombo->currentData().toString();

        if (selectedProtocol == "auto") {
            parseResult = parseMessage(input);
        } else {
            parseResult = parseWithProtocol(input, selectedProtocol);
        }

        // 显示解析结果
        QString resultText;
        if (parseResult.isValid) {
            resultText += QString("协议类型: %1\n\n")
                         .arg(ProtocolTypeUtils::protocolTypeToString(parseResult.protocolType));

            // 显示解析详细信息
            if (!parseResult.parsedData.isEmpty()) {
                resultText += "解析详细信息:\n";
                for (auto it = parseResult.parsedData.begin(); it != parseResult.parsedData.end(); ++it) {
                    QVariant value = it.value();
                    if (value.type() == QVariant::Map) {
                        resultText += QString("  %1:\n").arg(it.key());
                        QVariantMap subMap = value.toMap();
                        for (auto subIt = subMap.begin(); subIt != subMap.end(); ++subIt) {
                            resultText += QString("    %1: %2\n").arg(subIt.key()).arg(subIt.value().toString());
                        }
                    } else {
                        resultText += QString("  %1: %2\n").arg(it.key()).arg(value.toString());
                    }
                }
            }

            // 显示摘要和详细信息
            if (!parseResult.summary.isEmpty()) {
                resultText += "\n摘要: " + parseResult.summary + "\n";
            }
            if (!parseResult.detailInfo.isEmpty()) {
                resultText += "\n详细信息:\n" + parseResult.detailInfo + "\n";
            }
        } else {
            resultText = QString("解析失败: %1").arg(parseResult.errorMessage);
        }

        resultEdit->setPlainText(resultText);
    });

    QObject::connect(clearButton, &QPushButton::clicked, [=]() {
        inputEdit->clear();
        byteEdit->clear();
        resultEdit->clear();
    });

    // 连接日志信号
    QObject::connect(this, &MessageParser::parseLog, [=](const QString &level, const QString &message) {
        // 可以在这里添加日志显示逻辑
        qDebug() << QString("[%1] %2").arg(level).arg(message);
    });

    return widget;
}

QVariantMap MessageParser::parseMessageForQML(const QString &rawMessage)
{
    ProtocolParseResult result = parseMessage(rawMessage);
    return result.toVariantMap();
}

QVariantMap MessageParser::parseWithProtocolForQML(const QString &rawMessage,
                                                   const QString &protocolType)
{
    ProtocolParseResult result = parseWithProtocol(rawMessage, protocolType);
    return result.toVariantMap();
}

QVariantMap MessageParser::startExternalTool(const QString &toolPath)
{
    QVariantMap result;

    emitLog("Info", QString("尝试启动外部工具: %1").arg(toolPath));

    try {
        // 构建完整路径
        QString appDir = QCoreApplication::applicationDirPath();
        QString fullPath = QDir(appDir).absoluteFilePath(toolPath);

        emitLog("Info", QString("工具完整路径: %1").arg(fullPath));

        // 检查文件是否存在
        if (!QFile::exists(fullPath)) {
            QString error = QString("工具文件不存在: %1").arg(fullPath);
            emitLog("Error", error);
            result["success"] = false;
            result["error"] = error;
            return result;
        }

        // 检查文件是否可执行
        QFileInfo fileInfo(fullPath);
        if (!fileInfo.isExecutable()) {
            QString error = QString("工具文件不可执行: %1").arg(fullPath);
            emitLog("Warning", error);
            // 继续尝试启动，有些系统上isExecutable可能不准确
        }

        // 启动程序
        bool started = QProcess::startDetached(fullPath);

        if (started) {
            QString success = QString("成功启动工具: %1").arg(toolPath);
            emitLog("Info", success);
            result["success"] = true;
            result["toolPath"] = fullPath;
        } else {
            QString error = QString("无法启动工具: %1").arg(fullPath);
            emitLog("Error", error);
            result["success"] = false;
            result["error"] = error;
        }

    } catch (const std::exception& e) {
        QString error = QString("启动工具时发生异常: %1").arg(e.what());
        emitLog("Error", error);
        result["success"] = false;
        result["error"] = error;
    } catch (...) {
        QString error = "启动工具时发生未知异常";
        emitLog("Error", error);
        result["success"] = false;
        result["error"] = error;
    }

    return result;
}
