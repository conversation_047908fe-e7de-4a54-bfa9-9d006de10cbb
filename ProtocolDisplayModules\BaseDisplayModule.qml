import QtQuick 2.12

/**
 * @brief 基础协议显示模块
 * 提供通用的树形显示功能和接口规范
 */
QtObject {
    id: baseModule
    
    // 引用主界面的模型和函数
    property var resultTreeModel: null
    property var addTreeItemWithVisibility: null
    property var messageInput: null
    
    /**
     * @brief 构建协议树形结构的基础接口
     * @param result 解析结果
     * @param level 起始层级
     * @param protocolName 协议名称
     */
    function buildTree(result, level, protocolName) {
        if (!result || !resultTreeModel || !addTreeItemWithVisibility) {
            console.error("BaseDisplayModule: 缺少必要的依赖")
            return
        }
        
        // 计算总字节长度
        var totalBytes = 0
        if (result.parsedData && result.parsedData.frameLength) {
            totalBytes = result.parsedData.frameLength
        }

        // 1级：报文解析（固定）- 添加总字节长度
        var rootName = "报文解析"
        if (totalBytes > 0) {
            rootName = "报文解析 <" + totalBytes + ">"
        }
        addTreeItemWithVisibility(rootName, "", result.summary || "协议解析成功", 0, true, true, true)

        // 调用具体协议的构建方法
        buildProtocolSpecificTree(result.parsedData, level)
    }
    
    /**
     * @brief 协议特定的树形构建方法（子类需要重写）
     * @param parsedData 解析后的数据
     * @param level 层级
     */
    function buildProtocolSpecificTree(parsedData, level) {
        // 默认实现：显示通用信息
        if (parsedData) {
            addTreeItemWithVisibility("协议数据", "", "通用协议数据显示", level, false, false, true)
            
            // 显示一些通用字段
            for (var key in parsedData) {
                var value = parsedData[key]
                if (typeof value === "string" || typeof value === "number") {
                    addTreeItemWithVisibility(key, String(value), key + "字段", level + 1, false, false, true)
                }
            }
        } else {
            addTreeItemWithVisibility("无数据", "", "没有可显示的协议数据", level, false, false, true)
        }
    }
    
    /**
     * @brief 移除16进制前缀的辅助函数
     */
    function removeHexPrefix(hexString) {
        if (typeof hexString === "string") {
            return hexString.replace(/^0X/i, "")
        }
        return hexString
    }
    
    /**
     * @brief 格式化字节数据为显示格式
     */
    function formatBytes(data) {
        if (!data || data.trim() === "") {
            return ""
        }
        
        var bytes = data.split(" ")
        var validBytes = []
        for (var i = 0; i < bytes.length; i++) {
            if (bytes[i].trim() !== "") {
                validBytes.push(bytes[i])
            }
        }
        return validBytes.join(" ")
    }
    
    /**
     * @brief 从原始报文中提取指定位置的字节
     * @param rawMessage 原始报文（无空格）
     * @param startByte 起始字节位置（从1开始）
     * @param byteCount 字节数量
     * @return 格式化的字节字符串
     */
    function extractBytes(rawMessage, startByte, byteCount) {
        if (!rawMessage || rawMessage.length < (startByte + byteCount - 1) * 2) {
            return ""
        }
        
        var startPos = (startByte - 1) * 2
        var endPos = startPos + byteCount * 2
        var extracted = rawMessage.substring(startPos, endPos)
        
        // 格式化为空格分隔的字节
        var result = ""
        for (var i = 0; i < extracted.length; i += 2) {
            if (i > 0) result += " "
            result += extracted.substr(i, 2)
        }
        
        return result
    }
    
    /**
     * @brief 获取原始报文（去除空格）
     */
    function getRawMessage() {
        if (!messageInput || !messageInput.text) {
            return ""
        }
        return messageInput.text.replace(/\s+/g, "").toUpperCase()
    }
    
    /**
     * @brief 初始化模块
     * @param treeModel 树形模型引用
     * @param addItemFunc 添加树项函数引用
     * @param inputRef 输入框引用
     */
    function initialize(treeModel, addItemFunc, inputRef) {
        resultTreeModel = treeModel
        addTreeItemWithVisibility = addItemFunc
        messageInput = inputRef
        console.log("BaseDisplayModule initialized")
    }
}
