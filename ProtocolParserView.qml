import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import QtQml.Models 2.12
import com.nw.protocolparser 1.0
import "ProtocolDisplayModules"

Rectangle {
    id: root
    color: "#f5f5f5"

    property string termDiskPath: ""

    // 创建MessageParser实例
    MessageParser {
        id: messageParser

        Component.onCompleted: {
            // 连接日志信号 - 使用安全的方式
            parseLog.connect(function(level, message) {
                if (typeof addLog === "function") {
                    addLog(level, message)
                } else {
                    console.log("[" + level + "] " + message)
                }
            })

            console.log("MessageParser初始化完成")
        }
    }

    // 协议显示模块
    DLT645DisplayModule {
        id: dlt645DisplayModule
    }

    NWUPDisplayModule {
        id: nwupDisplayModule
    }

    NWPLCDisplayModule {
        id: nwplcDisplayModule
    }

    BaseDisplayModule {
        id: baseDisplayModule
    }

    // 在所有组件加载完成后初始化显示模块
    Component.onCompleted: {
        initializeDisplayModules()
    }

    function initializeDisplayModules() {
        console.log("开始初始化显示模块...")

        // 检查必要的组件是否存在
        if (!resultTreeModel) {
            console.error("resultTreeModel 未定义")
            return
        }
        if (!messageInput) {
            console.error("messageInput 未定义")
            return
        }

        // 初始化所有显示模块
        dlt645DisplayModule.initialize(resultTreeModel, addTreeItemWithVisibility, messageInput)
        nwupDisplayModule.initialize(resultTreeModel, addTreeItemWithVisibility, messageInput)
        nwplcDisplayModule.initialize(resultTreeModel, addTreeItemWithVisibility, messageInput)
        baseDisplayModule.initialize(resultTreeModel, addTreeItemWithVisibility, messageInput)

        console.log("显示模块初始化完成")
    }
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10

        // 上方输入框
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 220
            color: "#ffffff"
            border.color: "#e1e5e9"
            border.width: 1
            radius: 8

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 12

                // 标题栏
                RowLayout {
                    Layout.fillWidth: true

                    Rectangle {
                        width: 4
                        height: 20
                        color: "#3b82f6"
                        radius: 2
                    }

                    Text {
                        text: "报文输入"
                        font.pixelSize: 16
                        font.bold: true
                        color: "#1f2937"
                    }

                    Item { Layout.fillWidth: true }

                    // 朗新解析工具按钮
                    Rectangle {
                        Layout.preferredWidth: 140
                        height: 36
                        color: "#e0f2fe"
                        radius: 18
                        border.color: "#0ea5e9"
                        border.width: 1

                        Text {
                            anchors.centerIn: parent
                            text: "🔧 朗新解析工具"
                            color: "#0369a1"
                            font.pixelSize: 12
                            font.bold: true
                        }

                        MouseArea {
                            anchors.fill: parent
                            cursorShape: Qt.PointingHandCursor
                            onClicked: callLangxinTool()

                            onPressed: parent.color = "#bae6fd"
                            onReleased: parent.color = "#e0f2fe"
                        }
                    }

                    // Expand解析工具按钮
                    Rectangle {
                        Layout.preferredWidth: 140
                        height: 36
                        color: "#f0fdf4"
                        radius: 18
                        border.color: "#22c55e"
                        border.width: 1

                        Text {
                            anchors.centerIn: parent
                            text: "⚡ Expand解析工具"
                            color: "#15803d"
                            font.pixelSize: 12
                            font.bold: true
                        }

                        MouseArea {
                            anchors.fill: parent
                            cursorShape: Qt.PointingHandCursor
                            onClicked: callExpandTool()

                            onPressed: parent.color = "#dcfce7"
                            onReleased: parent.color = "#f0fdf4"
                        }
                    }
                }

                // 工具栏
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 12

                    // 解析按钮
                    Rectangle {
                        Layout.preferredWidth: 80
                        height: 36
                        color: messageInput.text.length > 0 ? "#3b82f6" : "#9ca3af"
                        radius: 6
                        border.color: messageInput.text.length > 0 ? "#2563eb" : "#6b7280"
                        border.width: 1

                        Text {
                            anchors.centerIn: parent
                            text: "🚀 解析"
                            color: "white"
                            font.pixelSize: 12
                            font.bold: true
                        }

                        MouseArea {
                            anchors.fill: parent
                            enabled: messageInput.text.length > 0
                            onClicked: parseMessage()
                            cursorShape: enabled ? Qt.PointingHandCursor : Qt.ArrowCursor

                            onPressed: parent.color = messageInput.text.length > 0 ? "#2563eb" : "#9ca3af"
                            onReleased: parent.color = messageInput.text.length > 0 ? "#3b82f6" : "#9ca3af"
                        }
                    }

                    // 清空按钮
                    Rectangle {
                        Layout.preferredWidth: 80
                        height: 36
                        color: "#f3f4f6"
                        radius: 6
                        border.color: "#d1d5db"
                        border.width: 1

                        Text {
                            anchors.centerIn: parent
                            text: "🗑️ 清空"
                            color: "#6b7280"
                            font.pixelSize: 12
                            font.bold: true
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: clearAll()
                            cursorShape: Qt.PointingHandCursor

                            onPressed: parent.color = "#e5e7eb"
                            onReleased: parent.color = "#f3f4f6"
                        }
                    }

                    Item { Layout.fillWidth: true }

                    // 字符计数
                    Text {
                        text: "字符数: " + messageInput.text.length
                        color: "#9ca3af"
                        font.pixelSize: 11
                    }
                }

                // 输入框
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "#f9fafb"
                    radius: 6
                    border.color: messageInput.activeFocus ? "#3b82f6" : "#d1d5db"
                    border.width: messageInput.activeFocus ? 2 : 1

                    ScrollView {
                        anchors.fill: parent
                        anchors.margins: 8
                        clip: true

                        TextArea {
                            id: messageInput
                            placeholderText: "💡 提示：目前支持DLT/645-2007协议"
                            wrapMode: TextArea.Wrap
                            selectByMouse: true
                            font.family: "Consolas, Monaco, 'Courier New', monospace"
                            font.pixelSize: 12
                            color: "#374151"
                            background: Rectangle { color: "transparent" }
                            placeholderTextColor: "#9ca3af"

                            // 输入验证和格式化
                            onTextChanged: {
                                // 这里可以添加实时验证逻辑
                            }
                        }
                    }
                }
            }
        }
        


        // 下方解析结果显示框 - 树状显示
        GroupBox {
            title: "解析结果"
            Layout.fillWidth: true
            Layout.fillHeight: true

            ColumnLayout {
                anchors.fill: parent
                spacing: 5

                // 表头
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    color: "#f0f0f0"
                    border.color: "#cccccc"
                    border.width: 1

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 5
                        spacing: 0

                        Text {
                            text: "帧域"
                            font.bold: true
                            Layout.preferredWidth: parent.width * 0.3
                            Layout.fillHeight: true
                            verticalAlignment: Text.AlignVCenter
                        }

                        Rectangle {
                            width: 1
                            Layout.fillHeight: true
                            color: "#cccccc"
                        }

                        Text {
                            text: "数据"
                            font.bold: true
                            Layout.preferredWidth: parent.width * 0.2
                            Layout.fillHeight: true
                            verticalAlignment: Text.AlignVCenter
                            leftPadding: 5
                        }

                        Rectangle {
                            width: 1
                            Layout.fillHeight: true
                            color: "#cccccc"
                        }

                        Text {
                            text: "描述"
                            font.bold: true
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            verticalAlignment: Text.AlignVCenter
                            leftPadding: 5
                        }
                    }
                }

                // 树状视图
                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    ListView {
                        id: resultTreeView
                        model: resultTreeModel
                        delegate: treeItemDelegate

                        // 占位文本
                        Text {
                            anchors.centerIn: parent
                            text: "解析结果将在这里显示..."
                            color: "#999999"
                            visible: resultTreeModel.count === 0
                        }
                    }
                }
            }
        }
    }
    
    function parseMessage() {
        if (messageInput.text.trim() === "") {
            return
        }

        // 清空之前的结果
        clearResults()

        // 添加日志
        addLog("Info", "开始解析报文: " + messageInput.text.substring(0, 50) + "...")

        // 调用C++的解析器 - 使用自动检测
        var result = messageParser.parseMessageForQML(messageInput.text.trim())

        // 显示解析结果
        showParseResult(result)
    }
    
    function showParseResult(result) {
        // 清空之前的结果
        resultTreeModel.clear()

        if (!result) {
            addTreeItem("错误", "", "解析结果为空", 0, false)
            return
        }

        var isValid = result.isValid !== undefined ? result.isValid : false
        var protocolName = result.protocolName || result.protocolType || "未知协议"

        if (isValid) {
            // 构建树状结构
            buildTreeFromResult(result, protocolName)
        } else {
            // 显示错误结果
            addTreeItem("解析失败", "", result.errorMessage || "未知错误", 0, false)
            addTreeItem("输入报文", messageInput.text, "原始输入数据", 0, false)
        }
    }
    
    function clearAll() {
        messageInput.text = ""
        clearResults()
    }

    function clearResults() {
        resultTreeModel.clear()
    }
    
    function addLog(level, message) {
        // 简化版本：只在控制台输出日志
        console.log("[" + level + "] " + message)
    }

    function addTreeItem(name, data, description, level, hasChildren, expanded) {
        resultTreeModel.append({
            "name": name,
            "data": data,
            "description": description,
            "level": level || 0,
            "hasChildren": hasChildren || false,
            "expanded": expanded || false,
            "visible": level === 0 || expanded  // 顶级项目或展开的项目默认可见
        })
    }

    function toggleExpanded(index) {
        var item = resultTreeModel.get(index)
        if (item && item.hasChildren) {
            var wasExpanded = item.expanded
            resultTreeModel.setProperty(index, "expanded", !wasExpanded)

            // 如果是折叠，需要隐藏子项目
            if (wasExpanded) {
                hideChildren(index)
            } else {
                showChildren(index)
            }
        }
    }

    function hideChildren(parentIndex) {
        var parentItem = resultTreeModel.get(parentIndex)
        if (!parentItem) return

        var parentLevel = parentItem.level
        var i = parentIndex + 1

        // 隐藏所有子项目（level > parentLevel的连续项目）
        while (i < resultTreeModel.count) {
            var item = resultTreeModel.get(i)
            if (item.level <= parentLevel) {
                break // 遇到同级或更高级的项目，停止
            }
            resultTreeModel.setProperty(i, "visible", false)
            i++
        }
    }

    function showChildren(parentIndex) {
        var parentItem = resultTreeModel.get(parentIndex)
        if (!parentItem) return

        var parentLevel = parentItem.level
        var i = parentIndex + 1

        // 显示直接子项目（level = parentLevel + 1）
        while (i < resultTreeModel.count) {
            var item = resultTreeModel.get(i)
            if (item.level <= parentLevel) {
                break // 遇到同级或更高级的项目，停止
            }
            if (item.level === parentLevel + 1) {
                resultTreeModel.setProperty(i, "visible", true)
            }
            i++
        }
    }

    // 移除16进制前缀的辅助函数
    function removeHexPrefix(hexString) {
        if (typeof hexString === "string") {
            return hexString.replace(/^0X/i, "");
        }
        return hexString;
    }

    function buildTreeFromResult(result, protocolName) {
        // 根据协议类型选择对应的显示模块
        var protocolType = result.protocolType || protocolName
        var protocolTypeStr = result.protocolName || protocolName || ""

        console.log("buildTreeFromResult - protocolType:", protocolType, "protocolName:", protocolTypeStr)

        // 处理数字类型的协议类型（从C++返回的枚举值）
        if (typeof protocolType === "number") {
            switch(protocolType) {
                case 1: // ProtocolType::DLT645_2007
                    console.log("使用DL/T645显示模块 (数字类型)")
                    dlt645DisplayModule.buildTree(result, 1, "DL/T645-2007")
                    return
                case 2: // ProtocolType::DLT645_1997
                    console.log("使用DL/T645显示模块 (数字类型)")
                    dlt645DisplayModule.buildTree(result, 1, "DL/T645-1997")
                    return
                case 3: // ProtocolType::NW_UP
                    console.log("使用NW_UP显示模块 (数字类型)")
                    nwupDisplayModule.buildTree(result, 1, "NW-UP")
                    return
                case 4: // ProtocolType::NW_PLC
                    console.log("使用NW_PLC显示模块 (数字类型)")
                    nwplcDisplayModule.buildTree(result, 1, "NW-PLC")
                    return
            }
        }

        // 处理字符串类型的协议类型
        switch(protocolTypeStr) {
            case "DL/T645-2007":
            case "DL/T645-1997":
                console.log("使用DL/T645显示模块 (字符串类型)")
                dlt645DisplayModule.buildTree(result, 1, protocolTypeStr)
                break
            case "NW-UP":
                console.log("使用NW_UP显示模块 (字符串类型)")
                nwupDisplayModule.buildTree(result, 1, protocolTypeStr)
                break
            case "NW-PLC":
                console.log("使用NW_PLC显示模块 (字符串类型)")
                nwplcDisplayModule.buildTree(result, 1, protocolTypeStr)
                break
            default:
                console.log("使用基础显示模块，协议类型:", protocolType, "协议名称:", protocolTypeStr)
                baseDisplayModule.buildTree(result, 1, protocolTypeStr || "未知协议")
                break
        }
    }

    function addTreeItemWithVisibility(name, data, description, level, hasChildren, expanded, visible) {
        // 计算字节数并添加到名称中
        var displayName = name;
        if (data && data.trim() !== "") {
            var bytes = data.split(" ");
            var validBytes = [];
            for (var i = 0; i < bytes.length; i++) {
                if (bytes[i].trim() !== "") {
                    validBytes.push(bytes[i]);
                }
            }
            var byteCount = validBytes.length;
            if (byteCount > 0) {
                displayName = name + " <" + byteCount + ">";
            }
        }

        resultTreeModel.append({
            "name": displayName,
            "data": data,
            "description": description,
            "level": level || 0,
            "hasChildren": hasChildren || false,
            "expanded": expanded || false,
            "visible": visible !== undefined ? visible : (level === 0)
        });
    }









    function formatHexInput() {
        // 自动格式化十六进制输入
        var text = messageInput.text
        var cursorPos = messageInput.cursorPosition

        // 移除所有非十六进制字符
        var cleaned = text.replace(/[^0-9A-Fa-f]/g, "")

        // 转换为大写
        cleaned = cleaned.toUpperCase()

        // 格式化为每两个字符一组，用空格分隔
        var formatted = ""
        for (var i = 0; i < cleaned.length; i += 2) {
            if (i > 0) formatted += " "
            formatted += cleaned.substr(i, 2)
        }

        if (formatted !== text) {
            messageInput.text = formatted
            // 尝试保持光标位置
            messageInput.cursorPosition = Math.min(cursorPos, formatted.length)
        }
    }

    function formatParsedData(parsedData) {
        var result = ""

        for (var key in parsedData) {
            var value = parsedData[key]

            if (key === "dataFieldInfo" && typeof value === "object" && value !== null) {
                result += "数据域信息:\n"
                result += formatDataFieldInfo(value)
            } else if (typeof value === "object" && value !== null) {
                result += key + ":\n"
                for (var subKey in value) {
                    result += "  " + subKey + ": " + value[subKey] + "\n"
                }
            } else {
                result += key + ": " + value + "\n"
            }
        }

        return result
    }

    function formatDataFieldInfo(dataFieldInfo) {
        var result = ""

        for (var key in dataFieldInfo) {
            var value = dataFieldInfo[key]

            if (key === "parsedValue" && typeof value === "object" && value !== null) {
                // 处理数据块的解析结果
                if (value.items && Array.isArray(value.items)) {
                    result += "  解析结果 (数据块):\n"
                    result += "    " + (value.summary || "数据块解析") + "\n"
                    for (var i = 0; i < value.items.length; i++) {
                        var item = value.items[i]
                        result += "    项目" + item.order + " - " + item.name + ": " + item.parsedValue + " " + (item.unit || "") + "\n"
                    }
                } else {
                    result += "  " + key + ": " + JSON.stringify(value) + "\n"
                }
            } else if (key === "blockItems" && Array.isArray(value)) {
                result += "  数据块项目:\n"
                for (var j = 0; j < value.length; j++) {
                    var blockItem = value[j]
                    result += "    顺序" + blockItem.order + ": " + blockItem.dataId + "\n"
                }
            } else if (typeof value === "object" && value !== null) {
                result += "  " + key + ":\n"
                for (var subKey in value) {
                    result += "    " + subKey + ": " + value[subKey] + "\n"
                }
            } else {
                result += "  " + key + ": " + value + "\n"
            }
        }

        return result
    }

    // 树状数据模型
    ListModel {
        id: resultTreeModel
    }

    // 树状项目委托
    Component {
        id: treeItemDelegate

        Column {
            width: resultTreeView.width
            visible: model.visible !== undefined ? model.visible : true

            // 主项目行
            Rectangle {
                width: parent.width
                height: model.visible ? 32 : 0
                color: {
                    if (model.level === 0) return "#e8f4fd"  // 根节点背景
                    if (model.level === 1) return "#f8fafc"  // 一级节点
                    if (model.level === 2) return "#f1f5f9"  // 二级节点
                    return "#fafbfc"  // 其他节点
                }
                border.color: "#e5e7eb"
                border.width: 0.5
                visible: model.visible !== undefined ? model.visible : true

                // 悬停效果
                MouseArea {
                    anchors.fill: parent
                    hoverEnabled: true
                    onEntered: parent.color = "#e0f2fe"
                    onExited: {
                        if (model.level === 0) parent.color = "#e8f4fd"
                        else if (model.level === 1) parent.color = "#f8fafc"
                        else if (model.level === 2) parent.color = "#f1f5f9"
                        else parent.color = "#fafbfc"
                    }
                }

                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 2
                    spacing: 0

                    // 缩进和展开/折叠图标
                    Item {
                        Layout.preferredWidth: (model.level || 0) * 20 + 25
                        Layout.fillHeight: true

                        // 展开/折叠图标
                        Rectangle {
                            anchors.right: parent.right
                            anchors.verticalCenter: parent.verticalCenter
                            width: 16
                            height: 16
                            color: model.hasChildren ? "#3b82f6" : "transparent"
                            radius: 2
                            visible: model.hasChildren

                            Text {
                                anchors.centerIn: parent
                                text: model.expanded ? "−" : "+"
                                font.pixelSize: 10
                                font.bold: true
                                color: "white"
                            }

                            MouseArea {
                                anchors.fill: parent
                                onClicked: {
                                    if (model.hasChildren) {
                                        toggleExpanded(index)
                                    }
                                }
                            }
                        }
                    }

                    // 帧域列
                    Text {
                        text: model.name || ""
                        Layout.preferredWidth: resultTreeView.width * 0.3 - ((model.level || 0) * 20 + 25)
                        Layout.fillHeight: true
                        verticalAlignment: Text.AlignVCenter
                        elide: Text.ElideRight
                        font.pixelSize: 11
                        font.bold: model.level <= 1
                        color: {
                            if (model.level === 0) return "#1e40af"  // 根节点蓝色
                            if (model.level === 1) return "#374151"  // 一级节点深灰
                            if (model.level === 2) return "#4b5563"  // 二级节点中灰
                            return "#6b7280"  // 其他节点浅灰
                        }
                    }

                    Rectangle {
                        width: 1
                        Layout.fillHeight: true
                        color: "#e5e7eb"
                    }

                    // 数据列
                    Rectangle {
                        Layout.preferredWidth: resultTreeView.width * 0.2
                        Layout.fillHeight: true
                        color: model.data ? "#fef2f2" : "transparent"
                        radius: 3
                        border.color: model.data ? "#fecaca" : "transparent"
                        border.width: 1

                        Text {
                            anchors.centerIn: parent
                            text: model.data || ""
                            verticalAlignment: Text.AlignVCenter
                            horizontalAlignment: Text.AlignHCenter
                            font.family: "Consolas, Monaco, 'Courier New', monospace"
                            font.pixelSize: 11
                            font.bold: true
                            color: "#dc2626"
                        }
                    }

                    Rectangle {
                        width: 1
                        Layout.fillHeight: true
                        color: "#e5e7eb"
                    }

                    // 描述列
                    Text {
                        text: model.description || ""
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        verticalAlignment: Text.AlignVCenter
                        wrapMode: Text.WordWrap
                        elide: Text.ElideRight
                        font.pixelSize: 11
                        color: "#374151"
                        leftPadding: 8
                    }
                }
            }
        }
    }

    // 第三方解析工具调用函数
    function callLangxinTool() {
        console.log("调用朗新解析工具")


        // 直接打开朗新解析工具
        var toolPath = "tools/朗新报文解析-2023.exe"  // 请根据实际文件名修改
        callExternalTool(toolPath, "", "朗新解析工具")
    }

    function callExpandTool() {
        console.log("调用Expand解析工具")
        // 直接打开Expand解析工具
        var toolPath = "tools/expand200430.exe"  // 请根据实际文件名修改
        callExternalTool(toolPath, "", "Expand解析工具")
    }

    function callExternalTool(toolPath, inputData, toolName) {
        var result = messageParser.startExternalTool(toolPath)
        if (result && result.success) {
            addLog("Info", toolName + "启动成功")
        } else {
            addLog("Error", toolName + "启动失败: " + (result ? result.error : "未知错误"))
        }
    
    }
}
