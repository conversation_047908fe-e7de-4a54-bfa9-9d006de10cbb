# XML配置文件规则说明

## 1. 文件整体结构

```xml
<?xml version="1.0" encoding="UTF-8"?>
<root protocol="DL/T645-2007" version="2.0">
    <description>DL/T645-2007协议数据标识配置文件</description>
    <data_categories>
        <!-- 所有数据项和数据块配置 -->
    </data_categories>
</root>
```

## 2. 数据项配置规则

### 2.1 简单数据项配置

```xml
<data_item id="02010100" 
           name="A相电压" 
           format="0.1" 
           length="2" 
           unit="V" 
           encoding="BCD" 
           description="A相电压瞬时值"/>
```

**属性说明：**
- `id`: **必填** - 8位十六进制数据标识符
- `name`: 可选 - 数据项名称（简单数据项使用）
- `format`: **必填** - 数据格式
  - `"1"`: 整数
  - `"0.1"`: 一位小数
  - `"0.01"`: 两位小数
  - `"0.0001"`: 四位小数
  - `"YYMMDDhhmmss"`: 时间格式
- `length`: **必填** - 数据长度（字节数）
- `unit`: 可选 - 数据单位
- `encoding`: **必填** - 编码方式（通常为"BCD"或"ASCII"）
- `description`: 可选 - 数据项描述

### 2.2 带DI字段的复杂数据项

```xml
<data_item id="00000000"
           format="0.01" 
           length="4" 
           unit="kWh" 
           encoding="BCD"
           DI0="00-0C"
           DI1="00-3F"
           DI2="00-0A">
    <!-- DI字段名称映射 -->
    <DI0_names>
        <name code="00" value="(当前)"/>
        <name code="01-0C" pattern="上{DI0}结算日" conversion="hex_to_decimal"/>
    </DI0_names>
    <DI1_names>
        <name code="00" value="总"/>
        <name code="01-3F" pattern="费率{DI1}" conversion="hex_to_decimal"/>
    </DI1_names>
    <DI2_names>
        <name code="00" value="组合有功"/>
        <name code="01" value="正向有功"/>
        <!-- 更多映射 -->
    </DI2_names>
    
    <!-- 命名规则 -->
    <name_rule>{DI0_name}{DI2_name}{DI1_name}电能</name_rule>
    <description_rule>{DI0_name}{DI2_name}{DI1_name}电能</description_rule>
</data_item>
```

**DI字段配置规则：**
- `DI0`, `DI1`, `DI2`, `DI3`: DI字段的取值范围
  - 单个值：`"00"`
  - 连续范围：`"01-0C"`
  - 离散值：`"19,1C"`

**DI名称映射规则：**
- `code`: 十六进制值或范围
- `value`: 固定名称
- `pattern`: 名称模板，支持变量`{DI0}`, `{DI1}`, `{DI2}`, `{DI3}`
- `conversion`: 转换方式
  - `"hex_to_decimal"`: 十六进制转十进制

**命名规则变量：**
- `{DI0_name}`, `{DI1_name}`, `{DI2_name}`, `{DI3_name}`: 对应DI字段的名称

### 2.3 复合格式数据项

```xml
<data_item id="01010000" encoding="BCD" DI0="00-0C" DI1="00-3F" DI2="01-0A">
    <composite_format>
        <field name="{DI2_name}最大需量值" format="0.0001" length="3" unit="kW" offset="0"/>
        <field name="发生时间" format="YYMMDDhhmm" length="5" unit="年月日时分" offset="3"/>
    </composite_format>
    <total_length>8</total_length>
    <!-- DI名称映射和命名规则 -->
</data_item>
```

**复合格式字段属性：**
- `name`: 字段名称（支持变量替换）
- `format`: 字段数据格式
- `length`: 字段长度
- `unit`: 字段单位
- `offset`: 字段在数据中的偏移量

## 3. 数据块配置规则

```xml
<data_block id="0000FF00"
            format="BLOCK" 
            encoding="BCD"
            DI0="00-0C"
            DI2="00-0A">
    <!-- DI名称映射 -->
    <DI0_names>
        <name code="00" value="(当前)"/>
        <name code="01-0C" pattern="上{DI0}结算日" conversion="hex_to_decimal"/>
    </DI0_names>
    
    <!-- 命名规则 -->
    <name_rule>{DI0_name}{DI2_name}电能数据块</name_rule>
    <description_rule>{DI0_name}{DI2_name}电能数据块</description_rule>
    
    <!-- 包含的数据项 -->
    <include_items>
        <item_pattern base_id="00000000" DI1_range="00-3F" item_length="4"/>
    </include_items>
</data_block>
```

**数据块特殊属性：**
- `format`: 必须为`"BLOCK"`
- `include_items`: 定义包含的数据项模式
  - `base_id`: 基础数据项ID
  - `DI1_range`: DI1字段范围
  - `item_length`: 每个项的长度


## 5. 添加新配置的步骤

### 5.1 添加简单数据项
1. 确定数据标识符ID
2. 设置基本属性（format, length, unit, encoding）
3. 添加name和description

```xml
<data_item id="新ID" 
           name="数据项名称" 
           format="数据格式" 
           length="长度" 
           unit="单位" 
           encoding="BCD" 
           description="描述"/>
```

### 5.2 添加复杂数据项
1. 定义DI字段范围
2. 配置DI名称映射
3. 设置命名规则
4. 如果是复合数据，配置composite_format

### 5.3 添加数据块
1. 设置format="BLOCK"
2. 定义include_items中的item_pattern
3. 配置命名规则

## 6. 常见配置模式

### 6.1 电能数据模式
```xml
<data_item id="00xxxxxx" format="0.01" length="4" unit="kWh" encoding="BCD"
           DI0="00-0C" DI1="00-3F" DI2="具体范围">
```

### 6.2 瞬时值数据模式
```xml
<data_item id="02xxxxxx" format="0.1或0.001" length="2或3" unit="V或A" encoding="BCD">
```

### 6.3 事件记录模式
```xml
<data_item id="03xxxxxx" encoding="BCD">
    <composite_format>
        <field name="发生时刻" format="YYMMDDhhmmss" length="6" unit="年月日时分秒" offset="0"/>
        <!-- 其他字段 -->
    </composite_format>
</data_item>
```

## 7. 注意事项

### 7.1 ID唯一性
- 每个data_item和data_block的id必须唯一
- 建议按数据类别顺序编排ID

### 7.2 DI字段一致性
- DI字段范围要与实际协议规范一致
- DI名称映射要完整覆盖所有可能值

### 7.3 数据格式准确性
- format和length要与实际数据格式匹配
- 复合格式的offset要正确计算

### 7.4 命名规范
- 使用有意义的中文名称
- 保持命名风格一致
- 合理使用变量替换

### 7.5 文档维护
- 添加新配置时要更新相关注释
- 保持配置文件的可读性
- 定期检查配置的正确性

## 8. 详细配置示例

### 8.1 电能量数据配置示例

```xml
<!-- 基本电能数据 -->
<data_item id="00000000"
           format="0.01" length="4" unit="kWh" encoding="BCD"
           DI0="00-0C" DI1="00-3F" DI2="00-0A">
    <DI0_names>
        <name code="00" value="(当前)"/>
        <name code="01-0C" pattern="上{DI0}结算日" conversion="hex_to_decimal"/>
    </DI0_names>
    <DI1_names>
        <name code="00" value="总"/>
        <name code="01-3F" pattern="费率{DI1}" conversion="hex_to_decimal"/>
    </DI1_names>
    <DI2_names>
        <name code="00" value="组合有功"/>
        <name code="01" value="正向有功"/>
        <name code="02" value="反向有功"/>
    </DI2_names>
    <name_rule>{DI0_name}{DI2_name}{DI1_name}电能</name_rule>
    <description_rule>{DI0_name}{DI2_name}{DI1_name}电能</description_rule>
</data_item>

<!-- 对应的数据块 -->
<data_block id="0000FF00" format="BLOCK" encoding="BCD" DI0="00-0C" DI2="00-0A">
    <DI0_names>
        <name code="00" value="(当前)"/>
        <name code="01-0C" pattern="上{DI0}结算日" conversion="hex_to_decimal"/>
    </DI0_names>
    <DI2_names>
        <name code="00" value="组合有功"/>
        <name code="01" value="正向有功"/>
        <name code="02" value="反向有功"/>
    </DI2_names>
    <name_rule>{DI0_name}{DI2_name}电能数据块</name_rule>
    <description_rule>{DI0_name}{DI2_name}电能数据块</description_rule>
    <include_items>
        <item_pattern base_id="00000000" DI1_range="00-3F" item_length="4"/>
    </include_items>
</data_block>
```

### 8.2 最大需量数据配置示例

```xml
<data_item id="01010000" encoding="BCD" DI0="00-0C" DI1="00-3F" DI2="01-0A">
    <composite_format>
        <field name="{DI2_name}最大需量值" format="0.0001" length="3" unit="kW" offset="0"/>
        <field name="发生时间" format="YYMMDDhhmm" length="5" unit="年月日时分" offset="3"/>
    </composite_format>
    <total_length>8</total_length>
    <DI0_names>
        <name code="00" value="(当前)"/>
        <name code="01-0C" pattern="上{DI0}结算日" conversion="hex_to_decimal"/>
    </DI0_names>
    <DI1_names>
        <name code="00" value="总"/>
        <name code="01-3F" pattern="费率{DI1}" conversion="hex_to_decimal"/>
    </DI1_names>
    <DI2_names>
        <name code="01" value="正向有功"/>
        <name code="02" value="反向有功"/>
        <name code="09" value="正向视在"/>
        <name code="0A" value="反向视在"/>
    </DI2_names>
    <name_rule>{DI0_name}{DI2_name}{DI1_name}</name_rule>
    <description_rule>{DI0_name}{DI2_name}{DI1_name}</description_rule>
</data_item>
```

### 8.3 事件记录数据配置示例

```xml
<data_item id="03010101" encoding="BCD" DI0="01-0A" DI1="01-03" DI2="01-04">
    <composite_format>
        <field name="发生时刻" format="1" length="6" unit="" offset="0"/>
        <field name="结束时刻" format="1" length="6" unit="" offset="6"/>
        <field name="正向有功总电能增量" format="0.01" length="4" unit="kWh" offset="12"/>
        <field name="反向有功总电能增量" format="0.01" length="4" unit="kWh" offset="16"/>
        <field name="A相电压" format="0.1" length="2" unit="V" offset="44"/>
        <field name="A相电流" format="0.001" length="3" unit="A" offset="46"/>
    </composite_format>
    <total_length>132</total_length>
    <DI0_names>
        <name code="01-0A" pattern="上{DI0}次" conversion="hex_to_decimal"/>
    </DI0_names>
    <DI1_names>
        <name code="01" value="A相"/>
        <name code="02" value="B相"/>
        <name code="03" value="C相"/>
    </DI1_names>
    <DI2_names>
        <name code="01" value="失压"/>
        <name code="02" value="欠压"/>
        <name code="03" value="过压"/>
        <name code="04" value="断相"/>
    </DI2_names>
    <name_rule>{DI0_name}{DI1_name}{DI2_name}记录</name_rule>
    <description_rule>{DI0_name}{DI1_name}{DI2_name}记录</description_rule>
</data_item>
```

## 9. 高级配置技巧

### 9.1 变量替换的使用
- 在field的name属性中使用变量：`name="{DI2_name}最大需量值"`
- 在命名规则中组合多个变量：`{DI0_name}{DI2_name}{DI1_name}电能`
- 支持的变量：`{DI0}`, `{DI1}`, `{DI2}`, `{DI3}`, `{DI0_name}`, `{DI1_name}`, `{DI2_name}`, `{DI3_name}`

### 9.2 范围配置的灵活性
```xml
<!-- 连续范围 -->
<name code="01-0C" pattern="上{DI0}结算日" conversion="hex_to_decimal"/>

<!-- 离散值 -->
<name code="19,1C" pattern="{DI3}时钟乱" conversion="hex_to_decimal"/>

<!-- 单个值 -->
<name code="00" value="(当前)"/>
```

### 9.3 数据块的复用
```xml
<data_block id="0201FF00" format="BLOCK" encoding="BCD">
    <name_rule>电压数据块</name_rule>
    <description_rule>电压数据块</description_rule>
    <include_items>
        <item_pattern base_id="02010100" DI1_range="01-03" item_length="2"/>
    </include_items>
</data_block>
```

## 10. 常见错误和解决方案

### 10.1 ID冲突
**错误**: 多个数据项使用相同的ID
**解决**: 确保每个ID唯一，建议使用有序编号

### 10.2 DI范围不匹配
**错误**: DI字段范围与实际协议不符
**解决**: 参考DL/T645-2007标准文档，确保范围正确

### 10.3 偏移量计算错误
**错误**: composite_format中offset计算错误
**解决**: 仔细计算每个字段的起始位置，确保不重叠

### 10.4 变量名称错误
**错误**: 使用了不存在的变量名
**解决**: 只使用预定义的变量：`{DI0_name}`, `{DI1_name}`, `{DI2_name}`, `{DI3_name}`

## 11. 配置验证清单

在添加或修改配置后，请检查以下项目：

- [ ] ID是否唯一
- [ ] 必填属性是否完整（id, format, length, encoding）
- [ ] DI字段范围是否正确
- [ ] DI名称映射是否完整
- [ ] 复合格式的offset是否正确计算
- [ ] total_length是否与实际数据长度匹配
- [ ] 命名规则是否使用了正确的变量
- [ ] 数据块的include_items是否正确引用
- [ ] 单位和描述是否准确

## 12. 维护建议

### 12.1 版本控制
- 每次修改后更新version属性
- 记录修改日志
- 备份重要版本

### 12.2 测试验证
- 添加新配置后进行功能测试
- 验证数据解析的正确性
- 检查命名生成是否符合预期

### 12.3 文档同步
- 配置修改后及时更新相关文档
- 保持注释的准确性
- 定期审查配置的合理性

这个配置规则说明文档提供了完整的指导，帮助开发人员正确地添加、修改和维护协议的XML配置文件。
