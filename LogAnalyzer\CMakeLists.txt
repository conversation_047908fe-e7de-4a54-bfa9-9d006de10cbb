cmake_minimum_required(VERSION 3.14)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

set(LOG_ANALYZER_SOURCES
    loganalyzer.cpp
    loganalyzer.h
)

add_library(LogAnalyzer STATIC ${LOG_ANALYZER_SOURCES})

target_link_libraries(LogAnalyzer
    PRIVATE
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
)

target_include_directories(LogAnalyzer
    PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
) 