cmake_minimum_required(VERSION 3.14)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

set(DATABASE_TOOL_SOURCES
    databasetool.cpp
    databasetool.h
    dataparser.cpp
    dataparser.h
)

add_library(DatabaseTool STATIC ${DATABASE_TOOL_SOURCES})

target_link_libraries(DatabaseTool
    PRIVATE
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Sql
    Qt${QT_VERSION_MAJOR}::Xml
)

target_include_directories(DatabaseTool
    PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
) 