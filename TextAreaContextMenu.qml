import QtQuick 2.12
import QtQuick.Controls 2.12

Menu {
    id: contextMenu
    
    property string filePath: ""
    property bool hasSelection: false
    
    MenuItem {
        text: qsTr("复制选中文本")
        enabled: contextMenu.hasSelection
        onTriggered: {
            // 复制选中的文本
            var textArea = parent.parent;
            if (textArea) {
                textArea.copy();
            }
        }
    }
    
    MenuItem {
        text: qsTr("打开文件所在目录")
        onTriggered: {
            console.log("Menu: 打开文件所在目录", contextMenu.filePath);
            if (contextMenu.filePath && contextMenu.filePath.length > 0) {
                if (typeof archiveHandler !== "undefined" && archiveHandler && 
                    typeof archiveHandler.openFileDirectory === "function") {
                    
                    // 确保使用完整的文件路径
                    var result = archiveHandler.openFileDirectory(contextMenu.filePath);
                    console.log("打开文件目录结果:", result);
                } else {
                    console.error("C++端未实现openFileDirectory方法");
                }
            }
        }
    }
    
    MenuSeparator { }
    
    MenuItem {
        text: qsTr("全选")
        onTriggered: {
            var textArea = parent.parent;
            if (textArea) {
                textArea.selectAll();
            }
        }
    }
    
    onClosed: {
        // 菜单关闭后销毁自身
        contextMenu.destroy();
    }
} 