#ifndef DATABASETOOL_H
#define DATABASETOOL_H

#include <QObject>
#include <QWidget>
#include <QTableView>
#include <QSqlTableModel>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QVariant>
#include <QMap>
#include "dataparser.h"

class DatabaseTool : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(QString termDiskPath READ getTermDiskPath WRITE setTermDiskPath NOTIFY termDiskPathChanged)
    Q_PROPERTY(bool databasePathIsSet READ isDatabasePathSet NOTIFY databasePathChanged)
    Q_PROPERTY(DataParser* dataParser READ getDataParser CONSTANT)

public:
    explicit DatabaseTool(QWidget* parent = nullptr);
    ~DatabaseTool();

    QWidget* createDatabaseWidget();
    
    // 设置termdisk路径
    void setTermDiskPath(const QString& path);
    QString getTermDiskPath() const { return m_termDiskPath; }
    
    // 设置数据库文件路径
    Q_INVOKABLE void setDatabasePath(const QString& path) { m_databasePath = path; emit databasePathChanged(); }
    
    // 检查是否设置了数据库路径
    bool isDatabasePathSet() const { return !m_databasePath.isEmpty(); }
    
    // 获取默认数据库路径
    Q_INVOKABLE QString getDefaultDatabasePath() const;
    
    // 获取表列表
    Q_INVOKABLE QStringList getTablesList();
    
    // 获取表数据
    Q_INVOKABLE QVariantList getTableData(const QString& tableName);
    
    // 执行自定义SQL查询
    Q_INVOKABLE QVariantList executeCustomQuery(const QString& sql);
    
    // 连接数据库方法，从QML可调用
    Q_INVOKABLE bool connectToDatabase();
    
    // 断开数据库连接方法，从QML可调用
    Q_INVOKABLE bool disconnectFromDatabase();
    
    // 检查文件是否存在，从QML可调用
    Q_INVOKABLE bool checkFileExists(const QString& filePath) const;
    
    // 保存文本到文件，从QML可调用
    Q_INVOKABLE bool saveTextToFile(const QString& text, const QString& filePath);
    
    // 列出SQL脚本文件
    Q_INVOKABLE QStringList listSqlScripts() const;
    
    // 加载SQL脚本内容
    Q_INVOKABLE QString loadSqlScript(const QString& scriptName) const;
    
    // 导出表数据到文件
    Q_INVOKABLE bool exportTableData(const QString& tableName, const QString& filePath, const QString& parseRule = QString());
    
    // 导出数据到文件
    Q_INVOKABLE bool exportDataToFile(const QVariantList& data, const QString& filePath, bool isCSV);
    
    // 删除文件
    Q_INVOKABLE bool deleteFile(const QString& filePath);
    
    // 根据表名获取解析规则
    Q_INVOKABLE QString getParseRuleForTable(const QString& tableName) const;
    
    // 根据表名获取解析规则描述
    Q_INVOKABLE QString getDescriptionForTable(const QString& tableName) const;
    
    // 加载所有解析规则到内存
    Q_INVOKABLE bool loadAllParseRules() const;
    
    // 获取数据解析器
    DataParser* getDataParser() const { return m_dataParser; }

signals:
    void termDiskPathChanged();
    void databasePathChanged();
    void parseRulesLoaded(bool success); // 新信号：解析规则加载完成

private slots:
    void executeQuery();

private:
    QComboBox* m_tableSelector;
    QTableView* m_dataView;
    QSqlTableModel* m_tableModel;
    QPushButton* m_connectButton;
    QPushButton* m_executeButton;
    QPushButton* m_disconnectButton;
    QLabel* m_statusLabel;
    bool m_isConnected;
    QString m_termDiskPath;  // 存储termdisk路径
    QString m_databasePath;  // 存储数据库文件路径
    DataParser* m_dataParser; // 数据解析器
    
    // 解析规则缓存
    mutable QMap<QString, QString> m_parseRules;      // 前缀 -> 解析规则
    mutable QMap<QString, QString> m_parseDescriptions; // 前缀 -> 解析规则描述
    mutable bool m_parseRulesLoaded;                  // 解析规则是否已加载
    
    // 导出到CSV格式
    bool exportToCSV(const QVariantList& data, const QString& filePath);
    
    // 导出到Excel格式
    bool exportToExcel(const QVariantList& data, const QString& filePath);
    
    // 从表名中提取前缀
    QString extractPrefixFromTableName(const QString& tableName) const;
};

#endif // DATABASETOOL_H 