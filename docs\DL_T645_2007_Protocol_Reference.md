# DL/T645-2007电能表通信协议规范

## 1. 协议基础

### 1.1 帧格式
```
68H + 地址域(6字节) + 68H + 控制码(1字节) + 数据长度(1字节) + 数据域(可变) + 校验码(1字节) + 16H
```

### 1.2 字节传输格式
- 每字节8位(D7-D0)
- 传输时为11位：起始位(0)+D0+D1+D2+D3+D4+D5+D6+D7+偶校验位+停止位(1)
- 先传低位后传高位

### 1.3 数据传输顺序
- 所有数据项均先传送低位字节，后传送高位字节
- 例如：电能量值123456.78kWh在报文中显示为AB896745

### 1.4 校验码计算
从第一个帧起始符开始到校验码之前的所有各字节的模256的和

## 2. 控制码格式
- D7：传送方向(0主站命令/1从站应答)
- D6：应答标志(0正确/1异常)
- D5：后续帧标志(0无/1有)
- D4~D0：功能码

## 3. 数据域处理
- 接收方需对每字节进行减33H处理
- 发送方需对每字节进行加33H处理

## 4. 数据标识编码
用四个字节DI3、DI2、DI1、DI0区分不同数据项：
- DI2、DI1、DI0中任意字节为FFH时代表数据块
- 例如：00010000H表示当前正向有功总电能
- 例如：000100FFH表示正向有功总电能数据块

## 5. 数据格式说明
- **XXXXXX.XX**：计量值或存储值的整数位和小数位
- **NNNNNN.NN**：设定值的整数位和小数位
- **YY**：年，**MM**：月，**DD**：日，**WW**：星期
- **hh**：时，**mm**：分，**ss**：秒
- 未特殊说明均以两位十进制数表示
- 除ASCII码外，其他数据均采用压缩BCD码

## 6. 功能码定义

### 6.1 读数据功能
- **主站请求**：控制码C=11H
- **从站正常应答**：控制码91H(无后续)/B1H(有后续)
- **从站异常应答**：控制码D1H

### 6.2 读后续数据功能
- **主站请求**：控制码C=12H
- **从站正常应答**：控制码92H(无后续)/B2H(有后续)
- **从站异常应答**：控制码D2H

### 6.3 写数据功能
- **主站请求**：控制码C=14H，需要编程键配合
- **从站正常应答**：控制码94H
- **从站异常应答**：控制码D4H

### 6.4 读通信地址功能
- **主站请求**：控制码C=13H，使用全A地址域
- **从站应答**：控制码93H，返回6字节通信地址

### 6.5 写通信地址功能
- **主站请求**：控制码C=15H，使用全A地址域，需要编程键
- **从站应答**：控制码95H，使用新设置的通信地址

### 6.6 广播校时功能
- **主站请求**：控制码C=08H，使用全9地址域
- **数据域**：秒分时日月年星期(7字节)

### 6.7 冻结命令功能
- **主站请求**：控制码C=16H
- **从站正常应答**：控制码96H
- **从站异常应答**：控制码D6H
- **数据域**：MMDDhhmm时间参数，支持定时冻结和瞬时冻结(99999999)

### 6.8 更改通信速率功能
- **主站请求**：控制码C=17H
- **从站正常应答**：控制码97H
- **从站异常应答**：控制码D7H
- **数据域**：通信速率特征字Z

### 6.9 修改密码功能
- **主站请求**：控制码C=18H，需要编程键配合
- **从站正常应答**：控制码98H
- **从站异常应答**：控制码D8H
- **数据域**：数据标识+原密码权限及密码+新密码权限及密码

### 6.10 最大需量清零功能
- **主站请求**：控制码C=19H，需要编程键配合
- **从站正常应答**：控制码99H
- **从站异常应答**：控制码D9H
- **数据域**：密码权限及密码+操作者代码

### 6.11 电表清零功能
- **主站请求**：控制码C=1AH，需要编程键配合
- **从站正常应答**：控制码9AH
- **从站异常应答**：控制码DAH
- **数据域**：密码权限及密码+操作者代码
- **功能**：清空电能量、最大需量及发生时间、冻结量、事件记录、负荷记录等数据

### 6.12 事件清零功能
- **主站请求**：控制码C=1BH，需要编程键配合
- **从站正常应答**：控制码9BH
- **从站异常应答**：控制码DBH
- **数据域**：密码权限及密码+操作者代码+事件标识
- **事件总清零**：事件标识为FFFFFFFF
- **分项事件清零**：事件标识为具体事件记录数据标识

## 7. 状态字定义

### 7.1 电表运行状态字1
- Bit1：需量积算方式(0消差，1区间)
- Bit2：时钟电池(0正常，1欠压)
- Bit3：停电抄表电池(0正常，1欠压)
- Bit4：有功功率方向(0正向，1反向)
- Bit5：无功功率方向(0正向，1反向)

### 7.2 电表运行状态字2
- Bit0:A相有功功率方向(0正向，1反向)
- Bit1:B相有功功率方向(0正向，1反向)
- Bit2:C相有功功率方向(0正向，1反向)
- Bit3:保留
- Bit4:A相无功功率方向(0正向，1反向)
- Bit5:B相无功功率方向(0正向，1反向)
- Bit6:C相无功功率方向(0正向，1反向)


### 7.3 电表运行状态字3(操作类)
- Bit0：当前运行时段(0第一套，1第二套)
- Bit1-2：供电方式(00主电源，01辅助电源，10电池供电)
- Bit3：编程允许(0禁止，1允许)
- Bit4：继电器状态(0通，1断)

### 7.4 电表运行状态字4-6(A/B/C相故障状态)
- Bit0：失压
- Bit1：欠压
- Bit2：过压
- Bit3：失流
- Bit4：过流
- Bit5：过载
- Bit6：潮流反向
- Bit7：断相
- Bit8：断流
- 注：0代表无此类故障，1代表当前发生此类故障

### 7.5 电表运行状态字7(合相故障状态)
- Bit0：电压逆相序
- Bit1：电流逆相序
- Bit2：电压不平衡
- Bit3：电流不平衡
- Bit4：辅助电源失电
- Bit5：掉电
- Bit6：需量超限
- 注：0代表无此类故障，1代表当前发生此类故障

## 8. 特征字定义

### 8.1 有功组合方式特征字
- Bit0：正向有功(0不加，1加)
- Bit1：正向有功(0不减，1减)
- Bit2：反向有功(0不加，1加)
- Bit3：反向有功(0不减，1减)

### 8.1 无功组合方式1、2特征字
- Bit0： I象限(0不加，1加)
- Bit1： I象限(0不减，1减)
- Bit2： II象限(0不加，1加)
- Bit3： II象限(0不减，1减)
- Bit4： III象限(0不加，1加)
- Bit5： III象限(0不减，1减)
- Bit6： IV象限(0不加，1加)
- Bit7： IV象限(0不减，1减)


### 8.2 通信速率特征字
- Bit1：600bps
- Bit2：1200bps
- Bit3：2400bps
- Bit4：4800bps
- Bit5：9600bps
- Bit6：19200bps
注：0代表非当前接口通信速率，1代表当前接口通信速率，特征字仅在某一位为1时有效。
### 8.3 周休日特征字
- Bit0-6：周日到周六
- 注：0代表休息，1代表工作


### 8.4 负荷记录模式字  
Bit0:电压、电流、频率
Bit1:有、无功功率
Bit2:功率因数
Bit3:有、无功总电量
Bit4:四象限总电能
Bit5:当前需量
注：0代表不记录此类数据，1代表记录此类数据

### 8.5 冻结数据模式字  
Bit0：正向有功电能
Bit1：反向有功电能
Bit2：组合无功1电能
Bit3：组合无功2电能
Bit4：四象限无功电能
Bit5：正向有功最大需量及发生时间
Bit6：反向有功最大需量及发生时间
Bit7：变量
注：0代表不记录此类数据，1代表记录此类数据
## 9. 错误信息字
- Bit0：其他错误
- Bit1：无请求数据
- Bit2：密码错误/未授权
- Bit3：通信速率不能更改
- Bit4：年时区数据超
- Bit5：日时段数据超
- Bit6：费率数超
- 注：0代表无相应错误发生，1代表相应错误发生

## 10. 数据类型分类
1. **电能量**：各种电能量数据
2. **最大需量及发生时间**：需量相关数据
3. **变量**：实时测量数据
4. **事件记录**：各种事件记录
5. **参变量**：参数和变量
6. **冻结量**：冻结数据
7. **负荷记录**：负荷曲线数据

## 11. 安全机制
- 密码分级权限管理(00-09级，00为最高权限)
- 编程键物理安全保护
- 事件记录保护机制
- 通信地址保护
