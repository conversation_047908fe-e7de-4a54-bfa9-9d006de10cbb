cmake_minimum_required(VERSION 3.16)

project(ProtocolParser)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Core Xml Widgets REQUIRED)

set(SOURCES
    messageparser.cpp
    protocoldetector.cpp
    dlt645parser.cpp
    nwupparser.cpp
    dataidentifierconfig.cpp
)

set(HEADERS
    messageparser.h
    protocoldetector.h
    dlt645parser.h
    nwupparser.h
    protocoltypes.h
    dataidentifierconfig.h
)

add_library(ProtocolParser STATIC ${SOURCES} ${HEADERS})

target_link_libraries(ProtocolParser Qt${QT_VERSION_MAJOR}::Core Qt${QT_VERSION_MAJOR}::Xml Qt${QT_VERSION_MAJOR}::Widgets)

target_include_directories(ProtocolParser PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
