#include "dlt645parser.h"
#include <QDebug>

DLT645Parser::DLT645Parser(QObject *parent)
    : QObject(parent)
    , m_dataConfig(DataIdentifierConfig::instance())
{
    initializeMappings();
}

DLT645Parser::~DLT645Parser()
{
}

void DLT645Parser::setDataConfig(DataIdentifierConfig* config)
{
    m_dataConfig = config;
}

ProtocolParseResult DLT645Parser::parseFrame(const QByteArray &frameData)
{
    if (frameData.isEmpty()) {
        return createErrorResult("帧数据为空");
    }

    if (frameData.size() < 12) {
        return createErrorResult("帧数据长度不足");
    }

    QVariantMap parsedData;
    QString summary;
    QString detailInfo;

    try {
        // 解析帧结构
        parsedData["frameLength"] = frameData.size();
        parsedData["rawFrame"] = formatHexString(frameData);

        // 1. 解析起始标志
        quint8 startFlag1 = static_cast<quint8>(frameData.at(0));
        parsedData["startFlag1"] = QString("0x%1").arg(startFlag1, 2, 16, QChar('0')).toUpper();

        // 2. 解析地址域（6字节）
        QByteArray addressData = frameData.mid(1, 6);
        QString address = parseAddress(addressData);
        parsedData["address"] = address;

        // 3. 解析第二个起始标志
        quint8 startFlag2 = static_cast<quint8>(frameData.at(7));
        parsedData["startFlag2"] = QString("0x%1").arg(startFlag2, 2, 16, QChar('0')).toUpper();

        // 4. 解析控制码
        quint8 controlCode = static_cast<quint8>(frameData.at(8));
        QVariantMap controlInfo = parseControlCode(controlCode);
        parsedData["controlCode"] = controlInfo;

        // 5. 解析数据长度
        quint8 dataLength = static_cast<quint8>(frameData.at(9));
        parsedData["dataLength"] = dataLength;

        // 6. 解析数据域
        if (dataLength > 0 && frameData.size() >= 10 + dataLength + 2) {
            QByteArray rawDataField = frameData.mid(10, dataLength);
            QByteArray processedDataField = processDataField(rawDataField);

            parsedData["rawDataField"] = formatHexString(rawDataField);
            parsedData["processedDataField"] = formatHexString(processedDataField);

            QVariantMap dataFieldInfo = parseDataField(processedDataField, controlCode, rawDataField);
            parsedData["dataFieldInfo"] = dataFieldInfo;
        }

        // 7. 解析校验码
        quint8 checksum = static_cast<quint8>(frameData.at(frameData.size() - 2));
        parsedData["checksum"] = QString("0x%1").arg(checksum, 2, 16, QChar('0')).toUpper();

        // 8. 解析结束标志
        quint8 endFlag = static_cast<quint8>(frameData.at(frameData.size() - 1));
        parsedData["endFlag"] = QString("0x%1").arg(endFlag, 2, 16, QChar('0')).toUpper();

        // 生成摘要信息
        summary = QString("DL/T645-2007 地址:%1 功能:%2")
                  .arg(address)
                  .arg(controlInfo["functionDescription"].toString());

        // 生成详细信息
        detailInfo = QString("帧长度: %1字节\n地址域: %2\n控制码: %3\n数据长度: %4字节")
                     .arg(frameData.size())
                     .arg(address)
                     .arg(controlInfo["description"].toString())
                     .arg(dataLength);

        return createSuccessResult(parsedData, summary, detailInfo);

    } catch (const std::exception &e) {
        return createErrorResult(QString("解析异常: %1").arg(e.what()));
    } catch (...) {
        return createErrorResult("未知解析异常");
    }
}

QString DLT645Parser::parseAddress(const QByteArray &addressData)
{
    if (addressData.size() != 6) {
        return "地址长度错误";
    }

    // DL/T645地址域是BCD码，低字节在前
    QString address;
    for (int i = addressData.size() - 1; i >= 0; --i) {
        quint8 byte = static_cast<quint8>(addressData.at(i));
        address += QString("%1").arg(byte, 2, 16, QChar('0')).toUpper();
    }

    return address;
}

QVariantMap DLT645Parser::parseControlCode(quint8 controlCode)
{
    QVariantMap result;

    result["value"] = QString("0x%1").arg(controlCode, 2, 16, QChar('0')).toUpper();

    // 解析控制码各位
    bool isResponse = (controlCode & 0x80) != 0;           // D7: 传送方向
    bool hasError = (controlCode & 0x40) != 0;             // D6: 应答标志
    bool hasFollowFrame = (controlCode & 0x20) != 0;       // D5: 后续帧标志
    quint8 functionCode = controlCode & 0x1F;              // D4~D0: 功能码

    result["direction"] = isResponse ? "从站应答" : "主站命令";
    result["isResponse"] = isResponse;
    result["hasError"] = hasError;
    result["errorFlag"] = hasError ? "异常" : "正确";
    result["hasFollowFrame"] = hasFollowFrame;
    result["followFrameFlag"] = hasFollowFrame ? "有后续帧" : "无后续帧";
    result["functionCode"] = functionCode;
    result["functionDescription"] = getFunctionDescription(functionCode);

    QString description = QString("%1 %2 %3 功能码:%4(%5)")
                         .arg(result["direction"].toString())
                         .arg(result["errorFlag"].toString())
                         .arg(result["followFrameFlag"].toString())
                         .arg(functionCode)
                         .arg(result["functionDescription"].toString());

    result["description"] = description;

    return result;
}

QVariantMap DLT645Parser::parseDataField(const QByteArray &dataField, quint8 controlCode, const QByteArray &rawDataField)
{
    QVariantMap result;

    if (dataField.isEmpty()) {
        result["type"] = "空数据域";
        return result;
    }

    quint8 functionCode = controlCode & 0x1F;
    bool isResponse = (controlCode & 0x80) != 0;
    bool hasError = (controlCode & 0x40) != 0;

    // 如果是异常应答，解析错误码
    if (isResponse && hasError) {
        if (dataField.size() >= 1) {
            quint8 errorCode = static_cast<quint8>(dataField.at(0));
            QVariantMap errorInfo = parseErrorCode(errorCode);
            result["errorInfo"] = errorInfo;
            result["type"] = "异常应答";
        }
        return result;
    }

    // 根据功能码解析数据域
    switch (functionCode) {
    case 0x11: // 读数据
        if (isResponse) {
            result = parseReadDataResponse(dataField, rawDataField);
        } else {
            // 主站请求读数据 - 根据数据域长度判断帧格式
            result = parseReadDataRequest(dataField);
            result["type"] = "读数据请求";
        }
        break;

    case 0x12: // 读后续数据
        result = parseReadContinuousDataRequest(dataField);
        result["type"] = "读后续数据";
        break;

    case 0x13: // 读通信地址
        if (isResponse) {
            result = parseReadAddressResponse(dataField);
        } else {
            result["type"] = "读通信地址请求";
            result["note"] = "无数据域";
        }
        break;

    case 0x14: // 写数据
        result = parseWriteDataRequest(dataField, rawDataField);
        result["type"] = "写数据请求";
        break;

    case 0x15: // 写通信地址
        result = parseWriteAddressRequest(dataField);
        result["type"] = "写通信地址";
        break;

    case 0x08: // 广播校时
        // 广播校时使用统一的数据标识符解析方式
        if (dataField.size() >= 4) {
            // 解析数据标识符
            QByteArray dataId = dataField.left(4);
            QVariantMap idInfo = parseDataIdentifier(dataId);
            result["dataIdentifier"] = idInfo;

            // 解析数据内容
            if (dataField.size() > 4) {
                QByteArray dataContent = dataField.mid(4);
                result["dataContent"] = formatHexString(dataContent);

                // 根据配置文件解析数值（使用统一解析逻辑）
                result = parseDataContentWithConfig(dataId, dataContent, rawDataField, result);
            }
        }
        result["type"] = "广播校时";
        break;

    case 0x16: // 冻结命令
        // 冻结命令使用统一的数据标识符解析方式
        if (dataField.size() >= 4) {
            // 解析数据标识符
            QByteArray dataId = dataField.left(4);
            QVariantMap idInfo = parseDataIdentifier(dataId);
            result["dataIdentifier"] = idInfo;

            // 解析数据内容
            if (dataField.size() > 4) {
                QByteArray dataContent = dataField.mid(4);
                result["dataContent"] = formatHexString(dataContent);

                // 根据配置文件解析数值（使用统一解析逻辑）
                result = parseDataContentWithConfig(dataId, dataContent, rawDataField, result);
            }
        } else {
            // 兼容旧格式：直接是4字节冻结时间
            result = parseFreezeCommandData(dataField);
        }
        result["type"] = "冻结命令";
        break;

    case 0x17: // 更改通信速率
        // 更改通信速率使用统一的数据标识符解析方式
        if (dataField.size() >= 4) {
            // 解析数据标识符
            QByteArray dataId = dataField.left(4);
            QVariantMap idInfo = parseDataIdentifier(dataId);
            result["dataIdentifier"] = idInfo;

            // 解析数据内容
            if (dataField.size() > 4) {
                QByteArray dataContent = dataField.mid(4);
                result["dataContent"] = formatHexString(dataContent);

                // 根据配置文件解析数值（使用统一解析逻辑）
                result = parseDataContentWithConfig(dataId, dataContent, rawDataField, result);
            }
        } else {
            // 兼容旧格式：直接是密码+操作者代码+波特率
            result = parseChangeRateRequest(dataField);
        }
        result["type"] = "更改通信速率";
        break;

    case 0x18: // 修改密码
    case 0x19: // 最大需量清零
    case 0x1A: // 电表清零
    case 0x1B: // 事件清零
        // 密码相关命令使用统一的数据标识符解析方式
        if (dataField.size() >= 4) {
            // 解析数据标识符
            QByteArray dataId = dataField.left(4);
            QVariantMap idInfo = parseDataIdentifier(dataId);
            result["dataIdentifier"] = idInfo;

            // 解析数据内容
            if (dataField.size() > 4) {
                QByteArray dataContent = dataField.mid(4);
                result["dataContent"] = formatHexString(dataContent);

                // 根据配置文件解析数值（使用统一解析逻辑）
                result = parseDataContentWithConfig(dataId, dataContent, rawDataField, result);
            }
        } else {
            // 兼容旧格式：直接是密码数据
            result = parsePasswordData(dataField);
        }
        result["type"] = getFunctionDescription(functionCode);
        break;

    default:
        result["type"] = "未知数据类型";
        result["rawData"] = formatHexString(dataField);
        break;
    }

    return result;
}

QVariantMap DLT645Parser::parseDataContentWithConfig(const QByteArray &dataId, const QByteArray &dataContent, const QByteArray &rawDataField, QVariantMap &result)
{
    // 根据配置文件解析数值（使用与读数据相同的逻辑）
    if (m_dataConfig) {
        // 构造数据标识
        quint32 identifier = static_cast<quint8>(dataId.at(0)) |
                            (static_cast<quint8>(dataId.at(1)) << 8) |
                            (static_cast<quint8>(dataId.at(2)) << 16) |
                            (static_cast<quint8>(dataId.at(3)) << 24);

        DataItemConfig config = m_dataConfig->getDataItemConfig(identifier, ProtocolType::DLT645_2007);

        if (config.isBlock) {
            // 数据块解析：按照包含的数据项逐个解析
            result["parsedValue"] = parseDataBlockContent(dataContent, config);
            result["unit"] = config.unit;
            result["format"] = config.format;
            result["encoding"] = config.encoding;
            result["isVariable"] = config.isVariable;
        } else if (config.isVariable) {
            // 可变长度数据项解析：按照单元长度切割并解析
            QByteArray rawDataContent;
            if (!rawDataField.isEmpty() && rawDataField.size() > 4) {
                rawDataContent = rawDataField.mid(4); // 跳过数据标识的4字节
            }
            result["parsedValue"] = parseVariableDataContent(dataContent, config, rawDataContent);
            result["unit"] = config.unit;
            result["format"] = config.format;
            result["encoding"] = config.encoding;
            result["isVariable"] = true;
        } else if (config.isComplex) {
            // 复合数据格式解析：按照字段定义逐个解析
            QByteArray rawDataContent;
            if (!rawDataField.isEmpty() && rawDataField.size() > 4) {
                rawDataContent = rawDataField.mid(4); // 跳过数据标识的4字节
            }
            result["parsedValue"] = parseComplexDataContent(dataContent, config, rawDataContent);
            result["unit"] = config.unit;
            result["format"] = config.format;
            result["encoding"] = config.encoding;
        } else if (!config.format.isEmpty()) {
            // 单独数据项解析
            QString value;

            // 特殊处理不同类型的数据标识
            QString specialValue;

            // 检查是否为时间相关数据 - 通过format判断
            if (isTimeFormat(config.format)) {
                specialValue = parseSpecialDateTimeData(dataContent, identifier, config.format);
            }
            // 检查是否为状态字、特征字和模式字数据
            else if (isFeatureWordData(identifier)) {
                specialValue = parseFeatureWordData(dataContent, identifier);
            }

            if (!specialValue.isEmpty()) {
                // 使用特殊格式解析
                value = specialValue;
            } else {
                // 使用通用格式解析
                value = parseDataByFormat(dataContent, config.format, config.encoding);
            }

            result["parsedValue"] = value;
            result["unit"] = config.unit;
            result["format"] = config.format;
            result["encoding"] = config.encoding;
            result["dataName"] = config.name;
        } else {
            // 使用默认解析
            QString value = parseBCDValue(dataContent, 2);
            result["parsedValue"] = value;
        }
    } else {
        // 没有配置文件时使用默认解析
        QString value = parseBCDValue(dataContent, 2);
        result["parsedValue"] = value;
    }

    return result;
}

QByteArray DLT645Parser::processDataField(const QByteArray &data)
{
    QByteArray result;

    // 对数据域进行减33H处理
    for (int i = 0; i < data.size(); ++i) {
        quint8 byte = static_cast<quint8>(data.at(i));
        byte -= 0x33;
        result.append(static_cast<char>(byte));
    }

    return result;
}

QString DLT645Parser::getFunctionDescription(quint8 functionCode)
{
    if (m_functionCodeMap.contains(functionCode)) {
        return m_functionCodeMap[functionCode];
    }
    return QString("未知功能码(0x%1)").arg(functionCode, 2, 16, QChar('0')).toUpper();
}

QString DLT645Parser::formatHexString(const QByteArray &data, const QString &separator)
{
    QStringList hexList;
    for (int i = 0; i < data.size(); ++i) {
        quint8 byte = static_cast<quint8>(data.at(i));
        hexList.append(QString("%1").arg(byte, 2, 16, QChar('0')).toUpper());
    }
    return hexList.join(separator);
}

ProtocolParseResult DLT645Parser::createErrorResult(const QString &errorMessage)
{
    ProtocolParseResult result;
    result.isValid = false;
    result.protocolType = ProtocolType::DLT645_2007;
    result.protocolName = "DL/T645-2007";
    result.errorMessage = errorMessage;
    return result;
}

ProtocolParseResult DLT645Parser::createSuccessResult(const QVariantMap &parsedData,
                                                     const QString &summary,
                                                     const QString &detailInfo)
{
    ProtocolParseResult result;
    result.isValid = true;
    result.protocolType = ProtocolType::DLT645_2007;
    result.protocolName = "DL/T645-2007";
    result.parsedData = parsedData;
    result.summary = summary;
    result.detailInfo = detailInfo;
    return result;
}

void DLT645Parser::initializeMappings()
{
    // 初始化功能码映射表
    m_functionCodeMap[0x08] = "广播校时";
    m_functionCodeMap[0x11] = "读数据";
    m_functionCodeMap[0x12] = "读后续数据";
    m_functionCodeMap[0x13] = "读通信地址";
    m_functionCodeMap[0x14] = "写数据";
    m_functionCodeMap[0x15] = "写通信地址";
    m_functionCodeMap[0x16] = "冻结命令";
    m_functionCodeMap[0x17] = "更改通信速率";
    m_functionCodeMap[0x18] = "修改密码";
    m_functionCodeMap[0x19] = "最大需量清零";
    m_functionCodeMap[0x1A] = "电表清零";
    m_functionCodeMap[0x1B] = "事件清零";

    // 初始化错误码映射表
    m_errorCodeMap[0x01] = "其他错误";
    m_errorCodeMap[0x02] = "无请求数据";
    m_errorCodeMap[0x04] = "密码错误/未授权";
    m_errorCodeMap[0x08] = "通信速率不能更改";
    m_errorCodeMap[0x10] = "年时区数据超";
    m_errorCodeMap[0x20] = "日时段数据超";
    m_errorCodeMap[0x40] = "费率数超";

    // TODO: 初始化数据标识映射表（数量较多，后续补充）
}

QVariantMap DLT645Parser::parseDataIdentifier(const QByteArray &dataId)
{
    QVariantMap result;

    if (dataId.size() != 4) {
        result["error"] = "数据标识长度错误";
        return result;
    }

    // 数据标识是低字节在前
    quint32 id = static_cast<quint8>(dataId.at(0)) |
                (static_cast<quint8>(dataId.at(1)) << 8) |
                (static_cast<quint8>(dataId.at(2)) << 16) |
                (static_cast<quint8>(dataId.at(3)) << 24);

    result["value"] = QString("0x%1").arg(id, 8, 16, QChar('0')).toUpper();
    result["bytes"] = formatHexString(dataId);
    result["description"] = getDataIdentifierDescription(id);

    // 判断是否为数据块 - 优先使用配置文件中的信息
    bool isDataBlock = false;
    if (m_dataConfig) {
        isDataBlock = m_dataConfig->isDataBlock(id, ProtocolType::DLT645_2007);
    }
    result["isDataBlock"] = isDataBlock;

    return result;
}


QString DLT645Parser::parseBCDValue(const QByteArray &bcdData, int decimalPlaces)
{
    if (bcdData.isEmpty()) {
        return "0";
    }

    QString result;

    // BCD码转换，低字节在前
    for (int i = bcdData.size() - 1; i >= 0; --i) {
        quint8 byte = static_cast<quint8>(bcdData.at(i));
        quint8 high = (byte >> 4) & 0x0F;
        quint8 low = byte & 0x0F;

        if (high > 9 || low > 9) {
            return "BCD格式错误";
        }

        result += QString::number(high) + QString::number(low);
    }

    // 插入小数点
    if (decimalPlaces > 0 && result.length() > decimalPlaces) {
        int pointPos = result.length() - decimalPlaces;
        result.insert(pointPos, '.');
    }

    return result;
}

QString DLT645Parser::parseDataByFormat(const QByteArray &data, const QString &format, const QString &encoding)
{
    if (data.isEmpty() || format.isEmpty()) {
        return "0";
    }

    // 根据编码格式选择解析方法
    if (encoding.toUpper() == "ASCII") {
        // ASCII编码直接转换为字符串
        return QString::fromLatin1(data);
    }

    // 特殊格式：BLOCK格式不处理
    if (format == "BLOCK") {
        return "数据块";
    }

    // 统一的数值格式解析
    bool ok;
    double formatValue = format.toDouble(&ok);

    if (!ok || formatValue <= 0) {
        return "格式错误";
    }

    // 直接解析BCD值为整数（不考虑小数点）
    QString result;
    for (int i = data.size() - 1; i >= 0; --i) {
        quint8 byte = static_cast<quint8>(data.at(i));
        quint8 high = (byte >> 4) & 0x0F;
        quint8 low = byte & 0x0F;

        if (high > 9 || low > 9) {
            return "BCD格式错误";
        }

        result += QString::number(high) + QString::number(low);
    }

    // 转换为数值并乘以format值
    bool convertOk;
    double numValue = result.toDouble(&convertOk);
    if (convertOk) {
        double finalValue = numValue * formatValue;
        return QString::number(finalValue);
    }

    return result;
}

QVariantMap DLT645Parser::parseStatusWord(const QByteArray &statusData, int statusType)
{
    QVariantMap result;

    if (statusData.isEmpty()) {
        result["error"] = "状态字数据为空";
        return result;
    }

    result["rawData"] = formatHexString(statusData);
    result["type"] = QString("状态字%1").arg(statusType);

    // 根据状态字类型解析具体含义
    // 这里简化处理，实际应该根据协议文档详细解析每一位
    QStringList statusList;

    for (int i = 0; i < statusData.size(); ++i) {
        quint8 byte = static_cast<quint8>(statusData.at(i));
        for (int bit = 0; bit < 8; ++bit) {
            if (byte & (1 << bit)) {
                statusList.append(QString("字节%1位%2").arg(i).arg(bit));
            }
        }
    }

    result["activeBits"] = statusList;
    return result;
}

QVariantMap DLT645Parser::parseErrorCode(quint8 errorCode)
{
    QVariantMap result;

    result["value"] = QString("0x%1").arg(errorCode, 2, 16, QChar('0')).toUpper();

    QStringList errors;

    if (errorCode & 0x01) errors.append("其他错误");
    if (errorCode & 0x02) errors.append("无请求数据");
    if (errorCode & 0x04) errors.append("密码错误/未授权");
    if (errorCode & 0x08) errors.append("通信速率不能更改");
    if (errorCode & 0x10) errors.append("年时区数据超");
    if (errorCode & 0x20) errors.append("日时段数据超");
    if (errorCode & 0x40) errors.append("费率数超");

    result["errors"] = errors;
    result["description"] = errors.isEmpty() ? "无错误" : errors.join(", ");

    return result;
}

QString DLT645Parser::getDataIdentifierDescription(quint32 dataId)
{
    // 优先使用配置文件中的描述
    if (m_dataConfig) {
        QString description = m_dataConfig->getDataItemDescription(dataId, ProtocolType::DLT645_2007);
        if (!description.isEmpty() && !description.startsWith("数据标识0x")) {
            return description;
        }
    }

    // 如果没有找到描述，返回默认描述
    return QString("数据标识0x%1").arg(dataId, 8, 16, QChar('0')).toUpper();
}



QVariantMap DLT645Parser::parseReadDataResponse(const QByteArray &dataField, const QByteArray &rawDataField)
{
    QVariantMap result;

    if (dataField.size() < 4) {
        result["error"] = "读数据应答长度不足";
        return result;
    }

    // 解析数据标识
    QByteArray dataId = dataField.left(4);
    QVariantMap idInfo = parseDataIdentifier(dataId);
    result["dataIdentifier"] = idInfo;

    // 解析数据内容
    if (dataField.size() > 4) {
        QByteArray dataContent = dataField.mid(4);
        result["dataContent"] = formatHexString(dataContent);

        // 根据配置文件解析数值
        if (m_dataConfig) {
            // 构造数据标识
            quint32 identifier = static_cast<quint8>(dataId.at(0)) |
                                (static_cast<quint8>(dataId.at(1)) << 8) |
                                (static_cast<quint8>(dataId.at(2)) << 16) |
                                (static_cast<quint8>(dataId.at(3)) << 24);

            DataItemConfig config = m_dataConfig->getDataItemConfig(identifier, ProtocolType::DLT645_2007);

            if (config.isBlock) {
                // 数据块解析：按照包含的数据项逐个解析
                result["parsedValue"] = parseDataBlockContent(dataContent, config);
                result["unit"] = config.unit;
                result["format"] = config.format;
                result["encoding"] = config.encoding;
                result["isVariable"] = config.isVariable;
                // 将blockItems转换为QVariantList以避免元类型注册问题
                QVariantList blockItemsList;
                for (const BlockItemInfo &item : config.blockItems) {
                    QVariantMap itemMap;
                    itemMap["order"] = item.order;
                    itemMap["dataId"] = QString("0x%1").arg(item.dataId, 8, 16, QChar('0')).toUpper();
                    blockItemsList.append(itemMap);
                }
                result["blockItems"] = blockItemsList;
            } else if (config.isVariable) {
                // 可变长度数据项解析：按照单元长度切割并解析
                // 获取对应的原始数据内容
                QByteArray rawDataContent;
                if (!rawDataField.isEmpty() && rawDataField.size() > 4) {
                    rawDataContent = rawDataField.mid(4); // 跳过数据标识的4字节
                }
                result["parsedValue"] = parseVariableDataContent(dataContent, config, rawDataContent);
                result["unit"] = config.unit;
                result["format"] = config.format;
                result["encoding"] = config.encoding;
                result["isVariable"] = true;
            } else if (config.isComplex) {
                // 复合数据格式解析：按照字段定义逐个解析
                // 获取对应的原始数据内容
                QByteArray rawDataContent;
                if (!rawDataField.isEmpty() && rawDataField.size() > 4) {
                    rawDataContent = rawDataField.mid(4); // 跳过数据标识的4字节
                    qDebug() << "复合数据解析 - 原始数据域:" << formatHexString(rawDataField);
                    qDebug() << "复合数据解析 - 原始数据内容:" << formatHexString(rawDataContent);
                    qDebug() << "复合数据解析 - 处理后数据内容:" << formatHexString(dataContent);
                } else {
                    qDebug() << "复合数据解析 - 没有原始数据域";
                }
                result["parsedValue"] = parseComplexDataContent(dataContent, config, rawDataContent);
                result["unit"] = config.unit;
                result["format"] = config.format;
                result["encoding"] = config.encoding;
                // 将fields转换为QVariantList
                QVariantList fieldsList;
                for (const FieldInfo &field : config.fields) {
                    QVariantMap fieldMap;
                    fieldMap["name"] = field.name;
                    fieldMap["format"] = field.format;
                    fieldMap["length"] = field.length;
                    fieldMap["unit"] = field.unit;
                    fieldMap["description"] = field.description;
                    fieldsList.append(fieldMap);
                }
                result["fields"] = fieldsList;
            } else if (!config.format.isEmpty()) {
                // 单独数据项解析
                QString value;

                // 特殊处理不同类型的数据标识
                QString specialValue;

                // 检查是否为时间相关数据 - 通过format判断
                if (isTimeFormat(config.format)) {
                    specialValue = parseSpecialDateTimeData(dataContent, identifier, config.format);
                }
                // 检查是否为状态字、特征字和模式字数据
                else if (isFeatureWordData(identifier)) {
                    specialValue = parseFeatureWordData(dataContent, identifier);
                }

                if (!specialValue.isEmpty()) {
                    // 使用特殊格式解析
                    value = specialValue;
                } else {
                    // 使用通用格式解析
                    value = parseDataByFormat(dataContent, config.format, config.encoding);
                }

                result["parsedValue"] = value;
                result["unit"] = config.unit;
                result["format"] = config.format;
                result["encoding"] = config.encoding;
            } else {
                // 使用默认解析
                QString value = parseBCDValue(dataContent, 2);
                result["parsedValue"] = value;
            }
        } else {
            // 没有配置文件时使用默认解析
            QString value = parseBCDValue(dataContent, 2);
            result["parsedValue"] = value;
        }
    }

    result["type"] = "读数据应答";
    return result;
}

QVariantMap DLT645Parser::parseWriteDataRequest(const QByteArray &dataField, const QByteArray &rawDataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    // 按照DLT645-2007协议：写数据请求数据域格式
    // 数据域 = 数据标识(4字节) + 密码(4字节) + 操作者代码(4字节) + 数据内容(L-12字节)
    // 其中L为数据域长度

    if (dataField.size() < 12) {
        result["error"] = QString("数据长度不足，写数据请求需要至少12字节，实际%1字节").arg(dataField.size());
        return result;
    }

    // 1. 解析数据标识（4字节，DI3 DI2 DI1 DI0）
    QByteArray dataId = dataField.left(4);
    QVariantMap idInfo = parseDataIdentifier(dataId);
    result["dataIdentifier"] = idInfo;

    // 2. 解析密码（4字节）- 显示原始值（加33H的值）
    QByteArray password = dataField.mid(4, 4);
    if (!rawDataField.isEmpty() && rawDataField.size() >= 8) {
        // 从原始数据域中提取密码的原始值
        QByteArray rawPassword = rawDataField.mid(4, 4);
        result["password"] = formatHexString(rawPassword);

        // 解析密码的减33H后的值，转换为数值显示（考虑小端序）
        QString parsedPassword = formatHexString(password);
        // DL/T645协议使用小端序，需要按字节逆序解析
        quint32 passwordNum = 0;
        if (password.size() == 4) {
            passwordNum = static_cast<quint8>(password.at(0)) |
                         (static_cast<quint8>(password.at(1)) << 8) |
                         (static_cast<quint8>(password.at(2)) << 16) |
                         (static_cast<quint8>(password.at(3)) << 24);
            result["passwordNote"] = QString("编程密码：%1").arg(passwordNum, 6, 10, QChar('0'));
        } else {
            result["passwordNote"] = QString("编程密码：%1").arg(parsedPassword);
        }
    } else {
        result["password"] = formatHexString(password);
        // 将十六进制字符串转换为数值显示（考虑小端序）
        quint32 passwordNum = 0;
        if (password.size() == 4) {
            passwordNum = static_cast<quint8>(password.at(0)) |
                         (static_cast<quint8>(password.at(1)) << 8) |
                         (static_cast<quint8>(password.at(2)) << 16) |
                         (static_cast<quint8>(password.at(3)) << 24);
            result["passwordNote"] = QString("编程密码：%1").arg(passwordNum, 6, 10, QChar('0'));
        } else {
            result["passwordNote"] = "编程密码：无效值";
        }
    }

    // 3. 解析操作者代码（4字节）- 显示原始值（加33H的值）
    QByteArray operatorCode = dataField.mid(8, 4);
    if (!rawDataField.isEmpty() && rawDataField.size() >= 12) {
        // 从原始数据域中提取操作者代码的原始值
        QByteArray rawOperatorCode = rawDataField.mid(8, 4);
        result["operatorCode"] = formatHexString(rawOperatorCode);

        // 解析操作者代码的减33H后的值，转换为数值显示（考虑小端序）
        QString parsedOperatorCode = formatHexString(operatorCode);
        // DL/T645协议使用小端序，需要按字节逆序解析
        quint32 operatorNum = 0;
        if (operatorCode.size() == 4) {
            operatorNum = static_cast<quint8>(operatorCode.at(0)) |
                         (static_cast<quint8>(operatorCode.at(1)) << 8) |
                         (static_cast<quint8>(operatorCode.at(2)) << 16) |
                         (static_cast<quint8>(operatorCode.at(3)) << 24);
            result["operatorCodeNote"] = QString("操作者身份标识：%1").arg(operatorNum, 6, 10, QChar('0'));
        } else {
            result["operatorCodeNote"] = QString("操作者身份标识：%1").arg(parsedOperatorCode);
        }
    } else {
        result["operatorCode"] = formatHexString(operatorCode);
        // 将十六进制字符串转换为数值显示（考虑小端序）
        quint32 operatorNum = 0;
        if (operatorCode.size() == 4) {
            operatorNum = static_cast<quint8>(operatorCode.at(0)) |
                         (static_cast<quint8>(operatorCode.at(1)) << 8) |
                         (static_cast<quint8>(operatorCode.at(2)) << 16) |
                         (static_cast<quint8>(operatorCode.at(3)) << 24);
            result["operatorCodeNote"] = QString("操作者身份标识：%1").arg(operatorNum, 6, 10, QChar('0'));
        } else {
            result["operatorCodeNote"] = "操作者身份标识：无效值";
        }
    }

    // 4. 解析数据内容（剩余字节）
    if (dataField.size() > 12) {
        QByteArray dataContent = dataField.mid(12);
        result["dataContent"] = formatHexString(dataContent);
        result["dataContentLength"] = dataContent.size();

        // 尝试根据数据标识配置解析数据内容
        if (m_dataConfig) {
            quint32 dataIdValue = (static_cast<quint32>(dataId.at(3)) << 24) |
                                  (static_cast<quint32>(dataId.at(2)) << 16) |
                                  (static_cast<quint32>(dataId.at(1)) << 8) |
                                  static_cast<quint32>(dataId.at(0));

            DataItemConfig config = m_dataConfig->getDataItemConfig(dataIdValue, ProtocolType::DLT645_2007);
            if (!config.id.isEmpty()) {
                QString parsedValue;

                // 检查是否为时间相关数据 - 通过format判断
                if (isTimeFormat(config.format)) {
                    parsedValue = parseSpecialDateTimeData(dataContent, dataIdValue, config.format);
                } else {
                    parsedValue = parseDataByFormat(dataContent, config.format, config.encoding);
                }

                result["parsedDataContent"] = parsedValue;
                result["unit"] = config.unit;
                result["dataName"] = config.name;
            }
        }
    } else {
        result["dataContentLength"] = 0;
        result["note"] = "无数据内容的写数据请求";
    }

    result["frameType"] = "写数据请求";
    result["securityLevel"] = "需要密码和操作者代码验证";

    return result;
}


QVariantMap DLT645Parser::parseFreezeCommandData(const QByteArray &dataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    // 按照DLT645-2007协议：冻结命令数据域格式
    // 数据域 = 冻结时间标签(4字节) 格式：MMDDhhmm（月日时分）
    // 特殊值：99999999H表示瞬时冻结

    if (dataField.size() != 4) {
        result["error"] = QString("数据长度错误，冻结命令需要4字节，实际%1字节").arg(dataField.size());
        return result;
    }

    // 解析冻结时间（BCD码）
    quint8 month = static_cast<quint8>(dataField.at(0));
    quint8 day = static_cast<quint8>(dataField.at(1));
    quint8 hour = static_cast<quint8>(dataField.at(2));
    quint8 minute = static_cast<quint8>(dataField.at(3));

    // 检查是否为瞬时冻结特殊标识
    if (month == 0x99 && day == 0x99 && hour == 0x99 && minute == 0x99) {
        result["freezeType"] = "瞬时冻结";
        result["freezeTime"] = "立即执行";
        result["frameType"] = "瞬时冻结命令";
        result["note"] = "立即冻结当前数据";
    } else {
        // BCD码转换为十进制
        int monthDec = ((month >> 4) & 0x0F) * 10 + (month & 0x0F);
        int dayDec = ((day >> 4) & 0x0F) * 10 + (day & 0x0F);
        int hourDec = ((hour >> 4) & 0x0F) * 10 + (hour & 0x0F);
        int minuteDec = ((minute >> 4) & 0x0F) * 10 + (minute & 0x0F);

        // 验证时间数据有效性
        if (monthDec < 1 || monthDec > 12 || dayDec < 1 || dayDec > 31 ||
            hourDec > 23 || minuteDec > 59) {
            result["error"] = "冻结时间数据超出有效范围";
            return result;
        }

        QString freezeTimeStr = QString("%1月%2日 %3:%4")
                               .arg(monthDec, 2, 10, QChar('0'))
                               .arg(dayDec, 2, 10, QChar('0'))
                               .arg(hourDec, 2, 10, QChar('0'))
                               .arg(minuteDec, 2, 10, QChar('0'));

        result["freezeType"] = "定时冻结";
        result["freezeTime"] = freezeTimeStr;
        result["frameType"] = "定时冻结命令";
        result["note"] = QString("在%1执行数据冻结").arg(freezeTimeStr);

        // 分别显示各个时间字段
        result["timeFields"] = QVariantMap{
            {"month", monthDec},
            {"day", dayDec},
            {"hour", hourDec},
            {"minute", minuteDec}
        };
    }

    return result;
}

QVariantMap DLT645Parser::parsePasswordData(const QByteArray &dataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    // 密码相关命令通常包含：密码权限+密码+操作者代码等
    // 这里简化处理，不解析具体内容以保护安全信息
    result["note"] = "包含密码信息，不显示具体内容";

    return result;
}

QVariantMap DLT645Parser::parseDataBlockContent(const QByteArray &dataContent, const DataItemConfig &blockConfig)
{
    QVariantMap result;
    QVariantList itemResults;

    if (!blockConfig.isBlock || blockConfig.blockItems.isEmpty()) {
        result["error"] = "不是有效的数据块配置";
        return result;
    }

    int currentOffset = 0;

    // 按顺序解析数据块中的每个数据项
    for (const BlockItemInfo &itemInfo : blockConfig.blockItems) {
        QVariantMap itemResult;

        // 获取对应数据项的配置
        DataItemConfig itemConfig = m_dataConfig->getDataItemConfig(itemInfo.dataId, ProtocolType::DLT645_2007);

        if (itemConfig.id.isEmpty()) {
            // 如果找不到配置，跳过这个项目
            qWarning() << "找不到数据项配置，ID:" << QString("0x%1").arg(itemInfo.dataId, 8, 16, QChar('0')).toUpper();
            continue;
        }

        // 检查数据长度是否足够
        if (currentOffset + itemConfig.length > dataContent.size()) {
            qWarning() << "数据块长度不足，期望" << (currentOffset + itemConfig.length)
                       << "字节，实际" << dataContent.size() << "字节";
            break;
        }

        // 提取当前数据项的数据
        QByteArray itemData = dataContent.mid(currentOffset, itemConfig.length);

        // 构建数据项结果的基本信息
        itemResult["order"] = itemInfo.order;
        itemResult["dataId"] = QString("0x%1").arg(itemInfo.dataId, 8, 16, QChar('0')).toUpper();
        itemResult["name"] = itemConfig.name;
        itemResult["rawData"] = formatHexString(itemData);
        itemResult["format"] = itemConfig.format;
        itemResult["length"] = itemConfig.length;

        // 根据数据项类型进行不同的解析
        if (itemConfig.isVariable) {
            // 可变长度数据项：需要特殊处理，使用剩余的所有数据
            QByteArray remainingData = dataContent.mid(currentOffset);
            QVariantMap variableResult = parseVariableDataContent(remainingData, itemConfig);
            itemResult["parsedValue"] = variableResult;
            itemResult["unit"] = itemConfig.unit;
            itemResult["isVariable"] = true;

            qDebug() << "解析数据块中的可变长度项目:" << itemConfig.name
                     << "剩余数据:" << formatHexString(remainingData)
                     << "子项数量:" << variableResult.value("totalItems", 0).toInt();

            // 对于可变长度项，使用实际解析的长度
            int actualLength = variableResult.value("parsedBytes", 0).toInt();
            currentOffset += actualLength;

            itemResults.append(itemResult);
            break; // 可变长度项通常是数据块的最后一项
        } else if (itemConfig.isComplex) {
            // 复杂格式数据项：解析为多个字段
            QVariantMap complexResult = parseComplexDataContent(itemData, itemConfig);
            itemResult["parsedValue"] = complexResult;
            itemResult["unit"] = itemConfig.unit;
            itemResult["isComplex"] = true;

            // 将字段信息添加到结果中
            if (complexResult.contains("fields")) {
                itemResult["fields"] = complexResult["fields"];
            }

            qDebug() << "解析数据块中的复杂格式项目:" << itemConfig.name
                     << "原始数据:" << formatHexString(itemData)
                     << "字段数量:" << itemConfig.fields.size();
        } else {
            // 简单格式数据项：解析为单个值
            QString parsedValue = parseDataByFormat(itemData, itemConfig.format, itemConfig.encoding);
            itemResult["parsedValue"] = parsedValue;
            itemResult["unit"] = itemConfig.unit;
            itemResult["isComplex"] = false;

            qDebug() << "解析数据块中的简单格式项目:" << itemConfig.name
                     << "原始数据:" << formatHexString(itemData)
                     << "解析值:" << parsedValue << itemConfig.unit;

            itemResults.append(itemResult);

            // 移动到下一个数据项
            currentOffset += itemConfig.length;
        }
    }

    result["items"] = itemResults;
    result["totalItems"] = itemResults.size();
    result["parsedBytes"] = currentOffset;
    result["summary"] = QString("数据块包含%1个数据项，共解析%2字节")
                        .arg(itemResults.size())
                        .arg(currentOffset);

    return result;
}

QVariantMap DLT645Parser::parseComplexDataContent(const QByteArray &dataContent, const DataItemConfig &complexConfig, const QByteArray &rawDataContent)
{
    QVariantMap result;
    QVariantList fieldResults;

    if (!complexConfig.isComplex || complexConfig.fields.isEmpty()) {
        result["error"] = "不是有效的复合数据配置";
        return result;
    }

    qDebug() << "parseComplexDataContent - 处理后数据:" << formatHexString(dataContent);
    qDebug() << "parseComplexDataContent - 原始数据:" << formatHexString(rawDataContent);

    int currentOffset = 0;

    // 逐个解析字段
    for (const FieldInfo &fieldInfo : complexConfig.fields) {
        QVariantMap fieldResult;

        // 检查数据长度是否足够
        if (currentOffset + fieldInfo.length > dataContent.size()) {
            fieldResult["error"] = QString("数据长度不足，需要%1字节，实际剩余%2字节")
                                   .arg(fieldInfo.length)
                                   .arg(dataContent.size() - currentOffset);
            fieldResult["name"] = fieldInfo.name;
            fieldResults.append(fieldResult);
            break;
        }

        // 提取当前字段的数据（处理后的数据）
        QByteArray fieldData = dataContent.mid(currentOffset, fieldInfo.length);

        // 提取对应的原始数据（如果提供了rawDataContent）
        QByteArray rawFieldData;
        if (!rawDataContent.isEmpty() && currentOffset + fieldInfo.length <= rawDataContent.size()) {
            rawFieldData = rawDataContent.mid(currentOffset, fieldInfo.length);
        }

        // 解析当前字段
        QString parsedValue;
        if (isTimeFormat(fieldInfo.format)) {
            // 使用时间格式解析
            parsedValue = parseTimeByFormat(fieldData, fieldInfo.format);
        } else {
            // 使用通用格式解析
            parsedValue = parseDataByFormat(fieldData, fieldInfo.format, complexConfig.encoding);
        }

        // 构建字段结果
        fieldResult["name"] = fieldInfo.name;
        fieldResult["rawData"] = formatHexString(!rawFieldData.isEmpty() ? rawFieldData : fieldData);
        fieldResult["processedData"] = formatHexString(fieldData);
        fieldResult["parsedValue"] = parsedValue;
        fieldResult["unit"] = fieldInfo.unit;
        fieldResult["format"] = fieldInfo.format;
        fieldResult["length"] = fieldInfo.length;
        fieldResult["description"] = fieldInfo.description;

        fieldResults.append(fieldResult);

        // 移动到下一个字段
        currentOffset += fieldInfo.length;
    }

    result["totalFields"] = fieldResults.size();
    result["fields"] = fieldResults;
    result["totalLength"] = currentOffset;
    result["remainingBytes"] = dataContent.size() - currentOffset;
    result["summary"] = QString("复合数据包含%1个字段，共解析%2字节")
                        .arg(fieldResults.size())
                        .arg(currentOffset);

    return result;
}

QVariantMap DLT645Parser::parseVariableDataContent(const QByteArray &dataContent, const DataItemConfig &variableConfig, const QByteArray &rawDataContent)
{
    QVariantMap result;
    QVariantList itemResults;

    if (!variableConfig.isVariable) {
        result["error"] = "不是有效的可变长度数据配置";
        return result;
    }

    qDebug() << "parseVariableDataContent - 处理后数据:" << formatHexString(dataContent);
    qDebug() << "parseVariableDataContent - 原始数据:" << formatHexString(rawDataContent);

    // 确定单元长度
    int unitLength = variableConfig.length;
    if (variableConfig.isComplex && variableConfig.fields.size() > 0) {
        // 对于复杂格式，计算所有字段的总长度
        unitLength = 0;
        for (const FieldInfo &field : variableConfig.fields) {
            unitLength += field.length;
        }
    }

    if (unitLength <= 0) {
        result["error"] = "无效的单元长度";
        return result;
    }

    // 计算数据项数量
    int totalLength = dataContent.size();
    int itemCount = totalLength / unitLength;
    int remainingBytes = totalLength % unitLength;

    qDebug() << "解析可变长度数据:" << variableConfig.name
             << "总长度:" << totalLength
             << "单元长度:" << unitLength
             << "项目数量:" << itemCount
             << "剩余字节:" << remainingBytes;

    // 按单元长度切割并解析每个数据项
    for (int i = 0; i < itemCount; i++) {
        QVariantMap itemResult;
        int offset = i * unitLength;
        QByteArray itemData = dataContent.mid(offset, unitLength);

        // 获取对应的原始数据（未减33H的）
        QByteArray rawItemData;
        if (!rawDataContent.isEmpty() && offset + unitLength <= rawDataContent.size()) {
            rawItemData = rawDataContent.mid(offset, unitLength);
        }

        // 构建数据项结果的基本信息
        itemResult["index"] = i;
        itemResult["name"] = QString::number(i);
        // 使用原始数据（未减33H的）显示在数据列中
        itemResult["rawData"] = formatHexString(!rawItemData.isEmpty() ? rawItemData : itemData);
        itemResult["format"] = variableConfig.format;
        itemResult["length"] = unitLength;

        // 根据数据项类型进行解析
        if (variableConfig.isComplex) {
            // 复杂格式：解析为多个字段
            QVariantMap complexResult = parseComplexDataContent(itemData, variableConfig, rawItemData);
            itemResult["parsedValue"] = complexResult;
            itemResult["unit"] = variableConfig.unit;
            itemResult["isComplex"] = true;

            // 将字段信息添加到结果中
            if (complexResult.contains("fields")) {
                itemResult["fields"] = complexResult["fields"];
            }

            qDebug() << "解析可变长度复杂格式项目" << i << ":"
                     << "原始数据:" << formatHexString(itemData)
                     << "字段数量:" << variableConfig.fields.size();
        } else {
            // 简单格式：解析为单个值
            QString parsedValue = parseDataByFormat(itemData, variableConfig.format, variableConfig.encoding);
            itemResult["parsedValue"] = parsedValue;
            itemResult["unit"] = variableConfig.unit;
            itemResult["isComplex"] = false;

            qDebug() << "解析可变长度简单格式项目" << i << ":"
                     << "原始数据:" << formatHexString(itemData)
                     << "解析值:" << parsedValue << variableConfig.unit;
        }

        itemResults.append(itemResult);
    }

    result["items"] = itemResults;
    result["totalItems"] = itemCount;
    result["parsedBytes"] = itemCount * unitLength;
    result["remainingBytes"] = remainingBytes;
    result["unitLength"] = unitLength;
    result["summary"] = QString("可变长度数据包含%1个数据项，每项%2字节，共解析%3字节")
                        .arg(itemCount)
                        .arg(unitLength)
                        .arg(itemCount * unitLength);

    if (remainingBytes > 0) {
        result["warning"] = QString("数据长度不是单元长度的整数倍，剩余%1字节未解析").arg(remainingBytes);
    }

    return result;
}

QVariantMap DLT645Parser::parseReadContinuousDataRequest(const QByteArray &dataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    if (dataField.size() >= 4) {
        // 读后续数据请求包含：数据标识（4字节）
        QByteArray dataId = dataField.left(4);
        QVariantMap idInfo = parseDataIdentifier(dataId);
        result["dataIdentifier"] = idInfo;
        result["note"] = "请求读取后续数据";
    } else {
        result["error"] = "数据长度不足，需要4字节数据标识";
    }

    return result;
}

QVariantMap DLT645Parser::parseReadAddressResponse(const QByteArray &dataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    if (dataField.size() >= 6) {
        // 读通信地址应答包含：通信地址（6字节BCD码）
        QByteArray addressData = dataField.left(6);
        QString address = parseAddress(addressData);
        result["communicationAddress"] = address;
        result["note"] = "电表通信地址";
    } else {
        result["error"] = "数据长度不足，需要6字节通信地址";
    }

    return result;
}

QVariantMap DLT645Parser::parseWriteAddressRequest(const QByteArray &dataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    if (dataField.size() >= 10) {
        // 写通信地址请求包含：密码（4字节）+ 操作者代码（4字节）+ 新地址（6字节）
        QByteArray password = dataField.mid(0, 4);
        QByteArray operatorCode = dataField.mid(4, 4);
        QByteArray newAddress = dataField.mid(8, 6);

        result["password"] = formatHexString(password);
        result["operatorCode"] = formatHexString(operatorCode);
        result["newAddress"] = parseAddress(newAddress);
        result["note"] = "写入新的通信地址";
    } else {
        result["error"] = "数据长度不足，需要14字节（密码4+操作者代码4+新地址6）";
    }

    return result;
}

QVariantMap DLT645Parser::parseChangeRateRequest(const QByteArray &dataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    if (dataField.size() >= 9) {
        // 更改通信速率请求包含：密码（4字节）+ 操作者代码（4字节）+ 波特率（1字节）
        QByteArray password = dataField.mid(0, 4);
        QByteArray operatorCode = dataField.mid(4, 4);
        quint8 rateCode = static_cast<quint8>(dataField.at(8));

        result["password"] = formatHexString(password);
        result["operatorCode"] = formatHexString(operatorCode);
        result["rateCode"] = QString("0x%1").arg(rateCode, 2, 16, QChar('0')).toUpper();

        // 解析波特率代码
        QString baudRate;
        switch (rateCode) {
        case 0x02: baudRate = "600 bps"; break;
        case 0x04: baudRate = "1200 bps"; break;
        case 0x08: baudRate = "2400 bps"; break;
        case 0x10: baudRate = "4800 bps"; break;
        case 0x20: baudRate = "9600 bps"; break;
        case 0x40: baudRate = "19200 bps"; break;
        default: baudRate = "未知波特率"; break;
        }
        result["baudRate"] = baudRate;
        result["note"] = "更改通信速率";
    } else {
        result["error"] = "数据长度不足，需要9字节（密码4+操作者代码4+波特率1）";
    }

    return result;
}

QVariantMap DLT645Parser::parseReadDataRequest(const QByteArray &dataField)
{
    QVariantMap result;
    result["rawData"] = formatHexString(dataField);

    // 按照DLT645-2007协议：0x11功能码读数据请求有三种帧格式
    // 根据数据域长度判断具体格式：
    // 帧格式1 (m=0): L=04H, 数据标识(4字节)
    // 帧格式2 (m=1): L=05H, 数据标识(4字节) + 负荷记录块数N(1字节)
    // 帧格式3 (m=6): L=0AH, 数据标识(4字节) + 块数N(1字节) + 时间格式(6字节)

    int dataLength = dataField.size();

    if (dataLength < 4) {
        result["error"] = "数据长度不足，读数据请求需要至少4字节数据标识";
        return result;
    }

    // 1. 解析数据标识（4字节，DI3 DI2 DI1 DI0）
    QByteArray dataId = dataField.left(4);
    QVariantMap idInfo = parseDataIdentifier(dataId);
    result["dataIdentifier"] = idInfo;

    // 2. 根据数据域长度判断帧格式
    if (dataLength == 4) {
        // 帧格式1: 标准读数据请求
        result["frameFormat"] = 1;
        result["frameType"] = "帧格式1 - 标准读数据请求";
        result["description"] = "读取指定数据标识的数据";

    } else if (dataLength == 5) {
        // 帧格式2: 读给定数据的负荷记录
        quint8 blockCount = static_cast<quint8>(dataField.at(4));
        result["frameFormat"] = 2;
        result["frameType"] = "帧格式2 - 读负荷记录块数";
        result["blockCount"] = blockCount;
        result["blockCountHex"] = QString("0x%1").arg(blockCount, 2, 16, QChar('0')).toUpper();
        result["description"] = QString("读取%1个负荷记录块").arg(blockCount);

    } else if (dataLength == 10) {
        // 帧格式3: 读给定时间、块数的负荷记录
        // 标准协议格式：数据标识(4) + 块数N(1) + 时间(5) + CS校验码和结束符在数据域外
        quint8 blockCount = static_cast<quint8>(dataField.at(4));
        QByteArray timeData = dataField.mid(5, 5);  // 5字节时间数据：mm hh DD MM YY

        result["frameFormat"] = 3;
        result["frameType"] = "帧格式3 - 读指定时间的负荷记录";
        result["blockCount"] = blockCount;
        result["blockCountHex"] = QString("0x%1").arg(blockCount, 2, 16, QChar('0')).toUpper();
        result["timeData"] = formatHexString(timeData);

        // 解析5字节时间格式 (mm hh DD MM YY)
        QString timeStr = parseTimeByFormat(timeData, "mmhhDDMMYY");

        if (!timeStr.isEmpty() && !timeStr.contains("数据长度不匹配")) {
            result["specifiedTime"] = timeStr;

            // 提取各个时间字段用于兼容性
            QMap<QString, int> timeFields;
            if (timeData.size() >= 5) {
                timeFields["minute"] = bcdToInt(static_cast<quint8>(timeData.at(0)));
                timeFields["hour"] = bcdToInt(static_cast<quint8>(timeData.at(1)));
                timeFields["day"] = bcdToInt(static_cast<quint8>(timeData.at(2)));
                timeFields["month"] = bcdToInt(static_cast<quint8>(timeData.at(3)));
                timeFields["year"] = bcdToInt(static_cast<quint8>(timeData.at(4)));

                result["timeFields"] = QVariantMap{
                    {"minute", timeFields["minute"]},
                    {"hour", timeFields["hour"]},
                    {"day", timeFields["day"]},
                    {"month", timeFields["month"]},
                    {"year", timeFields["year"]}
                };
            }
        } else {
            result["timeError"] = timeStr.isEmpty() ? "时间解析失败" : timeStr;
        }

        result["description"] = QString("读取%1时刻的%2个负荷记录块")
                               .arg(result.value("specifiedTime", "指定时间").toString())
                               .arg(blockCount);

    } else {
        result["error"] = QString("不支持的数据域长度：%1字节，支持的长度：4、5、10字节").arg(dataLength);
    }

    return result;
}

bool DLT645Parser::isTimeFormat(const QString &format)
{
    if (format.isEmpty()) {
        return false;
    }

    // 检查format中是否包含时间相关的字符
    // YY/MM/DD/hh/mm/ss/WW 都是时间格式的标识
    QStringList timeComponents = {"YY", "MM", "DD", "hh", "mm", "ss", "WW"};

    for (const QString &component : timeComponents) {
        if (format.contains(component, Qt::CaseInsensitive)) {
            return true;
        }
    }

    return false;
}

QString DLT645Parser::parseSpecialDateTimeData(const QByteArray &data, quint32 dataId, const QString &format)
{
    // 直接使用format进行时间解析
    return parseTimeFormatData(data, dataId, format);
}

QString DLT645Parser::parseTimeFormatData(const QByteArray &data, quint32 dataId, const QString &format)
{
    // 根据format字段动态解析时间格式
    if (format.isEmpty()) {
        return ""; // 无格式信息
    }

    return parseTimeByFormat(data, format);
}

QString DLT645Parser::parseTimeByFormat(const QByteArray &data, const QString &format)
{
    if (data.isEmpty() || format.isEmpty()) {
        return "";
    }

    // 解析format字符串，确定各个时间字段的位置
    QStringList components;
    QString currentComponent;

    for (int i = 0; i < format.length(); i++) {
        QChar c = format[i];
        if (currentComponent.isEmpty() || currentComponent.back() == c) {
            currentComponent += c;
        } else {
            if (!currentComponent.isEmpty()) {
                components.append(currentComponent);
            }
            currentComponent = c;
        }
    }
    if (!currentComponent.isEmpty()) {
        components.append(currentComponent);
    }

    // 检查数据长度是否匹配
    if (data.size() != components.size()) {
        return QString("数据长度不匹配：期望%1字节，实际%2字节").arg(components.size()).arg(data.size());
    }

    // 解析各个字段 - 数据存储和格式是反的，需要反序解析
    QMap<QString, int> timeFields;
    for (int i = 0; i < components.size() && i < data.size(); i++) {
        // 格式组件按正序，但数据按反序存储
        QString component = components[i];
        int dataIndex = data.size() - 1 - i;  // 反序索引
        int value = bcdToInt(static_cast<quint8>(data.at(dataIndex)));

        if (component.startsWith("YY", Qt::CaseInsensitive)) {
            timeFields["year"] = value;
        } else if (component.startsWith("MM", Qt::CaseInsensitive)) {
            timeFields["month"] = value;
        } else if (component.startsWith("DD", Qt::CaseInsensitive)) {
            timeFields["day"] = value;
        } else if (component.startsWith("hh", Qt::CaseInsensitive)) {
            timeFields["hour"] = value;
        } else if (component.startsWith("mm", Qt::CaseInsensitive)) {
            timeFields["minute"] = value;
        } else if (component.startsWith("ss", Qt::CaseInsensitive)) {
            timeFields["second"] = value;
        } else if (component.startsWith("WW", Qt::CaseInsensitive)) {
            timeFields["weekday"] = value;
        }
    }

    // 构建时间字符串
    QString result;
    bool hasDate = timeFields.contains("year") || timeFields.contains("month") || timeFields.contains("day");
    bool hasTime = timeFields.contains("hour") || timeFields.contains("minute") || timeFields.contains("second");

    if (hasDate) {
        int year = timeFields.value("year", 0);
        int month = timeFields.value("month", 0);
        int day = timeFields.value("day", 0);

        // 构造完整年份（假设20xx年）
        int fullYear = 2000 + year;

        result = QString("%1-%2-%3")
                 .arg(fullYear, 4, 10, QChar('0'))
                 .arg(month, 2, 10, QChar('0'))
                 .arg(day, 2, 10, QChar('0'));
    }

    if (hasTime) {
        int hour = timeFields.value("hour", 0);
        int minute = timeFields.value("minute", 0);
        int second = timeFields.value("second", 0);

        QString timeStr;
        // 根据原始format字符串来决定时间格式
        if (format.contains("ss", Qt::CaseInsensitive)) {
            // format包含秒，显示时分秒
            timeStr = QString("%1:%2:%3")
                      .arg(hour, 2, 10, QChar('0'))
                      .arg(minute, 2, 10, QChar('0'))
                      .arg(second, 2, 10, QChar('0'));
        } else {
            // format不包含秒，只显示时分
            timeStr = QString("%1:%2")
                      .arg(hour, 2, 10, QChar('0'))
                      .arg(minute, 2, 10, QChar('0'));
        }

        if (!result.isEmpty()) {
            result += " " + timeStr;
        } else {
            result = timeStr;
        }
    }

    // 添加星期信息
    if (timeFields.contains("weekday")) {
        int weekday = timeFields["weekday"];
        QStringList weekNames = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        QString weekName = (weekday >= 0 && weekday <= 6) ? weekNames[weekday] : QString("星期%1").arg(weekday);

        if (!result.isEmpty()) {
            result += " " + weekName;
        } else {
            result = weekName;
        }
    }

    return result;
}









int DLT645Parser::bcdToInt(quint8 bcd)
{
    // BCD码转换为十进制（不进行有效性检查，允许任何值）
    return ((bcd >> 4) & 0x0F) * 10 + (bcd & 0x0F);
}



bool DLT645Parser::isFeatureWordData(quint32 dataId)
{
    // 检查是否为状态字、特征字和模式字数据标识
    return (dataId >= 0x04000501 && dataId <= 0x04000507) ||  // 电表运行状态字1-7
           (dataId >= 0x04000601 && dataId <= 0x04000603) ||  // 有功/无功组合方式特征字
           (dataId >= 0x04000701 && dataId <= 0x04000705) ||  // 通信速率特征字
           (dataId == 0x04000801) ||                           // 周休日特征字
           (dataId >= 0x04000901 && dataId <= 0x04000902);    // 负荷记录/冻结数据模式字
}

QString DLT645Parser::parseFeatureWordData(const QByteArray &data, quint32 dataId)
{
    if (data.isEmpty()) {
        return "无特征字数据";
    }

    QStringList featureList;

    // 解析每个字节的特征位
    for (int byteIndex = 0; byteIndex < data.size(); byteIndex++) {
        quint8 featureByte = static_cast<quint8>(data.at(byteIndex));
        QStringList byteFeatureList = parseFeatureWordByte(featureByte, dataId, byteIndex);
        featureList.append(byteFeatureList);
    }

    if (featureList.isEmpty()) {
        return "无有效特征";
    }

    return featureList.join("; ");
}

QStringList DLT645Parser::parseFeatureWordByte(quint8 featureByte, quint32 dataId, int byteIndex)
{
    QStringList featureList;

    switch (dataId) {
    // 电表运行状态字1-7
    case 0x04000501: // 电表运行状态字1
        if (byteIndex == 0) {
            if (featureByte & 0x01) featureList << "需量积算方式:区间";
            else featureList << "需量积算方式:消差";

            if (featureByte & 0x02) featureList << "时钟电池:欠压";
            else featureList << "时钟电池:正常";

            if (featureByte & 0x04) featureList << "停电抄表电池:欠压";
            else featureList << "停电抄表电池:正常";

            if (featureByte & 0x08) featureList << "有功功率方向:反向";
            else featureList << "有功功率方向:正向";

            if (featureByte & 0x10) featureList << "无功功率方向:反向";
            else featureList << "无功功率方向:正向";
        }
        break;

    case 0x04000502: // 电表运行状态字2
        if (byteIndex == 0) {
            if (featureByte & 0x01) featureList << "A相有功功率:反向";
            else featureList << "A相有功功率:正向";

            if (featureByte & 0x02) featureList << "B相有功功率:反向";
            else featureList << "B相有功功率:正向";

            if (featureByte & 0x04) featureList << "C相有功功率:反向";
            else featureList << "C相有功功率:正向";

            if (featureByte & 0x10) featureList << "A相无功功率:反向";
            else featureList << "A相无功功率:正向";

            if (featureByte & 0x20) featureList << "B相无功功率:反向";
            else featureList << "B相无功功率:正向";

            if (featureByte & 0x40) featureList << "C相无功功率:反向";
            else featureList << "C相无功功率:正向";
        }
        break;

    case 0x04000503: // 电表运行状态字3(操作类)
        if (byteIndex == 0) {
            if (featureByte & 0x01) featureList << "当前运行时段:第二套";
            else featureList << "当前运行时段:第一套";

            int powerSupply = (featureByte >> 1) & 0x03;
            switch (powerSupply) {
            case 0: featureList << "供电方式:主电源"; break;
            case 1: featureList << "供电方式:辅助电源"; break;
            case 2: featureList << "供电方式:电池供电"; break;
            default: featureList << "供电方式:未知"; break;
            }

            if (featureByte & 0x08) featureList << "编程允许:允许";
            else featureList << "编程允许:禁止";

            if (featureByte & 0x10) featureList << "继电器状态:断";
            else featureList << "继电器状态:通";
        }
        break;

    case 0x04000504: // 电表运行状态字4(A相故障状态)
    case 0x04000505: // 电表运行状态字5(B相故障状态)
    case 0x04000506: // 电表运行状态字6(C相故障状态)
        {
            QString phaseName;
            if (dataId == 0x04000504) phaseName = "A相";
            else if (dataId == 0x04000505) phaseName = "B相";
            else phaseName = "C相";

            if (byteIndex == 0) {
                if (featureByte & 0x01) featureList << phaseName + ":失压";
                if (featureByte & 0x02) featureList << phaseName + ":欠压";
                if (featureByte & 0x04) featureList << phaseName + ":过压";
                if (featureByte & 0x08) featureList << phaseName + ":失流";
                if (featureByte & 0x10) featureList << phaseName + ":过流";
                if (featureByte & 0x20) featureList << phaseName + ":过载";
                if (featureByte & 0x40) featureList << phaseName + ":潮流反向";
                if (featureByte & 0x80) featureList << phaseName + ":断相";
            } else if (byteIndex == 1) {
                if (featureByte & 0x01) featureList << phaseName + ":断流";
            }
        }
        break;

    case 0x04000507: // 电表运行状态字7(合相故障状态)
        if (byteIndex == 0) {
            if (featureByte & 0x01) featureList << "电压逆相序";
            if (featureByte & 0x02) featureList << "电流逆相序";
            if (featureByte & 0x04) featureList << "电压不平衡";
            if (featureByte & 0x08) featureList << "电流不平衡";
            if (featureByte & 0x10) featureList << "辅助电源失电";
            if (featureByte & 0x20) featureList << "掉电";
            if (featureByte & 0x40) featureList << "需量超限";
        }
        break;
    case 0x04000601: // 有功组合方式特征字
        if (byteIndex == 0) {
            if (featureByte & 0x01) featureList << "正向有功:加";
            else featureList << "正向有功:不加";

            if (featureByte & 0x02) featureList << "正向有功:减";
            else featureList << "正向有功:不减";

            if (featureByte & 0x04) featureList << "反向有功:加";
            else featureList << "反向有功:不加";

            if (featureByte & 0x08) featureList << "反向有功:减";
            else featureList << "反向有功:不减";
        }
        break;

    case 0x04000602: // 无功组合方式1特征字
    case 0x04000603: // 无功组合方式2特征字
        {
            QString typeName = (dataId == 0x04000602) ? "无功1" : "无功2";
            if (byteIndex == 0) {
                if (featureByte & 0x01) featureList << typeName + " I象限:加";
                else featureList << typeName + " I象限:不加";

                if (featureByte & 0x02) featureList << typeName + " I象限:减";
                else featureList << typeName + " I象限:不减";

                if (featureByte & 0x04) featureList << typeName + " II象限:加";
                else featureList << typeName + " II象限:不加";

                if (featureByte & 0x08) featureList << typeName + " II象限:减";
                else featureList << typeName + " II象限:不减";

                if (featureByte & 0x10) featureList << typeName + " III象限:加";
                else featureList << typeName + " III象限:不加";

                if (featureByte & 0x20) featureList << typeName + " III象限:减";
                else featureList << typeName + " III象限:不减";

                if (featureByte & 0x40) featureList << typeName + " IV象限:加";
                else featureList << typeName + " IV象限:不加";

                if (featureByte & 0x80) featureList << typeName + " IV象限:减";
                else featureList << typeName + " IV象限:不减";
            }
        }
        break;

    case 0x04000701: // 通信速率特征字1
    case 0x04000702: // 通信速率特征字2
    case 0x04000703: // 通信速率特征字3
    case 0x04000704: // 通信速率特征字4
    case 0x04000705: // 通信速率特征字5
        {
            int portNum = (dataId & 0xFF) - 0x00; // 提取端口号
            QString portName = QString("端口%1").arg(portNum);

            if (byteIndex == 0) {
                // 通信速率编码解析
                int baudRateCode = featureByte & 0x0F; // 低4位
                QStringList baudRates = {"300", "600", "1200", "2400", "4800", "7200", "9600", "19200"};
                QString baudRate = (baudRateCode < baudRates.size()) ?
                                   baudRates[baudRateCode] : QString("未知(%1)").arg(baudRateCode);

                featureList << portName + " 通信速率:" + baudRate + "bps";

                // 校验方式
                int parityCode = (featureByte >> 4) & 0x03; // bit4-5
                QStringList parityTypes = {"无校验", "奇校验", "偶校验", "保留"};
                QString parity = (parityCode < parityTypes.size()) ?
                                 parityTypes[parityCode] : QString("未知(%1)").arg(parityCode);

                featureList << portName + " 校验方式:" + parity;

                // 停止位
                if (featureByte & 0x40) featureList << portName + " 停止位:2位";
                else featureList << portName + " 停止位:1位";

                // 数据位
                if (featureByte & 0x80) featureList << portName + " 数据位:8位";
                else featureList << portName + " 数据位:7位";
            }
        }
        break;

    case 0x04000801: // 周休日特征字
        if (byteIndex == 0) {
            // 每个bit代表一天：bit0=周一, bit1=周二, ..., bit6=周日
            QStringList weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
            QStringList restDays;
            QStringList workDays;

            for (int i = 0; i < 7; i++) {
                if (featureByte & (1 << i)) {
                    restDays << weekDays[i];
                } else {
                    workDays << weekDays[i];
                }
            }

            if (!restDays.isEmpty()) {
                featureList << "休息日:" + restDays.join(",");
            }
            if (!workDays.isEmpty()) {
                featureList << "工作日:" + workDays.join(",");
            }
        }
        break;

    case 0x04000901: // 负荷记录模式字
        if (byteIndex == 0) {
            // 负荷记录间隔
            int interval = featureByte & 0x0F; // 低4位
            QStringList intervals = {"1分", "5分", "10分", "15分", "30分", "60分"};
            QString intervalStr = (interval < intervals.size()) ?
                                  intervals[interval] : QString("未知(%1分)").arg(interval);

            featureList << "负荷记录间隔:" + intervalStr;

            // 记录模式
            if (featureByte & 0x10) featureList << "记录模式:循环记录";
            else featureList << "记录模式:停止记录";

            // 记录内容
            if (featureByte & 0x20) featureList << "记录内容:包含电压电流";
            else featureList << "记录内容:仅功率";
        }
        break;

    case 0x04000902: // 冻结数据模式字
        if (byteIndex == 0) {
            // 冻结类型
            int freezeType = featureByte & 0x07; // 低3位
            QStringList freezeTypes = {"不冻结", "日冻结", "月冻结", "年冻结", "时段冻结", "瞬时冻结", "约定冻结", "整点冻结"};
            QString freezeTypeStr = (freezeType < freezeTypes.size()) ?
                                    freezeTypes[freezeType] : QString("未知(%1)").arg(freezeType);

            featureList << "冻结类型:" + freezeTypeStr;

            // 冻结深度
            int depth = (featureByte >> 3) & 0x0F; // bit3-6
            featureList << QString("冻结深度:%1次").arg(depth);

            // 自动冻结
            if (featureByte & 0x80) featureList << "自动冻结:启用";
            else featureList << "自动冻结:禁用";
        }
        break;

    default:
        featureList << QString("未知特征字类型:0x%1").arg(dataId, 8, 16, QChar('0'));
        break;
    }

    return featureList;
}



