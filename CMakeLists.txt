cmake_minimum_required(VERSION 3.14)

project(NW_ManageTools LANGUAGES CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# QtCreator supports the following variables for Android, which are identical to qmake Android variables.
# Check http://doc.qt.io/qt-5/deployment-android.html for more information.
# They need to be set before the find_package(Qt5 ...) call.

#if(ANDROID)
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
#    if (ANDROID_ABI STREQUAL "armeabi-v7a")
#        set(ANDROID_EXTRA_LIBS
#            ${CMAKE_CURRENT_SOURCE_DIR}/path/to/libcrypto.so
#            ${CMAKE_CURRENT_SOURCE_DIR}/path/to/libssl.so)
#    endif()
#endif()

find_package(QT NAMES Qt6 Qt5 COMPONENTS Core Quick Widgets Sql Xml REQUIRED)
find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Core Quick Widgets Sql Xml REQUIRED)

add_subdirectory(LogAnalyzer)
add_subdirectory(ParamViewer)
add_subdirectory(DatabaseTool)
add_subdirectory(ProtocolParser)

if(ANDROID)
    add_library(NW_ManageTools SHARED
      main.cpp
      archivehandler.cpp
      archivehandler.h
      qml.qrc
    )
else()
    add_executable(NW_ManageTools WIN32
      main.cpp
      archivehandler.cpp
      archivehandler.h
      qml.qrc
      icon.rc
    )
endif()
set(SOURCE_DIR_CONFIG "${CMAKE_CURRENT_SOURCE_DIR}/config")
set(SOURCE_DIR_TOOLS "${CMAKE_CURRENT_SOURCE_DIR}/tools")
set(TARGET_DIR "${CMAKE_BINARY_DIR}")
if(DEFINED ENV{QT_CREATOR} OR DEFINED QT_QMAKE_EXECUTABLE)
    add_custom_target(CopyQtResources ALL
        COMMAND ${CMAKE_COMMAND} -E copy_directory "${SOURCE_DIR_CONFIG}" "${TARGET_DIR}/config"
        COMMAND ${CMAKE_COMMAND} -E copy_directory "${SOURCE_DIR_TOOLS}" "${TARGET_DIR}/tools"
        COMMENT "Copying resources to Qt build directory: ${TARGET_DIR}"
        VERBATIM
    )
endif()
target_compile_definitions(NW_ManageTools
  PRIVATE $<$<OR:$<CONFIG:Debug>,$<CONFIG:RelWithDebInfo>>:QT_QML_DEBUG>)
target_link_libraries(NW_ManageTools
  PRIVATE
  Qt${QT_VERSION_MAJOR}::Core
  Qt${QT_VERSION_MAJOR}::Quick
  Qt${QT_VERSION_MAJOR}::Widgets
  Qt${QT_VERSION_MAJOR}::Sql
  Qt${QT_VERSION_MAJOR}::Xml
  LogAnalyzer
  ParamViewer
  DatabaseTool
  ProtocolParser
)
