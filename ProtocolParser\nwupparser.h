#ifndef NWUPPARSER_H
#define NWUPPARSER_H

#include <QObject>
#include <QByteArray>
#include <QString>
#include <QVariantMap>
#include <QDateTime>
#include <QMap>
#include <memory>

#include "protocoltypes.h"
#include "dataidentifierconfig.h"

/**
 * @brief 南网上行协议解析器
 * 
 * 专门用于解析南网上行规约协议报文
 * 基于GB/T 18657.1的FT1.2异步式传输帧格式
 */
class NWUPParser : public QObject
{
    Q_OBJECT

public:
    explicit NWUPParser(QObject *parent = nullptr);
    ~NWUPParser();

    /**
     * @brief 设置数据标识配置
     * @param config 数据标识配置实例
     */
    void setDataConfig(DataIdentifierConfig* config);

    /**
     * @brief 解析南网上行协议报文
     * @param frameData 帧数据
     * @return 协议解析结果
     */
    ProtocolParseResult parseFrame(const QByteArray &frameData);

    /**
     * @brief 验证帧格式
     * @param frameData 帧数据
     * @return 帧验证结果
     */
    bool validateFrame(const QByteArray &frameData);

    /**
     * @brief 解析帧头
     * @param frameData 帧数据
     * @return 帧头信息
     */
    QVariantMap parseFrameHeader(const QByteArray &frameData);

    /**
     * @brief 解析长度域
     * @param lengthData 长度域数据（2字节）
     * @return 长度信息
     */
    QVariantMap parseLengthField(const QByteArray &lengthData);

    /**
     * @brief 解析控制域
     * @param controlData 控制域数据（1字节）
     * @return 控制域信息
     */
    QVariantMap parseControlField(quint8 controlData);

    /**
     * @brief 解析地址域
     * @param addressData 地址域数据（7字节）
     * @return 地址信息
     */
    QVariantMap parseAddressField(const QByteArray &addressData);

    /**
     * @brief 解析应用层数据
     * @param appData 应用层数据
     * @return 应用层解析结果
     */
    QVariantMap parseApplicationLayer(const QByteArray &appData);

    /**
     * @brief 解析应用层功能码
     * @param afn 应用层功能码
     * @return 功能码信息
     */
    QVariantMap parseApplicationFunctionCode(quint8 afn);

    /**
     * @brief 解析帧序列域
     * @param seqData 帧序列域数据（1字节）
     * @return 帧序列信息
     */
    QVariantMap parseSequenceField(quint8 seqData);

    /**
     * @brief 解析信息点标识
     * @param daData 信息点标识数据（2字节）
     * @return 信息点信息
     */
    QVariantMap parseDataAddress(const QByteArray &daData);

    /**
     * @brief 解析数据标识编码
     * @param diData 数据标识编码数据（4字节）
     * @return 数据标识信息
     */
    QVariantMap parseDataIdentifier(const QByteArray &diData);

    /**
     * @brief 解析数据时间域
     * @param timeData 数据时间域数据
     * @return 时间信息
     */
    QVariantMap parseDataTimeField(const QByteArray &timeData);

    /**
     * @brief 解析消息认证码
     * @param pwData 消息认证码数据（16字节）
     * @return 认证码信息
     */
    QVariantMap parseMessageAuthCode(const QByteArray &pwData);

    /**
     * @brief 解析时间标签
     * @param tpData 时间标签数据（5字节）
     * @return 时间标签信息
     */
    QVariantMap parseTimeTag(const QByteArray &tpData);

    /**
     * @brief 解析数据标识内容
     * @param dataContent 数据标识内容
     * @param dataId 数据标识编码
     * @param afn 应用层功能码
     * @return 数据内容解析结果
     */
    QVariantMap parseDataContent(const QByteArray &dataContent, quint32 dataId, quint8 afn);

    /**
     * @brief 解析错误码
     * @param errorCode 错误码
     * @return 错误信息
     */
    QVariantMap parseErrorCode(quint8 errorCode);

    /**
     * @brief 计算帧校验和
     * @param data 需要校验的数据
     * @return 校验和
     */
    quint8 calculateChecksum(const QByteArray &data);

    /**
     * @brief 验证帧校验和
     * @param frameData 帧数据
     * @return 校验结果
     */
    bool verifyChecksum(const QByteArray &frameData);

    /**
     * @brief 解析BCD编码的数值
     * @param bcdData BCD数据
     * @param decimalPlaces 小数位数
     * @return 数值字符串
     */
    QString parseBCDValue(const QByteArray &bcdData, int decimalPlaces = 2);

    /**
     * @brief 解析时间数据
     * @param timeData 时间数据（5字节BCD）
     * @return 时间字符串
     */
    QString parseTimeData(const QByteArray &timeData);

    /**
     * @brief 获取功能码描述
     * @param functionCode 功能码
     * @return 功能码描述
     */
    QString getFunctionDescription(quint8 functionCode);

    /**
     * @brief 获取应用层功能码描述
     * @param afn 应用层功能码
     * @return 功能码描述
     */
    QString getApplicationFunctionDescription(quint8 afn);

    /**
     * @brief 获取数据标识描述
     * @param dataId 数据标识编码
     * @return 数据标识描述
     */
    QString getDataIdentifierDescription(quint32 dataId);

    /**
     * @brief 格式化十六进制字符串
     * @param data 数据
     * @param separator 分隔符
     * @return 格式化的十六进制字符串
     */
    QString formatHexString(const QByteArray &data, const QString &separator = " ");

    /**
     * @brief 根据格式字符串解析数据
     * @param data 原始数据
     * @param format 格式字符串（如"0.01", "0.1"等）
     * @param encoding 编码格式（BCD/ASCII/BIN）
     * @return 解析后的数值字符串
     */
    QString parseDataByFormat(const QByteArray &data, const QString &format, const QString &encoding = "BCD");

    /**
     * @brief 根据时间格式解析时间数据
     * @param data 时间数据
     * @param format 时间格式字符串
     * @return 解析后的时间字符串
     */
    QString parseTimeDataByFormat(const QByteArray &data, const QString &format);

    /**
     * @brief 解析最大需量数据
     * @param data 需量数据
     * @param dataItem 数据项配置
     * @return 需量解析结果
     */
    QVariantMap parseDemandData(const QByteArray &data, const DataItemConfig &dataItem);

    /**
     * @brief 解析事件记录数据
     * @param data 事件数据
     * @param dataItem 数据项配置
     * @return 事件解析结果
     */
    QVariantMap parseEventData(const QByteArray &data, const DataItemConfig &dataItem);

    /**
     * @brief 解析文件传输数据
     * @param data 文件数据
     * @param dataItem 数据项配置
     * @return 文件解析结果
     */
    QVariantMap parseFileData(const QByteArray &data, const DataItemConfig &dataItem);

    /**
     * @brief 解析任务数据
     * @param data 任务数据
     * @param dataItem 数据项配置
     * @return 任务解析结果
     */
    QVariantMap parseTaskData(const QByteArray &data, const DataItemConfig &dataItem);

private:
    DataIdentifierConfig* m_dataConfig;
    
    // 功能码映射
    QMap<quint8, QString> m_functionCodeMap;
    QMap<quint8, QString> m_applicationFunctionMap;
    
    /**
     * @brief 初始化映射表
     */
    void initializeMappings();

    /**
     * @brief 创建错误结果
     * @param errorMessage 错误信息
     * @return 错误结果
     */
    ProtocolParseResult createErrorResult(const QString &errorMessage);

    /**
     * @brief 创建成功结果
     * @param parsedData 解析数据
     * @param summary 摘要
     * @param detailInfo 详细信息
     * @return 成功结果
     */
    ProtocolParseResult createSuccessResult(const QVariantMap &parsedData,
                                          const QString &summary,
                                          const QString &detailInfo);

    /**
     * @brief 解析特定功能码的报文
     * @param afn 应用层功能码
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseSpecificFunction(quint8 afn, const QByteArray &appData);

    /**
     * @brief 解析确认/否定报文 (AFN=00H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseConfirmDenyMessage(const QByteArray &appData);

    /**
     * @brief 解析链路接口检测报文 (AFN=02H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseLinkInterfaceTest(const QByteArray &appData);

    /**
     * @brief 解析写参数报文 (AFN=04H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseWriteParameter(const QByteArray &appData);

    /**
     * @brief 解析安全认证报文 (AFN=06H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseSecurityAuth(const QByteArray &appData);

    /**
     * @brief 解析读参数报文 (AFN=0AH)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseReadParameter(const QByteArray &appData);

    /**
     * @brief 解析读当前数据报文 (AFN=0CH)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseReadCurrentData(const QByteArray &appData);

    /**
     * @brief 解析读历史数据报文 (AFN=0DH)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseReadHistoryData(const QByteArray &appData);

    /**
     * @brief 解析读事件记录报文 (AFN=0EH)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseReadEventRecord(const QByteArray &appData);

    /**
     * @brief 解析文件传输报文 (AFN=0FH)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseFileTransfer(const QByteArray &appData);

    /**
     * @brief 解析中继转发报文 (AFN=10H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseRelayForward(const QByteArray &appData);

    /**
     * @brief 解析读任务数据报文 (AFN=12H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseReadTaskData(const QByteArray &appData);

    /**
     * @brief 解析读告警数据报文 (AFN=13H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseReadAlarmData(const QByteArray &appData);

    /**
     * @brief 解析用户自定义数据报文 (AFN=15H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseUserCustomData(const QByteArray &appData);

    /**
     * @brief 解析数据安全传输报文 (AFN=16H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseDataSecurityTransfer(const QByteArray &appData);

    /**
     * @brief 解析数据转加密报文 (AFN=17H)
     * @param appData 应用层数据
     * @return 解析结果
     */
    QVariantMap parseDataEncryption(const QByteArray &appData);

    // 辅助方法
    /**
     * @brief 根据数据标识获取数据长度
     * @param dataId 数据标识
     * @return 数据长度，0表示未知
     */
    int getDataLengthByDataId(quint32 dataId);

    /**
     * @brief 获取数据密度描述
     * @param density 数据密度值
     * @return 密度描述
     */
    QString getDataDensityDescription(quint8 density);
};

#endif // NWUPPARSER_H 