#ifndef PROTOCOLTYPES_H
#define PROTOCOLTYPES_H

#include <QString>
#include <QStringList>
#include <QByteArray>
#include <QVariantMap>
#include <QMetaType>
#include <QDebug>

/**
 * @brief 协议类型枚举
 */
enum class ProtocolType {
    Unknown = 0,        // 未知协议
    DLT645_2007,       // DL/T645-2007电能表通信协议
    DLT645_1997,       // DL/T645-1997电能表通信协议
    NW_UP,             // 南网上行协议
    NW_PLC,            // 南网PLC协议
    Custom             // 自定义协议
};

/**
 * @brief 帧解析结果结构
 */
struct FrameParseResult {
    bool isValid = false;           // 帧是否有效
    ProtocolType protocolType = ProtocolType::Unknown;  // 协议类型
    QString errorMessage;           // 错误信息
    QByteArray rawData;            // 原始数据
    QByteArray frameData;          // 帧数据（去除无效字节）
    int frameStart = -1;           // 帧起始位置
    int frameEnd = -1;             // 帧结束位置
    int frameLength = 0;           // 帧长度
};

/**
 * @brief 字节划分结果结构
 */
struct ByteSegmentResult {
    bool isValid = false;           // 是否成功划分
    QByteArray byteArray;          // 字节数组
    QStringList hexBytes;          // 十六进制字节列表
    QString errorMessage;          // 错误信息
    int totalBytes = 0;            // 总字节数
};

/**
 * @brief 协议解析结果结构
 */
struct ProtocolParseResult {
    bool isValid = false;           // 解析是否成功
    ProtocolType protocolType = ProtocolType::Unknown;  // 协议类型
    QString protocolName;           // 协议名称
    QString errorMessage;          // 错误信息
    QVariantMap parsedData;        // 解析后的数据
    QString summary;               // 解析摘要
    QString detailInfo;            // 详细信息

    // 为QML提供便捷的访问方法
    QVariantMap toVariantMap() const {
        QVariantMap map;
        map["isValid"] = isValid;
        map["protocolType"] = static_cast<int>(protocolType);
        map["protocolName"] = protocolName;
        map["errorMessage"] = errorMessage;
        map["parsedData"] = parsedData;
        map["summary"] = summary;
        map["detailInfo"] = detailInfo;
        return map;
    }
};

// 注册元类型以便在Qt中使用
Q_DECLARE_METATYPE(ProtocolType)
Q_DECLARE_METATYPE(FrameParseResult)
Q_DECLARE_METATYPE(ByteSegmentResult)
Q_DECLARE_METATYPE(ProtocolParseResult)

/**
 * @brief 协议类型工具类
 */
class ProtocolTypeUtils {
public:
    /**
     * @brief 将协议类型转换为字符串
     * @param type 协议类型
     * @return 协议类型字符串
     */
    static QString protocolTypeToString(ProtocolType type) {
        switch (type) {
        case ProtocolType::DLT645_2007:
            return "DL/T645-2007";
        case ProtocolType::DLT645_1997:
            return "DL/T645-1997";
        case ProtocolType::NW_UP:
            return "NW-UP";
        case ProtocolType::NW_PLC:
            return "NW-PLC";
        case ProtocolType::Custom:
            return "Custom";
        case ProtocolType::Unknown:
        default:
            return "Unknown";
        }
    }

    /**
     * @brief 从字符串转换为协议类型
     * @param typeString 协议类型字符串
     * @return 协议类型
     */
    static ProtocolType stringToProtocolType(const QString &typeString) {
        if (typeString == "DL/T645-2007") return ProtocolType::DLT645_2007;
        if (typeString == "DL/T645-1997") return ProtocolType::DLT645_1997;
        if (typeString == "NW-UP") return ProtocolType::NW_UP;
        if (typeString == "NW-PLC") return ProtocolType::NW_PLC;
        if (typeString == "Custom") return ProtocolType::Custom;
        return ProtocolType::Unknown;
    }
};

// 为ProtocolType添加QDebug输出支持
inline QDebug operator<<(QDebug debug, ProtocolType type)
{
    debug.nospace() << ProtocolTypeUtils::protocolTypeToString(type);
    return debug.space();
}

#endif // PROTOCOLTYPES_H
