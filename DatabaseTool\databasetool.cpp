#include "databasetool.h"
#include <QSqlDatabase>
#include <QSqlError>
#include <QMessageBox>
#include <QSqlQuery>
#include <QFileDialog>
#include <QSqlRecord>
#include <QDebug>
#include <QUrl>
#include <QApplication>
#include <QFileInfo>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QCoreApplication>
#include <QDomDocument>
#include <QDomElement>
#include <QDomNodeList>
#include <QRegExp>
#include <QDateTime>

DatabaseTool::DatabaseTool(QWidget* parent)
    : QWidget(parent)
    , m_tableSelector(nullptr)
    , m_dataView(nullptr)
    , m_tableModel(nullptr)
    , m_connectButton(nullptr)
    , m_executeButton(nullptr)
    , m_disconnectButton(nullptr)
    , m_statusLabel(nullptr)
    , m_isConnected(false)
    , m_termDiskPath(""),
      m_databasePath(""),
      m_dataParser(new DataParser(this)), // Initialize DataParser
      m_parseRulesLoaded(false) // 初始化为未加载状态
{
    // 在构造函数中加载解析规则，确保启动时就准备好
    loadAllParseRules();
}

DatabaseTool::~DatabaseTool()
{
    // 确保在销毁对象时关闭数据库连接
    if (m_isConnected)
    {
        disconnectFromDatabase();
    }
    // m_dataParser is automatically deleted as a child of this
}

void DatabaseTool::setTermDiskPath(const QString& path)
{
    if (m_termDiskPath != path)
    {
        m_termDiskPath = path;
        emit termDiskPathChanged();
    }
}

QWidget* DatabaseTool::createDatabaseWidget()
{
    // 确保QApplication已经创建
    if (!QApplication::instance()) {
        qWarning() << "无法创建QWidget，QApplication未初始化";
        return nullptr;
    }
    
    QWidget* widget = new QWidget;
    QVBoxLayout* layout = new QVBoxLayout(widget);
    
    // 状态标签
    m_statusLabel = new QLabel(tr("未连接到数据库"));
    
    // 连接控件
    m_connectButton = new QPushButton(tr("连接数据库"));
    m_disconnectButton = new QPushButton(tr("断开连接"));
    m_disconnectButton->setEnabled(false);
    
    QHBoxLayout* connectionLayout = new QHBoxLayout;
    connectionLayout->addWidget(m_connectButton);
    connectionLayout->addWidget(m_disconnectButton);
    
    // 表选择器
    QLabel* tableLabel = new QLabel(tr("选择表:"));
    m_tableSelector = new QComboBox;
    m_tableSelector->setEnabled(false);
    
    m_executeButton = new QPushButton(tr("执行查询"));
    m_executeButton->setEnabled(false);
    
    QHBoxLayout* queryLayout = new QHBoxLayout;
    queryLayout->addWidget(tableLabel);
    queryLayout->addWidget(m_tableSelector);
    queryLayout->addWidget(m_executeButton);
    
    // 数据视图
    m_dataView = new QTableView;
    m_dataView->setEditTriggers(QTableView::NoEditTriggers);
    m_dataView->setAlternatingRowColors(true);
    m_dataView->setSortingEnabled(true);
    
    layout->addWidget(m_statusLabel);
    layout->addLayout(connectionLayout);
    layout->addLayout(queryLayout);
    layout->addWidget(m_dataView);
    
    // 连接信号和槽
    connect(m_connectButton, &QPushButton::clicked, this, &DatabaseTool::connectToDatabase);
    connect(m_executeButton, &QPushButton::clicked, this, &DatabaseTool::executeQuery);
    connect(m_disconnectButton, &QPushButton::clicked, this, &DatabaseTool::disconnectFromDatabase);
    
    return widget;
}

bool DatabaseTool::connectToDatabase()
{
    // 先确保之前的连接已经关闭
    if (m_isConnected) {
        disconnectFromDatabase();
    }
    
    try
    {
        // 使用直接设置的数据库路径
        QString filePath;
        
        if (!m_databasePath.isEmpty())
        {
            filePath = m_databasePath;
            qDebug() << "使用直接设置的数据库路径:" << filePath;
        }
        else
        {
            // 未设置数据库路径，返回失败
            qDebug() << "未设置数据库路径，无法连接";
            return false;
        }
        
        // 检查文件是否存在
        QFileInfo fileInfo(filePath);
        qDebug() << "最终文件路径检查:" << fileInfo.absoluteFilePath() 
                 << "存在:" << fileInfo.exists() 
                 << "是文件:" << fileInfo.isFile()
                 << "可读:" << fileInfo.isReadable();
        
        if (!fileInfo.exists() || !fileInfo.isFile())
        {
            qWarning() << "数据库文件不存在或不是文件:" << filePath;
            return false;
        }
        
        // 安全地关闭并移除之前的连接
        QString connectionName = "qt_sql_default_connection";
        if (QSqlDatabase::contains(connectionName))
        {
            {
                QSqlDatabase db = QSqlDatabase::database(connectionName);
                if (db.isOpen()) {
                    qDebug() << "关闭现有数据库连接";
                    db.close();
                }
            }
            qDebug() << "移除现有数据库连接";
            QSqlDatabase::removeDatabase(connectionName);
        }
        
        // 创建新的数据库连接
        QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
        db.setDatabaseName(filePath);
        
        // 尝试打开数据库
        if (!db.open())
        {
            QString errorMsg = db.lastError().text();
            qWarning() << "无法打开数据库:" << errorMsg;
            if (this->isVisible()) {
                QMessageBox::critical(this, tr("错误"), tr("无法打开数据库: %1").arg(errorMsg));
            }
            return false;
        }
        
        m_isConnected = true;
        
        // 只有在Widget可见时才更新UI
        if (m_statusLabel) {
            m_statusLabel->setText(tr("已连接到: %1").arg(QFileInfo(filePath).fileName()));
            
            // 更新UI状态
            if (m_connectButton) m_connectButton->setEnabled(false);
            if (m_disconnectButton) m_disconnectButton->setEnabled(true);
            if (m_tableSelector) {
                m_tableSelector->setEnabled(true);
                m_tableSelector->clear();
                m_tableSelector->addItems(db.tables());
            }
            if (m_executeButton) m_executeButton->setEnabled(true);
        }
        
        return true;
    }
    catch (const std::exception& e)
    {
        qCritical() << "连接数据库时发生异常:" << e.what();
        if (this->isVisible()) {
            QMessageBox::critical(this, tr("错误"), tr("连接数据库时发生异常: %1").arg(e.what()));
        }
        return false;
    }
    catch (...)
    {
        qCritical() << "连接数据库时发生未知异常";
        if (this->isVisible()) {
            QMessageBox::critical(this, tr("错误"), tr("连接数据库时发生未知异常"));
        }
        return false;
    }
}

void DatabaseTool::executeQuery()
{
    if (!m_isConnected || !m_tableSelector || m_tableSelector->currentText().isEmpty())
    {
        return;
    }
    
    QString tableName = m_tableSelector->currentText();
    
    // 创建表模型
    if (m_tableModel)
    {
        delete m_tableModel;
        m_tableModel = nullptr;
    }
    
    try {
        m_tableModel = new QSqlTableModel;
        m_tableModel->setTable(tableName);
        m_tableModel->select();
        
        if (m_dataView) {
            m_dataView->setModel(m_tableModel);
        }
    } catch (const std::exception& e) {
        qCritical() << "执行查询时发生异常:" << e.what();
        if (this->isVisible()) {
            QMessageBox::critical(this, tr("错误"), tr("执行查询时发生异常: %1").arg(e.what()));
        }
    } catch (...) {
        qCritical() << "执行查询时发生未知异常";
        if (this->isVisible()) {
            QMessageBox::critical(this, tr("错误"), tr("执行查询时发生未知异常"));
        }
    }
}

bool DatabaseTool::disconnectFromDatabase()
{
    try {
        // 清理表模型
        if (m_tableModel)
        {
            delete m_tableModel;
            m_tableModel = nullptr;
        }
        
        // 关闭数据库连接
        QString connectionName = QSqlDatabase::defaultConnection;
        {
            QSqlDatabase db = QSqlDatabase::database(connectionName);
            if (db.isOpen()) {
                db.close();
            }
        }
        QSqlDatabase::removeDatabase(connectionName);
        
        m_isConnected = false;
        
        // 更新UI状态，但先检查是否已创建
        if (m_statusLabel) {
            m_statusLabel->setText(tr("未连接到数据库"));
            
            if (m_connectButton) m_connectButton->setEnabled(true);
            if (m_disconnectButton) m_disconnectButton->setEnabled(false);
            if (m_tableSelector) {
                m_tableSelector->setEnabled(false);
                m_tableSelector->clear();
            }
            if (m_executeButton) m_executeButton->setEnabled(false);
            
            // 清空数据视图
            if (m_dataView) {
                m_dataView->setModel(nullptr);
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        qCritical() << "断开数据库连接时发生异常:" << e.what();
        return false;
    } catch (...) {
        qCritical() << "断开数据库连接时发生未知异常";
        return false;
    }
}

// 获取默认数据库路径
QString DatabaseTool::getDefaultDatabasePath() const
{
    // 返回当前设置的数据库路径
    return m_databasePath;
}

// 执行自定义SQL查询
QVariantList DatabaseTool::executeCustomQuery(const QString& sql)
{
    QVariantList result;
    
    try
    {
        if (!m_isConnected)
        {
            QVariantMap error;
            error["error"] = true;
            error["message"] = tr("数据库未连接");
            result.append(error);
            return result;
        }
        
        // 确保数据库连接有效
        QSqlDatabase db = QSqlDatabase::database();
        if (!db.isOpen()) {
            QVariantMap error;
            error["error"] = true;
            error["message"] = tr("数据库连接已关闭");
            result.append(error);
            return result;
        }
        
        // 输出调试信息
        qDebug() << "执行SQL查询:" << sql;
        
        QSqlQuery query;
        if (!query.exec(sql))
        {
            QString errorMsg = query.lastError().text();
            qWarning() << "SQL查询失败:" << errorMsg;
            QVariantMap error;
            error["error"] = true;
            error["message"] = errorMsg;
            result.append(error);
            return result;
        }
        
        // 如果是SELECT查询，返回结果集
        if (sql.trimmed().toLower().startsWith("select"))
        {
            QSqlRecord record = query.record();
            int columnCount = record.count();
            
            qDebug() << "查询结果列数:" << columnCount;
            
            // 添加列名
            QVariantMap columns;
            columns["isHeader"] = true;
            for (int i = 0; i < columnCount; ++i)
            {
                QString fieldName = record.fieldName(i);
                columns[QString("col%1").arg(i + 1)] = fieldName;
                qDebug() << "列" << (i+1) << "名称:" << fieldName;
            }
            result.append(columns);
            
            // 添加数据行
            int rowCount = 0;
            while (query.next())
            {
                QVariantMap row;
                for (int i = 0; i < columnCount; ++i)
                {
                    QVariant value = query.value(i);
                    QString colKey = QString("col%1").arg(i + 1);
                    
                    // 输出调试信息，特别关注第5列
                    if (i == 4) { // 第5列
                        if (value.type() == QVariant::ByteArray) {
                            QByteArray ba = value.toByteArray();
                        }
                    }
                    
                    // 确保值是有效的，如果是null则转换为空字符串
                    if (value.isNull()) {
                        row[colKey] = "";
                    } else {
                        // 对于二进制数据，转换为十六进制字符串
                        if (value.type() == QVariant::ByteArray) {
                            QByteArray ba = value.toByteArray();
                            if (ba.isEmpty()) {
                                row[colKey] = "";
                            } else {
                                // 直接将二进制数据转换为十六进制字符串，不限制字节数
                                row[colKey] = QString(ba.toHex());
                            }
                        } else {
                            // 对于其他类型，转换为字符串
                            row[colKey] = value.toString();
                        }
                    }
                }
                result.append(row);
                rowCount++;
            }
            
            qDebug() << "查询返回" << rowCount << "行数据";
        }
        else
        {
            // 对于非SELECT查询，返回受影响的行数
            int affectedRows = query.numRowsAffected();
            QVariantMap info;
            info["affectedRows"] = affectedRows;
            result.append(info);
            qDebug() << "非SELECT查询影响了" << affectedRows << "行";
        }
    }
    catch (const std::exception& e)
    {
        qCritical() << "执行SQL查询时发生异常:" << e.what();
        QVariantMap error;
        error["error"] = true;
        error["message"] = QString("执行SQL查询时发生异常: %1").arg(e.what());
        result.append(error);
    }
    catch (...)
    {
        qCritical() << "执行SQL查询时发生未知异常";
        QVariantMap error;
        error["error"] = true;
        error["message"] = "执行SQL查询时发生未知异常";
        result.append(error);
    }
    
    return result;
}

// 获取表列表
QStringList DatabaseTool::getTablesList()
{
    QStringList tables;
    
    try
    {
        if (!m_isConnected)
        {
            qWarning() << "尝试获取表列表，但数据库未连接";
            return tables;
        }
        
        // 确保数据库连接有效
        QSqlDatabase db = QSqlDatabase::database();
        if (!db.isOpen()) {
            qWarning() << "数据库连接已关闭";
            m_isConnected = false;
            return tables;
        }
        
        tables = db.tables();
        
        qDebug() << "数据库中的表数量:" << tables.count();
        
        // 如果常规方法无法获取表，尝试使用SQL查询
        if (tables.isEmpty())
        {
            qDebug() << "使用SQL查询获取表列表";
            
            // 尝试多种SQL查询方式获取表列表
            QStringList queries = {
                "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name",
                "SELECT tbl_name FROM sqlite_master WHERE type='table' ORDER BY tbl_name",
                "PRAGMA table_list"
            };
            
            for (const QString& queryStr : queries)
            {
                try
                {
                    qDebug() << "尝试SQL查询:" << queryStr;
                    QSqlQuery query(queryStr);
                    QStringList queryTables;
                    
                    while (query.next())
                    {
                        QString tableName = query.value(0).toString();
                        queryTables.append(tableName);
                        qDebug() << "通过SQL查询找到表:" << tableName;
                    }
                    
                    if (!queryTables.isEmpty())
                    {
                        tables = queryTables;
                        qDebug() << "通过SQL查询找到表数量:" << tables.count();
                        break;
                    }
                }
                catch (const std::exception& e)
                {
                    qWarning() << "SQL查询异常:" << e.what();
                    continue;
                }
                catch (...)
                {
                    qWarning() << "SQL查询未知异常";
                    continue;
                }
            }
            
            // 如果仍然没有找到表，尝试直接查询一些常见表
            if (tables.isEmpty())
            {
                qDebug() << "尝试直接查询一些常见表";
                QStringList commonTables = {
                    "record", "records", "data", "main", "info", "system", 
                    "config", "settings", "users", "log", "logs", "events"
                };
                
                for (const QString& tableName : commonTables)
                {
                    try
                    {
                        qDebug() << "尝试查询表:" << tableName;
                        QSqlQuery query(QString("SELECT * FROM %1 LIMIT 1").arg(tableName));
                        if (query.exec())
                        {
                            qDebug() << "表存在:" << tableName;
                            tables.append(tableName);
                        }
                    }
                    catch (const std::exception& e)
                    {
                        qWarning() << "查询表异常:" << e.what();
                        continue;
                    }
                    catch (...)
                    {
                        qWarning() << "查询表未知异常";
                        continue;
                    }
                }
            }
        }
    }
    catch (const std::exception& e)
    {
        qCritical() << "获取表列表时发生异常:" << e.what();
    }
    catch (...)
    {
        qCritical() << "获取表列表时发生未知异常";
    }
    
    return tables;
}

// 获取表数据
QVariantList DatabaseTool::getTableData(const QString& tableName)
{
    QVariantList result;
    
    if (!m_isConnected)
    {
        QVariantMap error;
        error["error"] = true;
        error["message"] = tr("数据库未连接");
        result.append(error);
        return result;
    }
    
    // 确保表名不为空
    if (tableName.isEmpty()) {
        QVariantMap error;
        error["error"] = true;
        error["message"] = tr("表名不能为空");
        result.append(error);
        return result;
    }
    
    qDebug() << "获取表数据:" << tableName;
    
    // 使用双引号包围表名，防止SQL注入和特殊字符问题
    QString sql = QString("SELECT * FROM \"%1\"").arg(tableName);
    return executeCustomQuery(sql);
} 

// 检查文件是否存在
bool DatabaseTool::checkFileExists(const QString& filePath) const
{
    // 移除可能的文件URL前缀
    QString path = filePath;
    if (path.startsWith("file:///"))
    {
        path = QUrl(path).toLocalFile();
    }
    else if (path.startsWith("file://"))
    {
        path = path.mid(7);
    }
    
    QFileInfo fileInfo(path);
    bool exists = fileInfo.exists() && fileInfo.isFile() && fileInfo.isReadable();
    qDebug() << "文件存在检查:" << path << "存在:" << exists 
             << "是文件:" << fileInfo.isFile() 
             << "可读:" << fileInfo.isReadable()
             << "大小:" << fileInfo.size();
    
    return exists;
} 

// 保存文本到文件
bool DatabaseTool::saveTextToFile(const QString& text, const QString& filePath)
{
    try {
        qDebug() << "保存文本到文件:" << filePath;
        
        // 移除可能的文件URL前缀
        QString path = filePath;
        if (path.startsWith("file:///"))
        {
            path = QUrl(path).toLocalFile();
        }
        else if (path.startsWith("file://"))
        {
            path = path.mid(7);
        }
        
        // 如果是相对路径，则相对于应用程序目录
        QFileInfo fileInfo(path);
        if (!fileInfo.isAbsolute()) {
            QString appDir = QCoreApplication::applicationDirPath();
            path = QDir(appDir).filePath(path);
            qDebug() << "转换为应用程序目录的绝对路径:" << path;
        }
        
        // 确保目录存在
        QFileInfo finalFileInfo(path);
        QDir directory = finalFileInfo.dir();
        
        if (!directory.exists()) {
            qDebug() << "创建目录:" << directory.path();
            if (!directory.mkpath(".")) {
                qWarning() << "无法创建目录:" << directory.path();
                return false;
            }
        }
        
        // 打开文件并写入内容
        QFile file(path);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            qWarning() << "无法打开文件进行写入:" << path << ", 错误:" << file.errorString();
            return false;
        }
        
        QTextStream out(&file);
        out << text;
        
        file.close();
        qDebug() << "文件保存成功:" << path;
        return true;
    }
    catch (const std::exception& e) {
        qCritical() << "保存文件时发生异常:" << e.what();
        return false;
    }
    catch (...) {
        qCritical() << "保存文件时发生未知异常";
        return false;
    }
} 

// 列出SQL脚本文件
QStringList DatabaseTool::listSqlScripts() const
{
    QStringList scriptsList;
    
    try {
        // 获取应用程序目录
        QString appDir = QCoreApplication::applicationDirPath();
        
        // 构建SQL脚本目录路径
        QString sqlScriptsDir = QDir(appDir).filePath("tools/user_sql");
        
        qDebug() << "扫描SQL脚本目录:" << sqlScriptsDir;
        
        // 检查目录是否存在
        QDir scriptsDir(sqlScriptsDir);
        if (!scriptsDir.exists()) {
            qDebug() << "SQL脚本目录不存在，创建目录";
            // 创建目录
            if (!scriptsDir.mkpath(".")) {
                qWarning() << "无法创建SQL脚本目录:" << sqlScriptsDir;
                return scriptsList;
            }
        }
        
        // 设置过滤器只显示SQL文件
        scriptsDir.setNameFilters(QStringList() << "*.sql");
        scriptsDir.setFilter(QDir::Files | QDir::NoDotAndDotDot);
        
        // 获取文件列表
        QFileInfoList fileList = scriptsDir.entryInfoList();
        
        // 遍历文件列表
        foreach (const QFileInfo& fileInfo, fileList) {
            scriptsList.append(fileInfo.fileName());
        }
        
        qDebug() << "找到SQL脚本文件:" << scriptsList.join(", ");
    }
    catch (const std::exception& e) {
        qCritical() << "列出SQL脚本时发生异常:" << e.what();
    }
    catch (...) {
        qCritical() << "列出SQL脚本时发生未知异常";
    }
    
    return scriptsList;
}

// 加载SQL脚本内容
QString DatabaseTool::loadSqlScript(const QString& scriptName) const
{
    QString scriptContent;
    
    try {
        // 获取应用程序目录
        QString appDir = QCoreApplication::applicationDirPath();
        
        // 构建SQL脚本文件路径
        QString scriptPath = QDir(appDir).filePath("tools/user_sql/" + scriptName);
        
        qDebug() << "加载SQL脚本:" << scriptPath;
        
        // 检查文件是否存在
        QFileInfo fileInfo(scriptPath);
        if (!fileInfo.exists() || !fileInfo.isFile()) {
            qWarning() << "SQL脚本文件不存在:" << scriptPath;
            return scriptContent;
        }
        
        // 打开并读取文件内容
        QFile file(scriptPath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            qWarning() << "无法打开SQL脚本文件:" << scriptPath << ", 错误:" << file.errorString();
            return scriptContent;
        }
        
        QTextStream in(&file);
        scriptContent = in.readAll();
        
        file.close();
        qDebug() << "SQL脚本加载成功，内容长度:" << scriptContent.length();
    }
    catch (const std::exception& e) {
        qCritical() << "加载SQL脚本时发生异常:" << e.what();
    }
    catch (...) {
        qCritical() << "加载SQL脚本时发生未知异常";
    }
    
    return scriptContent;
} 

// 导出表数据到文件
bool DatabaseTool::exportTableData(const QString& tableName, const QString& filePath, const QString& parseRule)
{
    try {
        qDebug() << "导出表数据:" << tableName << "到文件:" << filePath << "解析规则:" << parseRule;
        
        if (!m_isConnected) {
            qWarning() << "数据库未连接，无法导出数据";
            return false;
        }
        
        // 检查文件扩展名
        bool isCSV = filePath.toLower().endsWith(".csv");
        bool isExcel = filePath.toLower().endsWith(".xlsx");
        
        if (!isCSV && !isExcel) {
            qWarning() << "不支持的文件格式，只支持CSV和Excel";
            return false;
        }
        
        // 获取表数据
        QString sql = QString("SELECT * FROM \"%1\"").arg(tableName);
        QVariantList tableData = executeCustomQuery(sql);
        
        if (tableData.isEmpty()) {
            qWarning() << "获取表数据失败，无法导出";
            return false;
        }
        
        // 检查是否有错误
        QVariantMap firstRow = tableData.first().toMap();
        if (firstRow.contains("error") && firstRow["error"].toBool()) {
            qWarning() << "获取表数据出错:" << firstRow["message"].toString();
            return false;
        }
        
        // 如果有解析规则，添加Parse列并应用解析规则
        if (!parseRule.isEmpty() && m_dataParser) {
            qDebug() << "应用解析规则:" << parseRule;
            
            // 找到DataSet列的索引
            int dataSetColIndex = -1;
            QVariantMap headerRow = tableData.first().toMap();
            
            for (int i = 1; i <= headerRow.count(); i++) {
                QString colKey = QString("col%1").arg(i);
                if (headerRow.contains(colKey) && headerRow[colKey].toString() == "DataSet") {
                    dataSetColIndex = i;
                    break;
                }
            }
            
            if (dataSetColIndex >= 0) {
                // 添加Parse列到表头
                QString parseColKey = QString("col%1").arg(headerRow.count());
                headerRow[parseColKey] = "Parse";
                tableData.replace(0, headerRow);
                
                // 对每一行应用解析规则
                QString dataSetColKey = QString("col%1").arg(dataSetColIndex);
                for (int i = 1; i < tableData.count(); i++) {
                    QVariantMap row = tableData[i].toMap();
                    if (row.contains(dataSetColKey)) {
                        QString hexData = row[dataSetColKey].toString();
                        if (!hexData.isEmpty()) {
                            QString parseResult = m_dataParser->batchParseHexData(hexData, parseRule);
                            row[parseColKey] = parseResult;
                            tableData.replace(i, row);
                        }
                    }
                }
            }
        }
        
        // 导出数据到文件
        return exportDataToFile(tableData, filePath, isCSV);
    }
    catch (const std::exception& e) {
        qCritical() << "导出表数据时发生异常:" << e.what();
        return false;
    }
    catch (...) {
        qCritical() << "导出表数据时发生未知异常";
        return false;
    }
}

// 导出数据到文件
bool DatabaseTool::exportDataToFile(const QVariantList& data, const QString& filePath, bool isCSV)
{
    try {
        qDebug() << "导出数据到文件:" << filePath << "格式:" << (isCSV ? "CSV" : "Excel");
        
        if (data.isEmpty()) {
            qWarning() << "没有数据可导出";
            return false;
        }
        
        // 移除可能的文件URL前缀
        QString path = filePath;
        if (path.startsWith("file:///")) {
            path = QUrl(path).toLocalFile();
        }
        else if (path.startsWith("file://")) {
            path = path.mid(7);
        }
        
        // 如果是CSV格式
        if (isCSV) {
            return exportToCSV(data, path);
        }
        // 如果是Excel格式
        else if (path.toLower().endsWith(".xlsx")) {
            return exportToExcel(data, path);
        }
        else {
            qWarning() << "不支持的文件格式";
            return false;
        }
    }
    catch (const std::exception& e) {
        qCritical() << "导出数据到文件时发生异常:" << e.what();
        return false;
    }
    catch (...) {
        qCritical() << "导出数据到文件时发生未知异常";
        return false;
    }
}

// 导出到CSV格式
bool DatabaseTool::exportToCSV(const QVariantList& data, const QString& filePath)
{
    try {
        QFile file(filePath);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            qWarning() << "无法打开文件进行写入:" << filePath << ", 错误:" << file.errorString();
            return false;
        }
        
        QTextStream out(&file);
        
        // 获取表头
        if (data.isEmpty()) {
            file.close();
            return false;
        }
        
        QVariantMap headerRow = data.first().toMap();
        QStringList headers;
        
        // 收集所有列名
        QList<QString> columnKeys;
        QMap<QString, QString> columnNames; // 列键到列名的映射
        for (int i = 1; i <= 100; i++) { // 假设最多100列
            QString colKey = QString("col%1").arg(i);
            if (headerRow.contains(colKey)) {
                QString columnName = headerRow[colKey].toString();
                headers << columnName;
                columnKeys << colKey;
                columnNames[colKey] = columnName; // 保存列名映射
            }
            else {
                break;
            }
        }
        
        // 写入表头
        out << headers.join(",") << "\n";
        
        // 写入数据行
        for (int i = 1; i < data.size(); i++) {
            QVariantMap row = data[i].toMap();
            QStringList rowData;
            
            for (const QString& colKey : columnKeys) {
                QString cellValue;
                
                if (row.contains(colKey)) {
                    QVariant value = row[colKey];
                    
                    // 处理不同类型的值
                    if (value.isNull()) {
                        cellValue = "";
                    }
                    else if (value.type() == QVariant::ByteArray) {
                        cellValue = QString(value.toByteArray().toHex());
                    }
                    else {
                        // 检查是否是DataTime或CollectionTime列
                        QString columnName = columnNames.value(colKey);
                        if ((columnName == "DataTime" || columnName == "CollectionTime") && 
                            value.toString().contains(QRegExp("^-?\\d+(\\.\\d+)?$"))) {
                            // 将Unix时间戳转换为UTC格式
                            bool ok;
                            qint64 timestamp = value.toLongLong(&ok);
                            if (ok) {
                                QDateTime dateTime = QDateTime::fromSecsSinceEpoch(timestamp);
                                if (dateTime.isValid()) {
                                    // 格式化为UTC时间
                                    cellValue = dateTime.toUTC().toString("yyyy-MM-dd hh:mm:ss") + " UTC";
                                } else {
                                    cellValue = value.toString();
                                }
                            } else {
                                cellValue = value.toString();
                            }
                        } else {
                            cellValue = value.toString();
                        }
                    }
                    
                    // 如果值包含逗号或引号，需要用引号包裹
                    if (cellValue.contains(',') || cellValue.contains('"') || 
                        cellValue.contains('\n') || cellValue.contains('\r')) {
                        // 将引号替换为两个引号
                        cellValue.replace("\"", "\"\"");
                        // 用引号包裹
                        cellValue = "\"" + cellValue + "\"";
                    }
                }
                
                rowData << cellValue;
            }
            
            out << rowData.join(",") << "\n";
        }
        
        file.close();
        qDebug() << "CSV导出成功:" << filePath;
        return true;
    }
    catch (const std::exception& e) {
        qCritical() << "导出CSV时发生异常:" << e.what();
        return false;
    }
    catch (...) {
        qCritical() << "导出CSV时发生未知异常";
        return false;
    }
}

// 导出到Excel格式
bool DatabaseTool::exportToExcel(const QVariantList& data, const QString& filePath)
{
    // 由于Qt没有内置的Excel导出功能，这里使用简单的CSV导出
    // 实际项目中可以使用第三方库如QXlsx或调用Excel COM接口
    qDebug() << "Excel导出功能未实现，使用CSV格式代替";
    return exportToCSV(data, filePath);
} 

// 删除文件
bool DatabaseTool::deleteFile(const QString& filePath)
{
    try {
        qDebug() << "删除文件:" << filePath;
        
        // 移除可能的文件URL前缀
        QString path = filePath;
        if (path.startsWith("file:///"))
        {
            path = QUrl(path).toLocalFile();
        }
        else if (path.startsWith("file://"))
        {
            path = path.mid(7);
        }
        
        // 如果是相对路径，则相对于应用程序目录
        QFileInfo fileInfo(path);
        if (!fileInfo.isAbsolute()) {
            QString appDir = QCoreApplication::applicationDirPath();
            path = QDir(appDir).filePath(path);
            qDebug() << "转换为应用程序目录的绝对路径:" << path;
        }
        
        // 检查文件是否存在
        QFileInfo finalFileInfo(path);
        if (!finalFileInfo.exists()) {
            qWarning() << "文件不存在:" << path;
            return false;
        }
        
        // 检查是否有权限删除
        QFile file(path);
        if (!file.remove()) {
            qWarning() << "无法删除文件:" << path << ", 错误:" << file.errorString();
            return false;
        }
        
        qDebug() << "文件删除成功:" << path;
        return true;
    }
    catch (const std::exception& e) {
        qCritical() << "删除文件时发生异常:" << e.what();
        return false;
    }
    catch (...) {
        qCritical() << "删除文件时发生未知异常";
        return false;
    }
}

// 根据表名获取解析规则
QString DatabaseTool::getParseRuleForTable(const QString& tableName) const
{
    try {
        qDebug() << "尝试为表" << tableName << "获取解析规则";
        
        // 如果表名为空，返回空规则
        if (tableName.isEmpty()) {
            return QString();
        }
        
        // 提取表名前缀
        QString prefix = extractPrefixFromTableName(tableName);
        if (prefix.isEmpty()) {
            qDebug() << "表名" << tableName << "不符合tb_XXXXXXXX格式";
            return QString();
        }
        
        qDebug() << "提取表名前缀:" << prefix;
        
        // 如果解析规则已加载到内存，直接从内存中查找
        if (m_parseRulesLoaded) {
            if (m_parseRules.contains(prefix)) {
                QString rule = m_parseRules.value(prefix);
                qDebug() << "从缓存中找到表" << tableName << "的解析规则:" << rule;
                return rule;
            } else {
                qDebug() << "缓存中未找到表" << tableName << "的解析规则";
                return QString();
            }
        }
        
        // 如果未加载到内存，尝试重新加载
        if (loadAllParseRules()) {
            if (m_parseRules.contains(prefix)) {
                QString rule = m_parseRules.value(prefix);
                qDebug() << "重新加载后找到表" << tableName << "的解析规则:" << rule;
                return rule;
            }
        }
        
        qDebug() << "未找到表" << tableName << "的解析规则";
        return QString();
    }
    catch (const std::exception& e) {
        qCritical() << "获取解析规则时发生异常:" << e.what();
        return QString();
    }
    catch (...) {
        qCritical() << "获取解析规则时发生未知异常";
        return QString();
    }
} 

// 根据表名获取解析规则描述
QString DatabaseTool::getDescriptionForTable(const QString& tableName) const
{
    try {
        qDebug() << "尝试为表" << tableName << "获取解析规则描述";
        
        // 如果表名为空，返回空描述
        if (tableName.isEmpty()) {
            return QString();
        }
        
        // 提取表名前缀
        QString prefix = extractPrefixFromTableName(tableName);
        if (prefix.isEmpty()) {
            qDebug() << "表名" << tableName << "不符合tb_XXXXXXXX格式";
            return QString();
        }
        
        qDebug() << "提取表名前缀:" << prefix;
        
        // 如果解析规则已加载到内存，直接从内存中查找
        if (m_parseRulesLoaded) {
            if (m_parseDescriptions.contains(prefix)) {
                QString description = m_parseDescriptions.value(prefix);
                qDebug() << "从缓存中找到表" << tableName << "的解析规则描述:" << description;
                return description;
            } else {
                qDebug() << "缓存中未找到表" << tableName << "的解析规则描述";
                return QString();
            }
        }
        
        // 如果未加载到内存，尝试重新加载
        if (loadAllParseRules()) {
            if (m_parseDescriptions.contains(prefix)) {
                QString description = m_parseDescriptions.value(prefix);
                qDebug() << "重新加载后找到表" << tableName << "的解析规则描述:" << description;
                return description;
            }
        }
        
        qDebug() << "未找到表" << tableName << "的解析规则描述";
        return QString();
    }
    catch (const std::exception& e) {
        qCritical() << "获取解析规则描述时发生异常:" << e.what();
        return QString();
    }
    catch (...) {
        qCritical() << "获取解析规则描述时发生未知异常";
        return QString();
    }
} 

// 从表名中提取前缀
QString DatabaseTool::extractPrefixFromTableName(const QString& tableName) const
{
    QString prefix;
    
    // 首先尝试匹配十六进制格式的表名前缀
    QRegExp hexRx("tb_([0-9a-fA-F]+)");
    if (hexRx.indexIn(tableName) != -1) {
        prefix = hexRx.cap(1).toLower(); // 转为小写进行比较
        return prefix;
    }
    
    // 如果不是十六进制格式，则尝试匹配一般的表名前缀
    QRegExp generalRx("tb_([a-zA-Z0-9_]+)");
    if (generalRx.indexIn(tableName) != -1) {
        prefix = generalRx.cap(1).toLower(); // 转为小写进行比较
        qDebug() << "提取到一般格式的表名前缀:" << prefix;
        return prefix;
    }
    
    return QString(); // 如果不匹配，返回空字符串
}

// 加载所有解析规则到内存
bool DatabaseTool::loadAllParseRules() const
{
    try {
        // 如果已经加载过，直接返回true
        if (m_parseRulesLoaded && !m_parseRules.isEmpty()) {
            qDebug() << "解析规则已加载，跳过重复加载";
            return true;
        }
        
        qDebug() << "开始加载所有解析规则到内存...";
        
        // 清空现有的缓存
        m_parseRules.clear();
        m_parseDescriptions.clear();
        
        // 构建XML文件路径
        QString appDir = QCoreApplication::applicationDirPath();
        QString xmlPath = QDir(appDir).filePath("config/ParseRules.xml");
        
        // 检查文件是否存在
        QFileInfo fileInfo(xmlPath);
        if (!fileInfo.exists() || !fileInfo.isFile()) {
            qWarning() << "解析规则文件不存在:" << xmlPath;
            m_parseRulesLoaded = false;
            // 使用const_cast来临时移除const限定符，以便发射信号
            const_cast<DatabaseTool*>(this)->emit parseRulesLoaded(false);
            return false;
        }
        
        // 打开XML文件
        QFile file(xmlPath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            qWarning() << "无法打开解析规则文件:" << xmlPath;
            m_parseRulesLoaded = false;
            // 使用const_cast来临时移除const限定符，以便发射信号
            const_cast<DatabaseTool*>(this)->emit parseRulesLoaded(false);
            return false;
        }
        
        // 读取XML内容
        QDomDocument doc;
        if (!doc.setContent(&file)) {
            file.close();
            qWarning() << "XML解析错误";
            m_parseRulesLoaded = false;
            // 使用const_cast来临时移除const限定符，以便发射信号
            const_cast<DatabaseTool*>(this)->emit parseRulesLoaded(false);
            return false;
        }
        file.close();
        
        // 遍历XML加载所有规则
        QDomElement root = doc.documentElement();
        QDomNodeList items = root.elementsByTagName("item");
        
        int ruleCount = 0;
        for (int i = 0; i < items.count(); i++) {
            QDomElement item = items.at(i).toElement();
            if (item.isNull()) continue;
            
            // 获取name节点
            QDomElement nameElement = item.elementsByTagName("name").at(0).toElement();
            if (nameElement.isNull()) {
                // 尝试使用n标签作为替代
                nameElement = item.elementsByTagName("n").at(0).toElement();
                if (nameElement.isNull()) continue;
            }
            
            QString name = nameElement.text().toLower(); // 转为小写进行比较
            if (name.isEmpty()) continue;
            
            // 获取解析规则
            QDomElement rulseElement = item.elementsByTagName("rulse").at(0).toElement();
            if (!rulseElement.isNull()) {
                QString rulse = rulseElement.text();
                if (!rulse.isEmpty()) {
                    m_parseRules[name] = rulse;
                    ruleCount++;
                }
            }
            
            // 获取描述
            QDomElement descElement = item.elementsByTagName("description").at(0).toElement();
            if (!descElement.isNull()) {
                QString description = descElement.text();
                if (!description.isEmpty()) {
                    m_parseDescriptions[name] = description;
                }
            }
        }
        
        qDebug() << "成功加载" << ruleCount << "条解析规则到内存";
        m_parseRulesLoaded = true;
        // 使用const_cast来临时移除const限定符，以便发射信号
        const_cast<DatabaseTool*>(this)->emit parseRulesLoaded(true);
        return true;
    }
    catch (const std::exception& e) {
        qCritical() << "加载解析规则时发生异常:" << e.what();
        m_parseRulesLoaded = false;
        // 使用const_cast来临时移除const限定符，以便发射信号
        const_cast<DatabaseTool*>(this)->emit parseRulesLoaded(false);
        return false;
    }
    catch (...) {
        qCritical() << "加载解析规则时发生未知异常";
        m_parseRulesLoaded = false;
        // 使用const_cast来临时移除const限定符，以便发射信号
        const_cast<DatabaseTool*>(this)->emit parseRulesLoaded(false);
        return false;
    }
} 
