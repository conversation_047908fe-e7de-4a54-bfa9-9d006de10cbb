import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import Qt.labs.platform 1.1 as Labs

Item {
    id: paramViewerRoot
    anchors.fill: parent

    property string termDiskPath: ""
    property var openedFiles: ({}) // 存储已打开的文件内容
    property int tabCount: 0 // 跟踪标签页数量
    property int currentTabIndex: -1 // 当前标签页索引

    // 背景色
    Rectangle {
        anchors.fill: parent
        color: "#ffffff"
    }

    // 顶部工具栏
    Rectangle {
        id: toolbar
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        height: 50
        color: "#f0f0f0"
        border.color: "#cccccc"
        border.width: 1

        Row {
            anchors.left: parent.left
            anchors.leftMargin: 10
            anchors.verticalCenter: parent.verticalCenter
            spacing: 10

            Button {
                text: "打开文件"
                onClicked: fileDialog.open()
            }

            Button {
                text: "关闭当前"
                enabled: paramViewerRoot.tabCount > 0
                onClicked: {
                    if (paramViewerRoot.currentTabIndex >= 0) {
                        closeTab(paramViewerRoot.currentTabIndex)
                    }
                }
            }

            Rectangle {
                width: 1
                height: 30
                color: "#cccccc"
            }

            TextField {
                id: searchField
                width: 200
                placeholderText: "搜索参数..."
                onTextChanged: filterCurrentTab()
            }

            Button {
                text: "清除搜索"
                enabled: searchField.text.length > 0
                onClicked: {
                    searchField.text = ""
                    filterCurrentTab()
                }
            }

            Rectangle {
                width: 1
                height: 30
                color: "#cccccc"
            }

            // 视图切换按钮组
            Row {
                spacing: 0

                Button {
                    id: tableViewBtn
                    text: "表格视图"
                    width: 80
                    height: 30
                    checkable: true
                    checked: true
                    background: Rectangle {
                        color: parent.checked ? "#0078d4" : "#f0f0f0"
                        border.color: "#cccccc"
                        border.width: 1
                        radius: parent.checked ? 3 : 0
                    }
                    contentItem: Text {
                        text: parent.text
                        color: parent.checked ? "white" : "black"
                        font.pixelSize: 11
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    onClicked: {
                        if (!checked) {
                            checked = true
                            xmlViewBtn.checked = false
                            switchViewMode(true)
                        }
                    }
                }

                Button {
                    id: xmlViewBtn
                    text: "XML视图"
                    width: 80
                    height: 30
                    checkable: true
                    checked: false
                    background: Rectangle {
                        color: parent.checked ? "#0078d4" : "#f0f0f0"
                        border.color: "#cccccc"
                        border.width: 1
                        radius: parent.checked ? 3 : 0
                    }
                    contentItem: Text {
                        text: parent.text
                        color: parent.checked ? "white" : "black"
                        font.pixelSize: 11
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    onClicked: {
                        if (!checked) {
                            checked = true
                            tableViewBtn.checked = false
                            switchViewMode(false)
                        }
                    }
                }
            }
        }
    }
    
    // 标签页区域 - 改进样式
    Rectangle {
        id: tabBarContainer
        anchors.top: toolbar.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 42
        color: "#f8f9fa"
        border.color: "#dee2e6"
        border.width: 1

        TabBar {
            id: tabBar
            anchors.fill: parent
            anchors.margins: 1
            currentIndex: currentTabIndex
            background: Rectangle {
                color: "transparent"
            }

            // 动态创建标签页
            onCurrentIndexChanged: {
                if (currentIndex >= 0) {
                    currentTabIndex = currentIndex
                    stackLayout.currentIndex = currentIndex + 1 // +1 因为第一个是默认空页面
                }
            }
        }
    }

    // 主内容区域
    StackLayout {
        id: stackLayout
        anchors.top: tabBarContainer.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        currentIndex: currentTabIndex >= 0 ? currentTabIndex + 1 : 0 // +1 因为第一个是默认空页面

        // 默认空页面
        Item {
            visible: paramViewerRoot.tabCount === 0

            Rectangle {
                anchors.centerIn: parent
                width: 400
                height: 200
                color: "#f5f5f5"
                border.color: "#dddddd"
                radius: 8

                Column {
                    anchors.centerIn: parent
                    spacing: 20

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: "参数查看器"
                        font.pixelSize: 18
                        font.bold: true
                        color: "#666666"
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: "点击\"打开文件\"按钮加载参数文件"
                        font.pixelSize: 14
                        color: "#888888"
                    }

                    Text {
                        anchors.horizontalCenter: parent.horizontalCenter
                        text: "每个文件将在独立的标签页中显示"
                        font.pixelSize: 12
                        color: "#aaaaaa"
                    }
                }
            }
        }
    }

    
    // 文件选择对话框
    Labs.FileDialog {
        id: fileDialog
        title: "选择参数文件"
        folder: termDiskPath ? "file:///" + termDiskPath : Labs.StandardPaths.writableLocation(Labs.StandardPaths.HomeLocation)
        nameFilters: ["参数文件 (*.xml)", "所有文件 (*)"]
        onAccepted: {
            var filePath = fileDialog.file.toString()
            filePath = filePath.replace(/^(file:\/{2,3})/, "")
            if (Qt.platform.os === "windows") {
                filePath = decodeURIComponent(filePath)
            }
            loadParameterFile(filePath)
        }
    }

    // 核心功能函数
    function loadParameterFile(filePath) {
        console.log("加载参数文件:", filePath)

        // 检查文件是否已经打开
        var fileName = filePath.split('/').pop().split('\\').pop()
        if (openedFiles[fileName]) {
            // 文件已打开，切换到对应标签页
            for (var i = 0; i < tabBar.count; i++) {
                if (tabBar.itemAt(i).text === fileName) {
                    tabBar.currentIndex = i
                    return
                }
            }
        }

        // 模拟读取XML文件内容（实际应该通过C++后端读取）
        var xmlContent = getSimulatedXmlContent(fileName)
        var parsedData = parseXmlContent(xmlContent)

        // 存储文件内容
        openedFiles[fileName] = {
            filePath: filePath,
            fileName: fileName,
            xmlContent: xmlContent,
            parsedData: parsedData,
            filteredData: parsedData // 初始时过滤后的数据与原始数据相同
        }

        // 创建新标签页
        createNewTab(fileName)
    }

    function createNewTab(fileName) {
        // 生成安全的ID（替换特殊字符为下划线，并确保以字母开头）
        var safeId = fileName.replace(/[^a-zA-Z0-9_]/g, '_')
        // 确保ID以字母开头
        if (safeId.charAt(0) < 'A' || (safeId.charAt(0) > 'Z' && safeId.charAt(0) < 'a') || safeId.charAt(0) > 'z') {
            safeId = "tab_" + safeId
        }

        console.log("创建标签页，文件名:", fileName, "安全ID:", safeId)

        try {
            // 创建美观的标签按钮
            var tabButtonCode = 'import QtQuick 2.12; import QtQuick.Controls 2.12; ' +
                'TabButton { ' +
                'id: tabButton_' + safeId + '; ' +
                'text: "' + fileName + '"; ' +
                'width: Math.max(implicitWidth + 40, 140); ' +
                'height: 38; ' +
                'background: Rectangle { ' +
                'color: parent.checked ? "#ffffff" : "#e9ecef"; ' +
                'border.color: "#dee2e6"; ' +
                'border.width: 1; ' +
                'radius: 6; ' +
                'Rectangle { ' +
                'anchors.bottom: parent.bottom; ' +
                'width: parent.width; ' +
                'height: 3; ' +
                'color: parent.parent.checked ? "#0d6efd" : "transparent"; ' +
                'radius: 1 ' +
                '} ' +
                '}' +
                'contentItem: Text { ' +
                'text: parent.text; ' +
                'color: parent.checked ? "#212529" : "#6c757d"; ' +
                'font.pixelSize: 12; ' +
                'font.bold: parent.checked; ' +
                'horizontalAlignment: Text.AlignHCenter; ' +
                'verticalAlignment: Text.AlignVCenter; ' +
                'elide: Text.ElideRight ' +
                '} }';
            console.log("创建标签按钮，代码:", tabButtonCode);
            var tabButton = Qt.createQmlObject(tabButtonCode, tabBar, "dynamicTabButton_" + safeId);

            // 创建标签页内容 - 表格形式
            var tabContentCode = 'import QtQuick 2.12; import QtQuick.Controls 2.12; import QtQuick.Layouts 1.12; ' +
                'Item { id: tabContent_' + safeId + ' }';
            console.log("创建标签内容，代码:", tabContentCode);
            var tabContent = Qt.createQmlObject(tabContentCode, stackLayout, "dynamicTabContent_" + safeId);

            // 添加表格和XML查看区域
            var contentLayoutCode = 'import QtQuick 2.12; import QtQuick.Controls 2.12; import QtQuick.Layouts 1.12; ' +
                'ColumnLayout { id: contentLayout_' + safeId + '; anchors.fill: parent; anchors.margins: 5; spacing: 5 }';
            console.log("创建内容布局，代码:", contentLayoutCode);
            var contentLayout = Qt.createQmlObject(contentLayoutCode, tabContent, "dynamicContentLayout_" + safeId);

            // 添加表格视图
            var tableView = createTableView(contentLayout, safeId);
            if (!tableView) {
                throw new Error("创建表格视图失败");
            }

            // XML查看区域已移除，现在通过XML按钮切换视图模式

            // 设置标签页索引
            tabCount++;
            currentTabIndex = tabCount - 1;
            stackLayout.currentIndex = currentTabIndex + 1; // +1 因为第一个是默认空页面

            // 加载数据到表格
            loadDataToTable(fileName, tableView);
        } catch (error) {
            console.error("创建标签页时出错:", error);
        }
    }
    
    function createTableView(parent, safeId) {
        console.log("创建表格视图，safeId:", safeId)

        try {
            // 从safeId获取原始文件名用于显示
            var displayName = ""
            for (var key in openedFiles) {
                if (key.replace(/[^a-zA-Z0-9_]/g, '_') === safeId) {
                    displayName = key
                    break
                }
            }

            // 使用Component方式创建表格容器
            var tableContainer = tableContainerComponent.createObject(parent, {
                "title": "参数表格 - " + displayName,
                "fileName": displayName
            })

            return tableContainer.listView;
        } catch (error) {
            console.error("创建表格视图时出错:", error);
            return null;
        }
    }

    function loadDataToTable(fileName, tableView) {
        console.log("加载数据到表格，文件名:", fileName)
        var fileData = openedFiles[fileName]
        if (!fileData) {
            console.error("找不到文件数据:", fileName)
            return
        }

        try {
            // 使用Component方式创建的表格，数据会自动绑定
            // 这里只需要确保数据已经存储在openedFiles中
            console.log("数据已准备完成，表格将自动更新")

            // 如果需要强制刷新，可以触发模型更新
            if (tableView && tableView.model) {
                // 触发模型更新
                var tempModel = tableView.model
                tableView.model = null
                tableView.model = tempModel
                console.log("表格模型已刷新")
            }
        } catch (error) {
            console.error("加载数据到表格时出错:", error)
        }
    }

    function closeTab(index) {
        if (index < 0 || tabCount <= 0) return

        // 获取要关闭的标签页信息
        var fileName = ""
        var tabIndex = 0

        // 遍历查找对应的标签按钮
        for (var key in openedFiles) {
            if (tabIndex === index) {
                fileName = key
                break
            }
            tabIndex++
        }

        if (!fileName) return

        // 获取当前标签页和内容
        var tabButton = null
        if (index < tabBar.count) {
            tabButton = tabBar.itemAt(index)
        }

        var tabContent = null
        if (index + 1 < stackLayout.count) { // +1 因为第一个是默认空页面
            tabContent = stackLayout.itemAt(index + 1)
        }

        // 销毁标签按钮和内容
        if (tabButton) {
            tabButton.destroy()
        }

        if (tabContent) {
            tabContent.destroy()
        }

        // 从已打开文件列表中移除
        delete openedFiles[fileName]

        // 减少标签计数
        tabCount--

        // 更新当前标签索引
        if (tabCount <= 0) {
            currentTabIndex = -1
        } else if (currentTabIndex >= tabCount) {
            currentTabIndex = tabCount - 1
        }
    }

    function filterCurrentTab() {
        var searchText = searchField.text.toLowerCase()
        if (currentTabIndex < 0) return

        // 获取当前标签页的文件名
        var fileName = ""
        var tabIndex = 0
        for (var key in openedFiles) {
            if (tabIndex === currentTabIndex) {
                fileName = key
                break
            }
            tabIndex++
        }

        if (!fileName) return

        var fileData = openedFiles[fileName]
        if (!fileData) return

        // 过滤数据
        if (searchText === "") {
            fileData.filteredData = fileData.parsedData
        } else {
            fileData.filteredData = fileData.parsedData.filter(function(item) {
                return (item.name && item.name.toLowerCase().indexOf(searchText) >= 0) ||
                       (item.value && item.value.toLowerCase().indexOf(searchText) >= 0) ||
                       (item.group && item.group.toLowerCase().indexOf(searchText) >= 0) ||
                       (item.description && item.description.toLowerCase().indexOf(searchText) >= 0)
            })
        }

        // 更新当前标签页的表格 - 数据绑定会自动更新
        console.log("过滤完成，数据已更新，表格将自动刷新")

        // 触发当前标签页的ListView刷新
        if (currentTabIndex >= 0 && currentTabIndex + 1 < stackLayout.count) {
            var currentTabContent = stackLayout.itemAt(currentTabIndex + 1)
            if (currentTabContent && currentTabContent.listView) {
                // 强制刷新ListView
                var tempModel = currentTabContent.listView.model
                currentTabContent.listView.model = null
                currentTabContent.listView.model = tempModel
            }
        }
    }

    function switchViewMode(isTableView) {
        console.log("切换视图模式:", isTableView ? "表格视图" : "XML视图")

        // 遍历所有打开的标签页，切换它们的视图模式
        for (var i = 1; i < stackLayout.count; i++) { // 从1开始，跳过默认空页面
            var tabContent = stackLayout.itemAt(i)
            if (tabContent && typeof tabContent.switchView === "function") {
                tabContent.switchView(isTableView)
            }
        }
    }

    function findTableView(parent) {
        // 递归查找表格视图
        if (parent.objectName && parent.objectName.indexOf("tableListView_") === 0) {
            return parent
        }

        for (var i = 0; i < parent.children.length; i++) {
            var result = findTableView(parent.children[i])
            if (result) return result
        }

        return null
    }

    function getSimulatedXmlContent(fileName) {
        return '<?xml version="1.0" standalone="no" encoding="gb2312" ?>\n' +
               '<!--\n' +
               '文件: 通信参数配置\n' +
               '\n' +
               '说明:\n' +
               '    1.  主站通讯参数    ---COMM\n' +
               '    2.  终端通讯参数    ---TERM\n' +
               '\n' +
               '-->\n' +
               '<AllParameter>\n' +
               '    <TERM_COMM>\n' +
               '        <ChgTime>1752244108</ChgTime>\n' +
               '        <TermZone>447320</TermZone>\n' +
               '        <TermAddr>4051</TermAddr>\n' +
               '        <WorkMode>0x1</WorkMode>\n' +
               '        <port>9001</port>\n' +
               '        <TermIP>0.0.0.0</TermIP>\n' +
               '        <SubnetMask>*************</SubnetMask>\n' +
               '        <GateWayIP>0.0.0.0</GateWayIP>\n' +
               '        <IPGetMode>1</IPGetMode>\n' +
               '    </TERM_COMM>\n' +
               '    <TERM_COMM_EXT>\n' +
               '        <ChgTime>1752244109</ChgTime>\n' +
               '        <RouteIP2>*************</RouteIP2>\n' +
               '        <port2>9002</port2>\n' +
               '        <TermIP2>*************24</TermIP2>\n' +
               '        <SubnetMask2>*************</SubnetMask2>\n' +
               '        <GateWayIP2>***************</GateWayIP2>\n' +
               '    </TERM_COMM_EXT>\n' +
               '    <COMM>\n' +
               '        <ChgTime>1752244108</ChgTime>\n' +
               '        <Main1IP>0.0.0.0</Main1IP>\n' +
               '        <Main1Port>0</Main1Port>\n' +
               '        <Main1PhoneNo>00000000000</Main1PhoneNo>\n' +
               '        <Main1ChannelType>2</Main1ChannelType>\n' +
               '        <Heartbeat>5</Heartbeat>\n' +
               '        <RedialInterval>10</RedialInterval>\n' +
               '        <RedialNum>0x63</RedialNum>\n' +
               '        <TCP-UDP>0x0</TCP-UDP>\n' +
               '    </COMM>\n' +
               '</AllParameter>'
    }

    function parseXmlContent(xmlContent) {
        // 简化的XML解析，实际应该使用更完善的XML解析器
        var parameters = []

        // 模拟解析结果 - 基于comm_param.xml的实际内容
        parameters.push({
            name: "ChgTime",
            value: "1752244108",
            group: "TERM_COMM",
            description: "终端通信参数修改时间戳"
        })
        parameters.push({
            name: "TermZone",
            value: "447320",
            group: "TERM_COMM",
            description: "终端区域码"
        })
        parameters.push({
            name: "TermAddr",
            value: "4051",
            group: "TERM_COMM",
            description: "终端地址"
        })
        parameters.push({
            name: "WorkMode",
            value: "0x1",
            group: "TERM_COMM",
            description: "工作模式"
        })
        parameters.push({
            name: "port",
            value: "9001",
            group: "TERM_COMM",
            description: "通信端口"
        })
        parameters.push({
            name: "TermIP",
            value: "0.0.0.0",
            group: "TERM_COMM",
            description: "终端IP地址"
        })
        parameters.push({
            name: "SubnetMask",
            value: "*************",
            group: "TERM_COMM",
            description: "子网掩码"
        })
        parameters.push({
            name: "GateWayIP",
            value: "0.0.0.0",
            group: "TERM_COMM",
            description: "网关IP地址"
        })
        parameters.push({
            name: "IPGetMode",
            value: "1",
            group: "TERM_COMM",
            description: "IP获取模式"
        })
        parameters.push({
            name: "RouteIP2",
            value: "*************",
            group: "TERM_COMM_EXT",
            description: "路由IP地址2"
        })
        parameters.push({
            name: "port2",
            value: "9002",
            group: "TERM_COMM_EXT",
            description: "通信端口2"
        })
        parameters.push({
            name: "TermIP2",
            value: "*************24",
            group: "TERM_COMM_EXT",
            description: "终端IP地址2"
        })
        parameters.push({
            name: "SubnetMask2",
            value: "*************",
            group: "TERM_COMM_EXT",
            description: "子网掩码2"
        })
        parameters.push({
            name: "GateWayIP2",
            value: "***************",
            group: "TERM_COMM_EXT",
            description: "网关IP地址2"
        })
        parameters.push({
            name: "Main1IP",
            value: "0.0.0.0",
            group: "COMM",
            description: "主站1 IP地址"
        })
        parameters.push({
            name: "Main1Port",
            value: "0",
            group: "COMM",
            description: "主站1端口"
        })
        parameters.push({
            name: "Main1PhoneNo",
            value: "00000000000",
            group: "COMM",
            description: "主站1电话号码"
        })
        parameters.push({
            name: "Main1ChannelType",
            value: "2",
            group: "COMM",
            description: "主站1通道类型"
        })
        parameters.push({
            name: "Heartbeat",
            value: "5",
            group: "COMM",
            description: "心跳间隔(分钟)"
        })
        parameters.push({
            name: "RedialInterval",
            value: "10",
            group: "COMM",
            description: "重拨间隔"
        })
        parameters.push({
            name: "RedialNum",
            value: "0x63",
            group: "COMM",
            description: "重拨次数"
        })
        parameters.push({
            name: "TCP-UDP",
            value: "0x0",
            group: "COMM",
            description: "TCP/UDP协议选择"
        })

        return parameters
    }

    // 表格容器组件 - 重新设计以支持视图切换和修复对齐问题
    Component {
        id: tableContainerComponent

        Item {
            Layout.fillWidth: true
            Layout.fillHeight: true

            property string fileName: ""
            property var listView: paramListView
            property bool isTableView: true

            // 视图切换逻辑
            function switchView(showTable) {
                isTableView = showTable
                tableContainer.visible = showTable
                xmlContainer.visible = !showTable
            }

            // 连接到父级的切换函数 - 移除这个连接，改为在switchViewMode函数中直接调用

            // 表格视图容器
            GroupBox {
                id: tableContainer
                anchors.fill: parent
                title: "参数表格 - " + fileName
                visible: isTableView

                // 自定义标题样式
                label: Rectangle {
                    width: titleText.width + 20
                    height: 25
                    color: "#f8f9fa"
                    border.color: "#dee2e6"
                    border.width: 1
                    radius: 3

                    Text {
                        id: titleText
                        anchors.centerIn: parent
                        text: "参数表格 - " + fileName
                        font.pixelSize: 12
                        font.bold: true
                        color: "#495057"
                    }
                }

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 5
                    spacing: 0

                    // 固定表头 - 使用固定宽度确保对齐
                    Rectangle {
                        Layout.fillWidth: true
                        height: 35
                        color: "#e9ecef"
                        border.color: "#dee2e6"
                        border.width: 1

                        Row {
                            anchors.fill: parent
                            spacing: 0

                            // 序号列
                            Rectangle {
                                width: 60
                                height: parent.height
                                color: "#f8f9fa"
                                border.color: "#dee2e6"
                                border.width: 1

                                Text {
                                    anchors.centerIn: parent
                                    text: "序号"
                                    font.bold: true
                                    font.pixelSize: 12
                                    color: "#495057"
                                }
                            }

                            // 参数组列
                            Rectangle {
                                width: 120
                                height: parent.height
                                color: "#f8f9fa"
                                border.color: "#dee2e6"
                                border.width: 1

                                Text {
                                    anchors.centerIn: parent
                                    text: "参数组"
                                    font.bold: true
                                    font.pixelSize: 12
                                    color: "#495057"
                                }
                            }

                            // 参数名列
                            Rectangle {
                                width: 150
                                height: parent.height
                                color: "#f8f9fa"
                                border.color: "#dee2e6"
                                border.width: 1

                                Text {
                                    anchors.centerIn: parent
                                    text: "参数名"
                                    font.bold: true
                                    font.pixelSize: 12
                                    color: "#495057"
                                }
                            }

                            // 参数值列
                            Rectangle {
                                width: 150
                                height: parent.height
                                color: "#f8f9fa"
                                border.color: "#dee2e6"
                                border.width: 1

                                Text {
                                    anchors.centerIn: parent
                                    text: "参数值"
                                    font.bold: true
                                    font.pixelSize: 12
                                    color: "#495057"
                                }
                            }

                            // 描述列 - 自适应宽度
                            Rectangle {
                                width: parent.width - 480 // 总宽度减去前面4列的固定宽度
                                height: parent.height
                                color: "#f8f9fa"
                                border.color: "#dee2e6"
                                border.width: 1

                                Text {
                                    anchors.centerIn: parent
                                    text: "描述"
                                    font.bold: true
                                    font.pixelSize: 12
                                    color: "#495057"
                                }
                            }
                        }
                    }

                    // 表格内容区域
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: "white"
                        border.color: "#dee2e6"
                        border.width: 1

                        ScrollView {
                            anchors.fill: parent
                            anchors.margins: 1
                            clip: true

                            ListView {
                                id: paramListView
                                anchors.fill: parent
                                model: openedFiles[fileName] ? openedFiles[fileName].filteredData || openedFiles[fileName].parsedData : []

                                delegate: Rectangle {
                                    width: ListView.view.width
                                    height: 32
                                    color: index % 2 === 0 ? "#ffffff" : "#f8f9fa"
                                    border.color: "#e9ecef"
                                    border.width: 0.5

                                    Row {
                                        anchors.fill: parent
                                        spacing: 0

                                        // 序号列 - 与表头对齐
                                        Rectangle {
                                            width: 60
                                            height: parent.height
                                            color: "transparent"
                                            border.color: "#e9ecef"
                                            border.width: 0.5

                                            Text {
                                                anchors.centerIn: parent
                                                text: index + 1
                                                font.pixelSize: 11
                                                color: "#6c757d"
                                            }
                                        }

                                        // 参数组列
                                        Rectangle {
                                            width: 120
                                            height: parent.height
                                            color: "transparent"
                                            border.color: "#e9ecef"
                                            border.width: 0.5

                                            Text {
                                                anchors.left: parent.left
                                                anchors.leftMargin: 8
                                                anchors.verticalCenter: parent.verticalCenter
                                                text: modelData.group || ""
                                                font.pixelSize: 11
                                                color: "#495057"
                                                elide: Text.ElideRight
                                                width: parent.width - 16
                                            }
                                        }

                                        // 参数名列
                                        Rectangle {
                                            width: 150
                                            height: parent.height
                                            color: "transparent"
                                            border.color: "#e9ecef"
                                            border.width: 0.5

                                            Text {
                                                anchors.left: parent.left
                                                anchors.leftMargin: 8
                                                anchors.verticalCenter: parent.verticalCenter
                                                text: modelData.name || ""
                                                font.pixelSize: 11
                                                font.bold: true
                                                color: "#212529"
                                                elide: Text.ElideRight
                                                width: parent.width - 16
                                            }
                                        }

                                        // 参数值列
                                        Rectangle {
                                            width: 150
                                            height: parent.height
                                            color: "transparent"
                                            border.color: "#e9ecef"
                                            border.width: 0.5

                                            Text {
                                                anchors.left: parent.left
                                                anchors.leftMargin: 8
                                                anchors.verticalCenter: parent.verticalCenter
                                                text: modelData.value || ""
                                                font.pixelSize: 11
                                                color: "#0d6efd"
                                                font.family: "Consolas, Monaco, monospace"
                                                elide: Text.ElideRight
                                                width: parent.width - 16
                                            }
                                        }

                                        // 描述列
                                        Rectangle {
                                            width: parent.width - 480
                                            height: parent.height
                                            color: "transparent"
                                            border.color: "#e9ecef"
                                            border.width: 0.5

                                            Text {
                                                anchors.left: parent.left
                                                anchors.leftMargin: 8
                                                anchors.right: parent.right
                                                anchors.rightMargin: 8
                                                anchors.verticalCenter: parent.verticalCenter
                                                text: modelData.description || ""
                                                font.pixelSize: 11
                                                color: "#6c757d"
                                                elide: Text.ElideRight
                                                wrapMode: Text.Wrap
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // XML视图容器
            GroupBox {
                id: xmlContainer
                anchors.fill: parent
                title: "XML原始内容 - " + fileName
                visible: !isTableView

                // 自定义标题样式
                label: Rectangle {
                    width: xmlTitleText.width + 20
                    height: 25
                    color: "#f8f9fa"
                    border.color: "#dee2e6"
                    border.width: 1
                    radius: 3

                    Text {
                        id: xmlTitleText
                        anchors.centerIn: parent
                        text: "XML原始内容 - " + fileName
                        font.pixelSize: 12
                        font.bold: true
                        color: "#495057"
                    }
                }

                ScrollView {
                    id: xmlScrollView
                    anchors.fill: parent
                    anchors.margins: 5
                    clip: true

                    TextArea {
                        id: xmlTextArea
                        readOnly: true
                        selectByMouse: true
                        font.family: "Consolas, Monaco, 'Courier New', monospace"
                        font.pixelSize: 11
                        wrapMode: TextArea.Wrap
                        text: openedFiles[fileName] ? openedFiles[fileName].xmlContent : ""
                        color: "#495057"

                        background: Rectangle {
                            color: "#f8f9fa"
                            border.color: "#dee2e6"
                            border.width: 1
                        }
                    }
                }
            }
        }
    }
}
