import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Rectangle {
    id: helpView
    color: "#f8f9fa"
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 20
        
        ColumnLayout {
            width: helpView.width - 40
            spacing: 20
            
            // 标题
            Text {
                text: "南网工具集 - 使用帮助"
                font.pixelSize: 28
                font.bold: true
                color: "#2c3e50"
                Layout.alignment: Qt.AlignHCenter
                Layout.bottomMargin: 20
            }
            
            // 工具概述
            Rectangle {
                Layout.fillWidth: true
                height: overviewColumn.height + 30
                color: "white"
                radius: 8
                border.color: "#e9ecef"
                border.width: 1
                
                ColumnLayout {
                    id: overviewColumn
                    anchors.left: parent.left
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.margins: 15
                    spacing: 10
                    
                    Text {
                        text: "工具概述"
                        font.pixelSize: 20
                        font.bold: true
                        color: "#495057"
                    }
                    
                    Text {
                        text: "南网工具集是一个专为南方电网设计的综合性工具软件，主要用于处理和分析终端设备的日志数据、参数配置和协议解析。"
                        font.pixelSize: 14
                        color: "#6c757d"
                        wrapMode: Text.WordWrap
                        Layout.fillWidth: true
                    }
                }
            }
            
            // 使用说明
            Rectangle {
                Layout.fillWidth: true
                height: usageColumn.height + 30
                color: "white"
                radius: 8
                border.color: "#e9ecef"
                border.width: 1
                
                ColumnLayout {
                    id: usageColumn
                    anchors.left: parent.left
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.margins: 15
                    spacing: 15
                    
                    Text {
                        text: "使用说明"
                        font.pixelSize: 20
                        font.bold: true
                        color: "#495057"
                    }
                    
                    Text {
                        text: "1. 首先在首页选择终端日志压缩包或已解压的目录"
                        font.pixelSize: 14
                        color: "#495057"
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: "2. 系统会自动解压并识别termdisk目录"
                        font.pixelSize: 14
                        color: "#495057"
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: "3. 解压成功后，左侧功能模块会被激活"
                        font.pixelSize: 14
                        color: "#495057"
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: "4. 点击相应的功能模块开始使用对应的工具"
                        font.pixelSize: 14
                        color: "#495057"
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: "5. 协议解析功能可以独立使用，无需先解压文件"
                        font.pixelSize: 14
                        color: "#495057"
                        Layout.fillWidth: true
                    }
                    Text {
                        text: "重要：自定义日志合并脚本路径：/tools/custom_rules；
    自定义数据表解析规则文件：/config/ParseRules.xml
    报文协议解析数据项定义文件：/config/dlt645_2007_config.xml"
                        font.pixelSize: 14
                        color: "#ee4444"
                        Layout.fillWidth: true
                    }
                    Text {
                        text: "重要：请大家多多更新以上文件，互通！！！！！！"
                        font.pixelSize: 14
                        color: "#ee4444"
                        Layout.fillWidth: true
                    }
                }
            }
            
            // 技术支持
            Rectangle {
                Layout.fillWidth: true
                height: supportColumn.height + 30
                color: "white"
                radius: 8
                border.color: "#e9ecef"
                border.width: 1
                
                ColumnLayout {
                    id: supportColumn
                    anchors.left: parent.left
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.margins: 15
                    spacing: 10
                    
                    Text {
                        text: "技术支持"
                        font.pixelSize: 20
                        font.bold: true
                        color: "#495057"
                    }
                    
                    Text {
                        text: "如果在使用过程中遇到问题，请联系技术支持团队。"
                        font.pixelSize: 14
                        color: "#6c757d"
                        Layout.fillWidth: true
                    }
                    
                    Text {
                        text: "版本信息：南网工具集 v1.0"
                        font.pixelSize: 14
                        color: "#6c757d"
                        Layout.fillWidth: true
                    }
                }
            }
        }
    }
}
