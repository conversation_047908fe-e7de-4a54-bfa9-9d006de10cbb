#ifndef MESSAGEPARSER_H
#define MESSAGEPARSER_H

#include <QObject>
#include <QString>
#include <QByteArray>
#include <QStringList>
#include <QVariantMap>
#include <QWidget>
#include <memory>

#include "protocoltypes.h"

class ProtocolDetector;
class DLT645Parser;
class NWUPParser;

/**
 * @brief 报文解析引擎核心类
 * 
 * 功能：
 * 1. 对输入的报文进行按16进制字节进行划分
 * 2. 对报文进行帧结构的解析，判断帧协议类型
 * 3. 根据帧协议类型选择对应的协议解析器
 */
class MessageParser : public QObject
{
    Q_OBJECT

public:
    explicit MessageParser(QObject *parent = nullptr);
    ~MessageParser();

    /**
     * @brief 解析报文的主入口函数
     * @param rawMessage 原始报文字符串（可包含空格、0x前缀等）
     * @return 协议解析结果
     */
    Q_INVOKABLE ProtocolParseResult parseMessage(const QString &rawMessage);

    /**
     * @brief 解析报文的主入口函数（QML友好版本）
     * @param rawMessage 原始报文字符串（可包含空格、0x前缀等）
     * @return 协议解析结果的QVariantMap
     */
    Q_INVOKABLE QVariantMap parseMessageForQML(const QString &rawMessage);

    /**
     * @brief 将输入报文按16进制字节进行划分
     * @param rawMessage 原始报文字符串
     * @return 字节划分结果
     */
    Q_INVOKABLE ByteSegmentResult segmentBytes(const QString &rawMessage);

    /**
     * @brief 解析帧结构并判断协议类型
     * @param byteArray 字节数组
     * @return 帧解析结果
     */
    Q_INVOKABLE FrameParseResult parseFrame(const QByteArray &byteArray);

    /**
     * @brief 根据协议类型选择解析器进行详细解析
     * @param frameResult 帧解析结果
     * @return 协议解析结果
     */
    Q_INVOKABLE ProtocolParseResult parseProtocol(const FrameParseResult &frameResult);

    /**
     * @brief 设置是否启用详细日志
     * @param enabled 是否启用
     */
    Q_INVOKABLE void setVerboseLogging(bool enabled);

    /**
     * @brief 获取支持的协议类型列表
     * @return 协议类型字符串列表
     */
    Q_INVOKABLE QStringList getSupportedProtocols() const;

    /**
     * @brief 启动外部工具
     * @param toolPath 工具相对路径（相对于应用程序目录）
     * @return 启动结果，包含success字段和可能的error信息
     */
    Q_INVOKABLE QVariantMap startExternalTool(const QString &toolPath);

    /**
     * @brief 手动指定协议类型进行解析
     * @param rawMessage 原始报文
     * @param protocolType 指定的协议类型
     * @return 协议解析结果
     */
    Q_INVOKABLE ProtocolParseResult parseWithProtocol(const QString &rawMessage,
                                                      const QString &protocolType);

    /**
     * @brief 手动指定协议类型进行解析（QML友好版本）
     * @param rawMessage 原始报文
     * @param protocolType 指定的协议类型
     * @return 协议解析结果的QVariantMap
     */
    Q_INVOKABLE QVariantMap parseWithProtocolForQML(const QString &rawMessage,
                                                    const QString &protocolType);



    /**
     * @brief 创建协议解析器界面组件
     * @return 界面组件指针
     */
    Q_INVOKABLE QWidget* createProtocolParserWidget();

signals:
    /**
     * @brief 解析日志信号
     * @param level 日志级别(Debug/Info/Warning/Error)
     * @param message 日志消息
     */
    void parseLog(const QString &level, const QString &message);

private:
    /**
     * @brief 清理输入字符串，移除空格、前缀等
     * @param input 输入字符串
     * @return 清理后的十六进制字符串
     */
    QString cleanHexString(const QString &input);

    /**
     * @brief 验证十六进制字符串的有效性
     * @param hexString 十六进制字符串
     * @return 是否有效
     */
    bool isValidHexString(const QString &hexString);

    /**
     * @brief 将十六进制字符串转换为字节数组
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    QByteArray hexStringToByteArray(const QString &hexString);

    /**
     * @brief 将字节数组转换为十六进制字符串列表
     * @param byteArray 字节数组
     * @return 十六进制字符串列表
     */
    QStringList byteArrayToHexList(const QByteArray &byteArray);

    /**
     * @brief 发送日志信号
     * @param level 日志级别
     * @param message 日志消息
     */
    void emitLog(const QString &level, const QString &message);

private:
    std::unique_ptr<ProtocolDetector> m_detector;    // 协议检测器
    std::unique_ptr<DLT645Parser> m_dlt645Parser;    // DL/T645解析器
    std::unique_ptr<NWUPParser> m_nwupParser;        // 南网上行协议解析器
    bool m_verboseLogging;                           // 是否启用详细日志
    QString m_configFilePath;                        // 配置文件路径
};

#endif // MESSAGEPARSER_H
