#ifndef DLT645_1997_PARSER_H
#define DLT645_1997_PARSER_H

#include <QObject>
#include <QByteArray>
#include <QString>
#include <QVariantMap>
#include <QDateTime>

#include "protocoltypes.h"
#include "dataidentifierconfig.h"

/**
 * @brief DL/T645-1997协议解析器
 * 
 * 专门用于解析DL/T645-1997电能表通信协议报文
 */
class DLT645_1997_Parser : public QObject
{
    Q_OBJECT

public:
    explicit DLT645_1997_Parser(QObject *parent = nullptr);
    ~DLT645_1997_Parser();

    /**
     * @brief 解析DL/T645-1997协议报文
     * @param frameData 帧数据
     * @return 协议解析结果
     */
    ProtocolParseResult parseFrame(const QByteArray &frameData);

    /**
     * @brief 解析地址域
     * @param addressData 地址域数据（6字节）
     * @return 地址字符串
     */
    QString parseAddress(const QByteArray &addressData);

    /**
     * @brief 解析控制码
     * @param controlCode 控制码
     * @return 控制码信息
     */
    QVariantMap parseControlCode(quint8 controlCode);

    /**
     * @brief 解析数据域
     * @param dataField 数据域（已减33H处理）
     * @param controlCode 控制码
     * @return 数据域解析结果
     */
    QVariantMap parseDataField(const QByteArray &dataField, quint8 controlCode);

    /**
     * @brief 解析数据标识
     * @param dataId 数据标识（2字节，DL/T645-1997使用2字节数据标识）
     * @return 数据标识信息
     */
    QVariantMap parseDataIdentifier(const QByteArray &dataId);

private:
    // 数据标识配置管理器（单例引用）
    DataIdentifierConfig* m_dataConfig;

    /**
     * @brief 初始化映射表
     */
    void initializeMappings();

    /**
     * @brief 创建成功结果
     */
    ProtocolParseResult createSuccessResult(const QVariantMap &data, const QString &summary, const QString &detail);

    /**
     * @brief 创建错误结果
     */
    ProtocolParseResult createErrorResult(const QString &error);

    /**
     * @brief 格式化字节数组为十六进制字符串
     */
    QString formatHexString(const QByteArray &data, const QString &separator = " ");
};

#endif // DLT645_1997_PARSER_H
