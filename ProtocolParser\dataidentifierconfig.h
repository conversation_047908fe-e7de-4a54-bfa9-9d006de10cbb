#ifndef DATAIDENTIFIERCONFIG_H
#define DATAIDENTIFIERCONFIG_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QVariantMap>
#include <QJsonObject>
#include <QJsonDocument>
#include <QXmlStreamReader>
#include <QStringList>
#include <QPair>
#include <functional>
#include "protocoltypes.h"

/**
 * @brief 数据块中的项目信息
 */
struct BlockItemInfo {
    int order;                  // 在数据块中的顺序（1, 2, 3...）
    quint32 dataId;             // 对应的单独数据项ID
};

/**
 * @brief 字段信息结构（用于复合数据格式）
 */
struct FieldInfo {
    QString name;               // 字段名称
    QString format;             // 字段格式
    int length;                 // 字段长度（字节）
    QString unit;               // 字段单位
    QString description;        // 字段描述
    int offset;                 // 字段偏移量
};

/**
 * @brief DI名称规则结构
 */
struct DINameRule {
    QString code;               // DI代码或范围，如 "00" 或 "01-3F"
    QString value;              // 直接值（用于精确匹配）
    QString pattern;            // 模式字符串（用于范围匹配）
    QString conversion;         // 转换函数名称
};

/**
 * @brief 条件格式结构
 */
struct ConditionalFormat {
    QString condition;          // 条件，如 "DI1=00-0F"
    QList<FieldInfo> fields;    // 字段列表
    int total_length;           // 总长度
};

/**
 * @brief 数据项模式结构
 */
struct ItemPattern {
    QString base_id;            // 基础ID
    QString DI0_range;          // DI0范围
    QString DI1_range;          // DI1范围
    QString DI2_range;          // DI2范围
    int item_length;            // 单个数据项长度
};

/**
 * @brief 模板数据项结构
 */
struct TemplateDataItem {
    QString base_id;            // 基础ID
    QString base_name;          // 基础名称
    QString base_description;   // 基础描述
    QString format;             // 格式
    int length;                 // 长度
    QString unit;               // 单位
    QString encoding;           // 编码

    // DI范围
    QString DI0_range;
    QString DI1_range;
    QString DI2_range;

    // DI名称规则
    QList<DINameRule> DI0_names;
    QList<DINameRule> DI1_names;
    QList<DINameRule> DI2_names;

    // 名称规则
    QString name_rule;
    QString description_rule;

    // 格式类型
    bool is_composite;          // 是否为复合格式
    bool is_conditional;        // 是否为条件格式
    bool is_block;              // 是否为数据块
    bool is_variable;           // 是否为可变长度

    // 复合格式信息
    QList<FieldInfo> composite_fields;
    int total_length;

    // 条件格式信息
    QList<ConditionalFormat> conditional_formats;

    // 数据块信息
    QString auto_calculate_base;  // 自动计算长度的基础项
};

/**
 * @brief 数据项配置结构
 */
struct DataItemConfig {
    QString id;                 // 数据项ID
    QString name;               // 数据项名称
    QString format;             // 数据格式
    int length;                 // 数据长度（字节）
    QString unit;               // 单位
    QString encoding;           // 编码格式（BCD/ASCII）
    QString access;             // 访问权限
    QString description;        // 描述
    bool isBlock;               // 是否为数据块
    bool isComplex;             // 是否为复合数据格式
    bool isVariable;            // 是否为可变长度
    QList<BlockItemInfo> blockItems;  // 数据块包含的有序项目列表
    QList<FieldInfo> fields;    // 复合数据包含的字段列表
};

/**
 * @brief 数据子类别配置结构
 */
struct DataSubcategoryConfig {
    QString id;                 // 子类别ID
    QString name;               // 子类别名称
    QString description;        // 描述
    QMap<QString, DataItemConfig> dataItems;  // 数据项映射
};

/**
 * @brief 数据类别配置结构
 */
struct DataCategoryConfig {
    QString id;                 // 类别ID
    QString name;               // 类别名称
    QString description;        // 描述
    QMap<QString, DataSubcategoryConfig> subcategories;  // 子类别映射
};


/**
 * @brief 单个协议的配置数据结构
 */
struct ConfigData {
    QString protocol;                                    // 协议名称
    QString version;                                     // 配置版本
    QString description;                                 // 配置描述
    QMap<QString, DataCategoryConfig> categories;       // 数据类别映射
    QMap<quint32, DataItemConfig> dataItemCache;        // 数据项缓存
    QMap<QString, TemplateDataItem> templateItems;      // 模板数据项缓存
};

/**
 * @brief 数据标识配置加载器（单例模式）
 *
 * 负责加载和管理协议的数据标识配置
 */
class DataIdentifierConfig : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return DataIdentifierConfig单例实例
     */
    static DataIdentifierConfig* instance();

    /**
     * @brief 销毁单例实例
     */
    static void destroyInstance();

private:
    explicit DataIdentifierConfig(QObject *parent = nullptr);
    ~DataIdentifierConfig();

    // 禁用拷贝构造和赋值操作
    DataIdentifierConfig(const DataIdentifierConfig&) = delete;
    DataIdentifierConfig& operator=(const DataIdentifierConfig&) = delete;

public:

    /**
     * @brief 从XML文件加载指定协议的配置
     * @param filePath XML配置文件路径
     * @param protocolType 协议类型
     * @return 是否加载成功
     */
    bool loadFromXml(const QString &filePath, ProtocolType protocolType);

    /**
     * @brief 根据数据标识和协议类型获取数据项配置
     * @param dataIdentifier 4字节数据标识（如：02010100）
     * @param protocolType 协议类型
     * @return 数据项配置，如果未找到则返回空配置
     */
    DataItemConfig getDataItemConfig(quint32 dataIdentifier, ProtocolType protocolType) const;

    /**
     * @brief 根据数据标识和协议类型获取数据项名称
     * @param dataIdentifier 4字节数据标识
     * @param protocolType 协议类型
     * @return 数据项名称
     */
    QString getDataItemName(quint32 dataIdentifier, ProtocolType protocolType) const;

    /**
     * @brief 根据数据标识和协议类型获取数据项描述
     * @param dataIdentifier 4字节数据标识
     * @param protocolType 协议类型
     * @return 数据项描述
     */
    QString getDataItemDescription(quint32 dataIdentifier, ProtocolType protocolType) const;

    /**
     * @brief 根据数据标识和协议类型获取编码格式
     * @param dataIdentifier 4字节数据标识
     * @param protocolType 协议类型
     * @return 编码格式（BCD/ASCII）
     */
    QString getDataEncoding(quint32 dataIdentifier, ProtocolType protocolType) const;

    /**
     * @brief 判断数据标识是否为数据块
     * @param dataIdentifier 4字节数据标识
     * @param protocolType 协议类型
     * @return 是否为数据块
     */
    bool isDataBlock(quint32 dataIdentifier, ProtocolType protocolType) const;

    /**
     * @brief 获取数据块包含的数据项列表
     * @param dataIdentifier 数据块标识
     * @param protocolType 协议类型
     * @return 包含的数据项信息列表
     */
    QList<BlockItemInfo> getBlockItems(quint32 dataIdentifier, ProtocolType protocolType) const;

    /**
     * @brief 获取指定协议的所有支持的数据标识
     * @param protocolType 协议类型
     * @return 数据标识列表
     */
    QList<quint32> getAllDataIdentifiers(ProtocolType protocolType) const;

    /**
     * @brief 获取指定类别的所有数据标识
     * @param categoryId 类别ID
     * @param protocolType 协议类型
     * @return 数据标识列表
     */
    QList<quint32> getDataIdentifiersByCategory(const QString &categoryId, ProtocolType protocolType) const;


private:
    /**
     * @brief 解析XML配置（指定协议类型）
     * @param xmlReader XML读取器
     * @param protocolType 协议类型
     * @return 是否解析成功
     */
    bool parseXmlConfig(QXmlStreamReader &xmlReader, ProtocolType protocolType);

    /**
     * @brief 解析数据类别（指定协议类型）
     * @param xmlReader XML读取器
     * @param protocolType 协议类型
     * @return 是否解析成功
     */
    bool parseDataCategories(QXmlStreamReader &xmlReader, ProtocolType protocolType);

    /**
     * @brief 解析数据子类别
     * @param xmlReader XML读取器
     * @param category 数据类别配置
     * @return 是否解析成功
     */
    bool parseSubcategories(QXmlStreamReader &xmlReader, DataCategoryConfig &category);

    /**
     * @brief 解析单独数据项（指定协议类型）
     * @param xmlReader XML读取器
     * @param protocolType 协议类型
     * @return 是否解析成功
     */
    bool parseSingleDataItem(QXmlStreamReader &xmlReader, ProtocolType protocolType);

    /**
     * @brief 解析数据块（指定协议类型）
     * @param xmlReader XML读取器
     * @param protocolType 协议类型
     * @return 是否解析成功
     */
    bool parseDataBlock(QXmlStreamReader &xmlReader, ProtocolType protocolType);

    /**
     * @brief 解析数据块项目
     * @param xmlReader XML读取器
     * @param blockItem 数据块配置
     * @return 是否解析成功
     */
    bool parseBlockItems(QXmlStreamReader &xmlReader, DataItemConfig &blockItem);

    /**
     * @brief 解析复合数据项包含的字段
     * @param xmlReader XML读取器
     * @param complexItem 复合数据项配置
     * @return 是否解析成功
     */
    bool parseFieldItems(QXmlStreamReader &xmlReader, DataItemConfig &complexItem);

    /**
     * @brief 解析模板数据项
     * @param xmlReader XML读取器
     * @param protocolType 协议类型
     * @return 是否解析成功
     */
    bool parseTemplateDataItem(QXmlStreamReader &xmlReader, ProtocolType protocolType);

    /**
     * @brief 解析模板数据块
     * @param xmlReader XML读取器
     * @param protocolType 协议类型
     * @return 是否解析成功
     */
    bool parseTemplateDataBlock(QXmlStreamReader &xmlReader, ProtocolType protocolType);

    /**
     * @brief 解析DI名称规则
     * @param xmlReader XML读取器
     * @param elementName 元素名称（如 "DI1_names"）
     * @return DI名称规则列表
     */
    QList<DINameRule> parseDINameRules(QXmlStreamReader &xmlReader, const QString &elementName);

    /**
     * @brief 解析复合格式
     * @param xmlReader XML读取器
     * @return 字段列表和总长度
     */
    QPair<QList<FieldInfo>, int> parseCompositeFormat(QXmlStreamReader &xmlReader);

    /**
     * @brief 解析条件格式
     * @param xmlReader XML读取器
     * @return 条件格式列表
     */
    QList<ConditionalFormat> parseConditionalFormat(QXmlStreamReader &xmlReader);

    /**
     * @brief 生成模板数据项的所有变体
     * @param templateItem 模板数据项
     * @param protocolType 协议类型
     */
    void generateTemplateVariants(const TemplateDataItem &templateItem, ProtocolType protocolType);

    /**
     * @brief 展开DI范围
     * @param range 范围字符串，如 "00-3F" 或 "01,03,05"
     * @param defaultValue 默认值（当range为空时）
     * @return DI值列表
     */
    QStringList expandDIRange(const QString &range, const QString &defaultValue = "");

    /**
     * @brief 获取DI对应的名称
     * @param rules DI名称规则列表
     * @param diCode DI代码
     * @return 对应的名称
     */
    QString getDIName(const QList<DINameRule> &rules, const QString &diCode);

    /**
     * @brief 应用转换函数
     * @param pattern 模式字符串
     * @param diCode DI代码
     * @param conversion 转换函数名称
     * @return 转换后的字符串
     */
    QString applyConversion(const QString &pattern, const QString &diCode, const QString &conversion);

    /**
     * @brief 应用名称规则
     * @param rule 名称规则
     * @param di0Name DI0名称
     * @param di1Name DI1名称
     * @param di2Name DI2名称
     * @return 生成的名称
     */
    QString applyNameRule(const QString &rule, const QString &di0Name, const QString &di1Name, const QString &di2Name);

    /**
     * @brief 生成数据项ID
     * @param baseId 基础ID
     * @param di0 DI0值
     * @param di1 DI1值
     * @param di2 DI2值
     * @return 生成的ID
     */
    QString generateDataItemId(const QString &baseId, const QString &di0, const QString &di1, const QString &di2);

    /**
     * @brief 检查DI规则是否匹配
     * @param rule DI名称规则
     * @param diCode DI代码
     * @return 是否匹配
     */
    bool matchesDIRule(const DINameRule &rule, const QString &diCode);

    /**
     * @brief 检查条件是否匹配
     * @param condition 条件字符串
     * @param diValue DI值
     * @return 是否匹配
     */
    bool matchesCondition(const QString &condition, const QString &diValue);

    /**
     * @brief 解析包含项规则
     * @param xmlReader XML读取器
     * @return 数据项模式列表
     */
    QList<ItemPattern> parseIncludeItems(QXmlStreamReader &xmlReader);

    /**
     * @brief 生成数据块的项目列表
     * @param blockItem 数据块配置
     * @param patterns 数据项模式列表
     * @param autoCalcBase 自动计算长度的基础项
     * @param protocolType 协议类型
     */
    void generateBlockItems(DataItemConfig &blockItem, const QList<ItemPattern> &patterns, const QString &autoCalcBase, ProtocolType protocolType);

    /**
     * @brief 解析带有DI范围的数据块
     */
    bool parseDataBlockWithDIRange(QXmlStreamReader &xmlReader, ProtocolType protocolType,
                                  const QString &baseBlockId, const QString &baseBlockName,
                                  const QString &baseBlockDescription, const QString &format,
                                  const QString &encoding, const QString &di0Range,
                                  const QString &di1Range, const QString &di2Range);

    /**
     * @brief 根据DI值生成单个数据块
     */
    void generateSingleDataBlockFromDI(const QString &baseBlockId, const QString &baseBlockName,
                                      const QString &baseBlockDescription, const QString &format,
                                      const QString &encoding, const QList<ItemPattern> &itemPatterns,
                                      const QString &autoCalcBase, const QString &di0, const QString &di1,
                                      const QString &di2, ProtocolType protocolType,
                                      const QList<DINameRule> &blockDI0Names = QList<DINameRule>(),
                                      const QList<DINameRule> &blockDI1Names = QList<DINameRule>(),
                                      const QList<DINameRule> &blockDI2Names = QList<DINameRule>(),
                                      const QString &blockNameRule = QString(),
                                      const QString &blockDescriptionRule = QString());

    /**
     * @brief 根据DI值更新数据块名称
     */
    QString updateBlockNameWithDI(const QString &baseName, const QString &diValue, const QString &diType);

    /**
     * @brief 查找模板数据项
     * @param baseId 基础ID
     * @param protocolType 协议类型
     * @return 模板数据项
     */
    TemplateDataItem findTemplateDataItem(const QString &baseId, ProtocolType protocolType);

    /**
     * @brief 解析数据标识
     * @param dataIdentifier 数据标识
     * @param categoryId 类别ID（输出）
     * @param subcategoryId 子类别ID（输出）
     * @param itemId 数据项ID（输出）
     */
    void parseDataIdentifier(quint32 dataIdentifier,
                           QString &categoryId,
                           QString &subcategoryId,
                           QString &itemId) const;

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const QString &error);

public:
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    QString getLastError() const;

private:
    static DataIdentifierConfig* s_instance;              // 单例实例指针

    // 多协议配置支持
    QMap<ProtocolType, ConfigData> m_protocolConfigs;    // 协议配置映射
    QString m_lastError;                                  // 最后的错误信息

    // 转换函数映射
    QMap<QString, std::function<QString(const QString&)>> m_conversionFunctions;
};

// 注册元类型
Q_DECLARE_METATYPE(DataItemConfig)
Q_DECLARE_METATYPE(DataSubcategoryConfig)
Q_DECLARE_METATYPE(DataCategoryConfig)

#endif // DATAIDENTIFIERCONFIG_H
