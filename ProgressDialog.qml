import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Dialog {
    id: progressDialog
    title: "日志处理中"
    modal: false
    closePolicy: Popup.NoClose
    
    property string message: ""
    
    background: Rectangle {
        color: "#ffffff"
        border.color: "#dddddd"
        border.width: 1
        radius: 5
    }
    
    header: Rectangle {
        color: "#0078d7"
        height: 40
        radius: 5
        
        Label {
            text: "日志处理中"
            color: "white"
            font.bold: true
            font.pixelSize: 14
            anchors.verticalCenter: parent.verticalCenter
            anchors.left: parent.left
            anchors.leftMargin: 15
        }
    }
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 20
        
        RowLayout {
            Layout.fillWidth: true
            spacing: 15
            
            BusyIndicator {
                running: true
                Layout.alignment: Qt.AlignVCenter
            }
            
            Label {
                text: progressDialog.message
                Layout.fillWidth: true
                wrapMode: Text.Wrap
                font.bold: true
                font.pixelSize: 14
            }
        }
        
        Rectangle {
            Layout.fillWidth: true
            height: 1
            color: "#eeeeee"
        }
        
        Label {
            text: "日志处理正在后台进行，您可以继续使用其他功能"
            Layout.fillWidth: true
            horizontalAlignment: Text.AlignHCenter
            font.italic: true
            color: "#0078d7"
        }
        
        Label {
            text: "处理完成后将自动通知您并刷新日志列表"
            Layout.fillWidth: true
            horizontalAlignment: Text.AlignHCenter
            font.italic: true
        }
        
        Button {
            text: "在后台继续处理"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 150
            
            background: Rectangle {
                color: parent.down ? "#005fa3" : (parent.hovered ? "#0078d7" : "#0086f0")
                radius: 4
            }
            
            contentItem: Text {
                text: parent.text
                color: "white"
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
            
            onClicked: {
                progressDialog.close()
            }
        }
    }
    
    onClosed: {
        destroy()
    }
} 