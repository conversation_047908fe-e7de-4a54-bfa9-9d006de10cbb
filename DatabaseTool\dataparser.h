#ifndef DATAPARSER_H
#define DATAPARSER_H

#include <QObject>
#include <QVariant>
#include <QDateTime>
#include <QByteArray>
#include <QString>

class DataParser : public QObject
{
    Q_OBJECT

public:
    explicit DataParser(QObject *parent = nullptr);
    ~DataParser();

    // Parse a hex string to various data types using big-endian format
    Q_INVOKABLE QVariant parseHexData(const QString &hexString, const QString &parseType);

    // Batch parse multiple values from a hex string using a rule string
    Q_INVOKABLE QString batchParseHexData(const QString &hexString, const QString &ruleString);

    // Helper methods for specific data types (all using big-endian)
    static quint8 parseU8(const QByteArray &data, int offset = 0);
    static qint8 parseS8(const QByteArray &data, int offset = 0);
    static quint16 parseU16(const QByteArray &data, int offset = 0);
    static qint16 parseS16(const QByteArray &data, int offset = 0);
    static quint32 parseU32(const QByteArray &data, int offset = 0);
    static qint32 parseS32(const QByteArray &data, int offset = 0);
    static float parseFloat(const QByteArray &data, int offset = 0);
    static QDateTime parseTime(const QByteArray &data, int offset = 0);
    static quint64 parseU64(const QByteArray &data, int offset = 0);
    static qint64 parseS64(const QByteArray &data, int offset = 0);
    static double parseDouble(const QByteArray &data, int offset = 0);
    
    // Reverse byte order parsing methods (still big-endian interpretation)
    static quint16 parseU16Rev(const QByteArray &data, int offset = 0);
    static qint16 parseS16Rev(const QByteArray &data, int offset = 0);
    static float parseFloatRev(const QByteArray &data, int offset = 0);
    static QDateTime parseTimeRev(const QByteArray &data, int offset = 0);
    static double parseDoubleRev(const QByteArray &data, int offset = 0);
    static qint64 parseS64Rev(const QByteArray &data, int offset = 0);

private:
    // Helper method to convert hex string to byte array
    static QByteArray hexStringToByteArray(const QString &hexString);
};

#endif // DATAPARSER_H 