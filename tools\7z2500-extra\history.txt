7-Zip Extra history
-------------------

This file contains only information about changes related to that package exclusively.
The full history of changes is listed in history.txt in main 7-Zip program.

25.00          2025-07-05
-------------------------
- 7-Z<PERSON> for Windows can now use more than 64 CPU threads for compression
  to zip/7z/xz archives and for the 7-Zip benchmark.
  If there are more than one processor group in Windows (on systems with more than
  64 cpu threads), 7-<PERSON><PERSON> distributes running CPU threads across different processor groups.
- bzip2 compression speed was increased by 15-40%.
- deflate (zip/gz) compression speed was increased by 1-3%.
- improved support for zip, cpio and fat archives.
- fixed some bugs and vulnerabilities.


24.09          2024-11-29
-------------------------
- The default dictionary size values for LZMA/LZMA2 compression methods were increased:
         dictionary size   compression level
  v24.08  v24.09  v24.09   
          32-bit  64-bit    
    8 MB   16 MB   16 MB   -mx4
   16 MB   32 MB   32 MB   -mx5 : Normal
   32 MB   64 MB   64 MB   -mx6
   32 MB   64 MB  128 MB   -mx7 : Maximum
   64 MB   64 MB  256 MB   -mx8
   64 MB   64 MB  256 MB   -mx9 : Ultra
  The default dictionary size values for 32-bit versions of LZMA/LZMA2 don't exceed 64 MB.
- If an archive update operation uses a temporary archive folder and 
  the archive is moved to the destination folder, 7-Zip shows the progress of moving 
  the archive file, as this operation can take a long time if the archive is large.
- Some bugs were fixed.


24.08          2024-08-11
-------------------------
- The bug in 7-Zip 24.00-24.07 was fixed:
  For creating a zip archive: 7-Zip could write extra zero bytes after the end of the archive,
  if a file included to archive cannot be compressed to a size smaller than original.
  The created zip archive is correct except for the useless zero bytes after the end of the archive.
  When unpacking such a zip archive, 7-Zip displays a warning:
    "WARNING: There are data after the end of archive".


24.07          2024-06-19
-------------------------
- The bug was fixed: 7-Zip could crash for some incorrect ZSTD archives.


24.06          2024-05-26
-------------------------
- The bug was fixed: 7-Zip could not unpack some ZSTD archives.


24.05          2024-05-14
-------------------------
- New switch -myv={MMNN} to set decoder compatibility version for 7z archive creating.
  {MMNN} is 4-digit number that represents the version of 7-Zip without a dot.
  If -myv={MMNN} switch is specified, 7-Zip will only use compression methods that can 
  be decoded by the specified version {MMNN} of 7-Zip and newer versions.
  If -myv={MMNN} switch is not specified, -myv=2300 is used, and 7-Zip will only
  use compression methods that can be decoded by 7-Zip 23.00 and newer versions.
- New switch -myfa={FilterID} to    allow 7-Zip to use the specified filter method for 7z archive creating.
- New switch -myfd={FilterID} to disallow 7-Zip to use the specified filter method for 7z archive creating.


24.03          2024-03-23
-------------------------
- 7-Zip now can use new RISCV filter for compression to 7z and xz archives.
  RISCV filter can increase compression ratio for data containing executable
  files compiled for RISC-V architecture.
- The speed for LZMA and LZMA2 decompression in ARM64 version for Windows
  was increased by 20%-60%.
- new switch -smemx{size}g : to set allowed memory usage limit for RAR archive unpacking.
  RAR archives can use dictionary up 64 GB. Default allowed limit for RAR unpacking is 4 GB.
- -slmu switch : to show timestamps as UTC instead of LOCAL TIME.
- -slsl switch : in console 7-Zip for Windows : to show file paths with 
  linux path separator slash '/' instead of backslash separator '\'.
- 7-Zip supports .sha256 files that use backslash path separator '\'.
- Some bugs were fixed.


24.01          2024-01-31
-------------------------
- 7-Zip now can unpack ZSTD archives (.zst filename extension).
- 7-Zip now can unpack ZIP archives that use ZSTD compression method.
- 7-Zip now supports fast hash algorithm XXH64 that is used in ZSTD.
- Speed optimizations for archive unpacking: zip, gz, cab.
- Speed optimizations for hash caclulation: CRC-32, CRC-64.
- arm64 binaries are included to 7-Zip Extra package.
- The bug was fixed: 7-Zip for Linux could fail for multivolume creation in some cases.
- Some bugs were fixed.


23.01          2023-06-20
-------------------------
- Some bugs were fixed.


23.00          2023-05-07
-------------------------
- 7-Zip now can use new ARM64 filter for compression to 7z and xz archives.
  ARM64 filter can increase compression ratio for data containing executable 
  files compiled for ARM64 (AArch64) architecture.
  Also 7-Zip now parses executable files (that have exe and dll filename extensions) 
  before compressing, and it selects appropriate filter for each parsed file: 
    - BCJ or BCJ2 filter for x86 executable files,
    - ARM64 filter for ARM64 executable files.
  Previous versions by default used x86 filter BCJ or BCJ2 for all exe/dll files.
- Default section size for BCJ2 filter was changed from 64 MiB to 240 MiB.
  It can increase compression ratio for executable files larger than 64 MiB.
- When new 7-Zip creates multivolume archive, 7-Zip keeps in open state
  only volumes that still can be changed. Previous versions kept all volumes 
  in open state until the end of the archive creation.
- 7-Zip for Linux and macOS now can reduce the number of simultaneously open files,
  when 7-Zip opens, extracts or creates multivolume archive. It allows to avoid 
  the failures for cases with big number of volumes, bacause there is a limitation 
  for number of open files allowed for a single program in Linux and macOS.
- The bugs were fixed:
  - ZIP archives: if multithreaded zip compression was performed with more than one 
      file to stdout stream (-so switch), 7-zip didn't write "data descriptor" for some files.
  - Some another bugs were fixed.


22.00          2022-06-16
-------------------------
- 7-Zip now can create TAR archives in POSIX (pax) tar format with the switches
    -ttar -mm=pax or -ttar -mm=posix
- 7-Zip now can store additional file timestamps with high precision (1 ns in Linux) 
  in tar/pax archives with the following switches:
    -ttar -mm=pax -mtp=3 -mtc -mta


21.07          2021-12-26
-------------------------
- New switches: -spm and -im!{file_path} to exclude directories from processing 
    for specified paths that don't contain path separator character at the end of path.
- The sorting order of files in archives was slightly changed to be more consistent
  for cases where the name of some directory is the same as the prefix part of the name
  of another directory or file.
- TAR archives created by 7-Zip now are more consistent with archives created by GNU TAR program.


21.06          2021-11-24
-------------------------
- New switch -mmemuse={N}g / -mmemuse=p{N} to set a limit on memory usage (RAM) 
  for compressing and decompressing.
- Bug in versions 21.00-21.05 was fixed:
  7-Zip didn't set attributes of directories during archive extracting.
- Some bugs were fixed.


21.04 beta     2021-11-02
-------------------------
- 7-Zip now reduces the number of working CPU threads for compression,
  if RAM size is not enough for compression with big LZMA2 dictionary.
- 7-Zip now can create and check "file.sha256" text files that contain the list 
  of file names and SHA-256 checksums in format compatible with sha256sum program.
	7-Zip can work with such checksum files as with archives,
	but these files don't contain	real file data.
  The context menu commands for command line version::
    7z a -thash file.sha256 *.txt
    7z t -thash file.sha256
    7z t -thash -shd. file.sha256 
  New -shd{dir_path} switch to set the directory that is used to check files 
  referenced by "file.sha256" file for "Test" operation. 
  If -shd{dir_path} is not specified, 7-Zip uses the directory where "file.sha256" is stored.  
- New -xtd switch to exclude directory metadata records from processing.


21.03 beta     2021-07-20
-------------------------
- The maximum dictionary size for LZMA/LZMA2 compressing was increased to 4 GB (3840 MiB).
- Minor speed optimizations in LZMA/LZMA2 compressing.


21.02 alpha    2021-05-06
-------------------------
- 7-Zip now writes additional field for filename in UTF-8 encoding to zip archives.
  It allows to extract correct file name from zip archives on different systems.
- Some changes and improvements in ZIP and TAR code.


21.01 alpha    2021-03-09
-------------------------
- The improvements for speed of ARM64 version using hardware CPU instructions 
  for AES, CRC-32, SHA-1 and SHA-256.
- The bug in versions 18.02 - 21.00 was fixed:
  7-Zip could not correctly extract some ZIP archives created with xz compression method.
- Some bugs were fixed.


20.02 alpha    2020-08-08
-------------------------
- The default number of LZMA2 chunks per solid block in 7z archive was increased to 64.
  It allows to increase the compression speed for big 7z archives, if there is a big number 
  of CPU cores and threads.
- The speed of PPMd compressing/decompressing was increased for 7z/ZIP archives.
- The new -ssp switch. If the switch -ssp is specified, 7-Zip doesn't allow the system 
  to modify "Last Access Time" property of source files for archiving and hashing operations. 
- Some bugs were fixed.


20.00 alpha    2020-02-06
-------------------------
- 7-Zip now supports new optional match finders for LZMA/LZMA2 compression: bt5 and hc5, 
  that can work faster than bt4 and hc4 match finders for the data with big redundancy.
- The compression ratio was improved for Fast and Fastest compression levels with the 
  following default settings:
   - Fastest level (-mx1) : hc5 match finder with 256 KB dictionary.
   - Fast    level (-mx3) : hc5 match finder with 4 MB dictionary.
- Minor speed optimizations in multithreaded LZMA/LZMA2 compression for Normal/Maximum/Ultra 
  compression levels.
- bzip2 decoding code was updated to support bzip2 archives, created by lbzip2 program.


19.02 alpha    2019-09-05
-------------------------
- 7-Zip now can use new x86/x64 hardware instructions for SHA-1 and SHA-256, supported
  by AMD Ryzen and latest Intel CPUs: Ice Lake and Goldmont.
  It increases
    - the speed of SHA-1/SHA-256 hash value calculation, 
    - the speed of encryption/decryption in zip AES, 
    - the speed of key derivation for encryption/decryption in 7z/zip/rar archives.
- The speed of zip AES encryption and 7z/zip/rar AES decryption was increased with 
  the following improvements:
   - 7-Zip now can use new x86/x64 VAES (AVX Vector AES) instructions, supported by 
     Intel Ice Lake CPU. 
   - The existing code of x86/x64 AES-NI was improved also.
- Some bugs were fixed.


19.00          2019-02-21
-------------------------
- Encryption strength for 7z archives was increased:
  the size of random initialization vector was increased from 64-bit to 128-bit,
  and the pseudo-random number generator was improved.
- Some bugs were fixed.


18.06          2018-12-30
-------------------------
- The speed for LZMA/LZMA2 compressing was increased by 3-10%,
  and there are minor changes in compression ratio.
- Some bugs were fixed.


18.05          2018-04-30
-------------------------
- The speed for LZMA/LZMA2 compressing was increased 
    by 8% for fastest/fast compression levels and 
    by 3% for normal/maximum compression levels.


18.03 beta     2018-03-04
-------------------------
- The speed for single-thread LZMA/LZMA2 decoding
  was increased  by 30% in x64 version and by 3% in x86 version.
- 7-Zip now can use multi-threading for 7z/LZMA2 decoding,
  if there are multiple independent data chunks in LZMA2 stream.


9.35 beta           2014-12-07
------------------------------
  - SFX modules were moved to LZMA SDK package.


9.34 alpha          2014-06-22
------------------------------
  - Minimum supported system now is Windows 2000 for EXE and DLL files.
  - all EXE and DLL files use msvcrt.dll.
  - 7zr.exe now support AES encryption.


9.18                2010-11-02
------------------------------
  - New small SFX module for installers.


9.17                2010-10-04
------------------------------
  - New 7-Zip plugin for FAR Manager x64.


9.10                2009-12-30
------------------------------
  - 7-Zip for installers now supports LZMA2.


9.09                2009-12-12
------------------------------
  - LZMA2 compression method support.
  - Some bugs were fixed.


4.65                2009-02-03
------------------------------
  - Some bugs were fixed.


4.38 beta           2006-04-13
------------------------------
  - SFX for installers now supports new properties in config file:
    Progress, Directory, ExecuteFile, ExecuteParameters.


4.34 beta           2006-02-27
------------------------------
  - ISetProperties::SetProperties:
      it's possible to specify desirable number of CPU threads: 
           PROPVARIANT: name=L"mt", vt = VT_UI4, ulVal = NumberOfThreads
      If "mt" is not defined, 7za.dll will check number of processors in system to set 
      number of desirable threads. 
      Now 7za.dll can use:
        2 threads for LZMA compressing
        N threads for BZip2 compressing
        4 threads for BZip2 decompressing
      Other codecs use only one thread. 
      Note: 7za.dll can use additional "small" threads with low CPU load.
  - It's possible to call ISetProperties::SetProperties to specify "mt" property for decoder.


4.33 beta           2006-02-05
------------------------------
  - Compressing speed and Memory requirements were increased.
    Default dictionary size was increased: Fastest: 64 KB, Fast: 1 MB, 
    Normal: 4 MB, Max: 16 MB, Ultra: 64 MB.
  - 7z/LZMA now can use only these match finders: HC4, BT2, BT3, BT4


4.27                2005-09-21
------------------------------
 - Some GUIDs/interfaces were changed.
   IStream.h:
     ISequentialInStream::Read now works as old ReadPart
     ISequentialOutStream::Write now works as old WritePart
