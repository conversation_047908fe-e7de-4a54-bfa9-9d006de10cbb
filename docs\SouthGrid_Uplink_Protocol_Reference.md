# 南网上行规约协议参考文档

## 5.1 帧格式

### 5.1.1 帧格式定义

本部分采用 GB/T 18657.1 的 6.2.4 条 FT1.2 异步式传输帧格式，定义见图2：

```
+----------------+----------------+
| 起始字符 (68H) |                |
+----------------+                |
| 长度L          | 固定长度       |
+----------------+ 的报文头       |
| 长度L          |                |
+----------------+----------------+
| 起始字符 (68H) |                |
+----------------+                |
| 控制域C        | 控制域         |
+----------------+                | 用户
| 地址域A        | 地址域         | 数据区
+----------------+                |
| 链路用户数据   | 链路用户数据   |
|               | (应用层)       |
+----------------+----------------+
| 帧校验和CS     | 帧校验和       |
+----------------+----------------+
| 结束字符 (16H) |                |
+----------------+----------------+
```

**关键特性：**
- 帧的基本单元为 8 位字节
- 链路层传输顺序为低位在前、高位在后
- 低字节在前，高字节在后

**帧结构组成：**
1. 起始字符 (68H)
2. 长度L
3. 长度L (重复)
4. 起始字符 (68H) (重复)
5. 控制域C
6. 地址域A
7. 链路用户数据 (应用层)
8. 帧校验和CS
9. 结束字符 (16H)

**用户数据区包含：**
- 控制域
- 地址域
- 链路用户数据 (应用层)

### 5.1.2 长度L

长度L表示用户数据长度，由2字节组成，见图3：

```
+-----+-----+-----+-----+-----+-----+-----+-----+
| D7  | D6  | D5  | D4  | D3  | D2  | D1  | D0  |
| D15 | D14 | D13 | D12 | D11 | D10 | D9  | D8  |
+-----+-----+-----+-----+-----+-----+-----+-----+
```

**长度L定义：**
- 用户数据长度L采用BIN编码
- 是控制域、地址域、链路用户数据（应用层）的字节总数

**不同传输方式的长度限制：**
- 采用专用无线数传传输：长度L不大于255
- 采用GPRS/CDMA传输：长度L不大于1024
- 采用4G传输：长度L不大于2048
- 采用网络传输：长度L不大于16383

**重要说明：**
- 长度L由两个字节组成，两个重复的长度L必须完全一致，否则此帧为无效帧

### 5.1.3 控制域C

控制域C表示报文传输方向和所提供的传输服务类型的信息，定义见图4：

```
+----------+----------+----------+----------+----------+
|    D7    |    D6    |    D5    |    D4    |  D3 D0   |
+----------+----------+----------+----------+----------+
| 下行方向 | 传输方向位| 启动标志位| 帧计数位FCB| 帧计数有效位|
| 上行方向 |   DIR    |   PRM    | 要求访问位ACD| FCV 保留  |
+----------+----------+----------+----------+----------+
|                                           | 功能码   |
+-------------------------------------------+----------+
```

**控制域C各位定义：**

**D7位 - 传输方向：**
- 下行方向：主站向终端传输
- 上行方向：终端向主站传输

**D6位 - 传输方向位(DIR)：**
- 用于指示数据传输方向

**D5位 - 启动标志位(PRM)：**
- 标识启动站发送的报文

**D4位：**
- 下行：帧计数位FCB
- 上行：要求访问位ACD

**D3位：**
- 下行：帧计数有效位FCV
- 上行：保留

**D3-D0位 - 功能码：**
- 定义具体的功能操作

#### 5.1.3.1 传输方向位DIR

- **DIR=0**：表示此帧报文是由主站发出的下行报文
- **DIR=1**：表示此帧报文是由终端发出的上行报文

#### 5.1.3.2 启动标志位PRM

- **PRM=1**：表示此帧报文来自启动站
- **PRM=0**：表示此帧报文来自从动站

#### 5.1.3.3 帧计数位FCB

当帧计数有效位FCV=1时，FCB表示每个站连续的发送/确认或者请求/响应服务的变化位。FCB位用来防止信息传输的丢失和重复。

**FCB工作机制：**
- 启动站向同一从动站传输新的发送/确认或请求/响应传输服务时，将FCB取相反值
- 启动站保存每一个从动站FCB值
- 若超时未收到从动站的报文，或接收出现差错，则启动站不改变FCB的状态，重复原来的发送/确认或者请求/响应服务
- 复位命令中的FCB=0，从动站接收复位命令后将FCB置"0"

#### 5.1.3.4 请求访问位ACD

ACD位用于上行响应报文中：
- **ACD=1**：表示终端有告警数据等待访问，主站在本次通信服务结束后可以发起召读告警数据
- **ACD=0**：表示终端无告警数据等待访问

**ACD置位规则：**
- **置"1"条件**：自上次收到报文后发生新的告警并且主动上送不成功
- **置"0"条件**：收到主站请求告警报文并执行后

#### 5.1.3.5 帧计数有效位FCV

- **FCV=1**：表示FCB位有效
- **FCV=0**：表示FCB位无效

#### 5.1.3.6 功能码

当启动标志位PRM=1时，功能码定义见表2：

**表2 功能码定义（PRM=1）**

| 功能码 | 帧类型 | 服务功能 |
|--------|--------|----------|
| 0 | —— | 备用 |
| 1 | 发送/确认 | 复位命令 |
| 2~3 | —— | 备用 |
| 4 | 发送/无回答 | 用户数据 |
| 5~8 | —— | 备用 |
| 9 | 请求/响应帧 | 链路测试 |
| 10 | 请求/响应帧 | 请求1级数据 |
| 11 | 请求/响应帧 | 请求2级数据 |
| 12~15 | —— | 备用 |

当启动标志位PRM=0时，功能码定义见表3：

**表3 功能码定义（PRM=0）**

| 功能码 | 帧类型 | 服务功能 |
|--------|--------|----------|
| 0 | 确认 | 认可 |
| 1~7 | —— | 备用 |
| 8 | 响应帧 | 用户数据 |
| 9 | 响应帧 | 否定：无所召唤的数据 |
| 10 | —— | 备用 |
| 11 | 响应帧 | 链路状态 |
| 12~15 | —— | 备用 |

**本部分规定：**

1. **启动站功能码10（请求1级数据）**用于应用层请求确认（CON=1）的链路传输，应用层请求确认标志见本部分6.1.2.4。

2. **启动站功能码11（请求2级数据）**用于应用层请求数据的链路传输。

3. **控制域C为链路层控制字传输方向和所提供的传输服务类型的信息**，本部分标准参照GB/T18657标准以及标际应用要求定义了控制域C中的各位以及由D0~D3组成的功能码定义，并且该链路层的功能码与后续的应用层功能码相互分离、独立，通过不同的功能码定义提供不同的通信服务类型。

**对链路控制域C的使用需注意以下几方面的内容：**
- 对D4~D7规定的各位都必须按要求进行标识

**表4 链路层功能码与应用层功能码对应关系**

| 启动标志位 | 链路层功能码 | 帧类型 | 服务功能 | 应用层AFN | 应用功能定义 |
|------------|--------------|--------|----------|-----------|--------------|
| **PRM=1** | 1 | 发送/确认 | 复位 | 01H | 复位 |
| | 4 | 发送/无回答 | 用户数据 | | 应用层按收用户数据，不作任何回答 |
| | 9 | 请求/响应帧 | 链路测试 | 02H | 链路接口检测 |
| | 10 | 请求/响应帧 | 请求1级数据 | 04H | 参数数 |
| | 11 | 请求/响应帧 | 请求2级数据 | 06H | 身份认证及密钥协商 |
| | | | | 0AH | 参数数 |
| | | | | 0CH | 读当前数据 |
| | | | | 0DH | 读历史数据 |
| | | | | 0EH | 读事件记录 |
| | | | | 0FH | 文件传输 |
| | | | | 10H | 中继转发 |
| | | | | 12H | 读任务数据 |
| | | | | 13H | 读告警数据 |
| | | | | 14H | 级联命令 |
| | | | | 15H | 用户自定义数据 |
| | | | | 16H | 数据安全传输 |
| | | | | 17H | 数据转加密 |
| **PRM=0** | 0 | 确认 | 认可 | 00H | 对应PRM=1，链路层功能码=01的确认/否认 |
| | 8 | 响应帧 | 用户数据 | 00H | 对应PRM=1，链路层功能码=10或11的确认/否认 |
| | | | | 04H | 对应PRM=1，链路层功能码=10，相应写参数 |
| | | | | 06H | 对应PRM=1，链路层功能码=11，身份认证及密钥协商 |
| | | | | 0AH | 对应PRM=1，链路层功能码=11，响应读参数 |
| | | | | 0CH | 对应PRM=1，链路层功能码=11，响应读当前数据 |
| | | | | 0DH | 对应PRM=1，链路层功能码=11，响应读历史数据 |
| | | | | 0EH | 对应PRM=1，链路层功能码=11，响应读事件记录 |
| | | | | 0FH | 对应PRM=1，链路层功能码=11，响应文件传输 |
| | | | | 10H | 对应PRM=1，链路层功能码=11，响应中继转发 |
| | | | | 12H | 对应PRM=1，链路层功能码=11，响应读任务数据 |
| | | | | 13H | 对应PRM=1，链路层功能码=11，响应读告警数据 |
| | | | | 14H | 对应PRM=1，链路层功能码=11，响应级联命令 |
| | | | | 15H | 对应PRM=1，链路层功能码=11，响应用户自定义数据 |
| | | | | 16H | 对应PRM=1，链路层功能码=11，响应数据安全传输 |
| | | | | 17H | 对应PRM=1，链路层功能码=11，响应数据转加密 |
| | 9 | 响应帧 | 否认：无所召唤数据 | | |
| | 11 | 响应帧 | 链路状态 | 00H | 对应PRM=1，链路层功能码=09的确认/否认 |

### 5.1.4 地址域A

#### 5.1.4.1 地址域格式

地址域由省地市区县码A1、终端地址A2、主站地址A3组成，共7个字节，格式见表5：

**表5 地址域格式**

| 地址域 | 数据格式 | 字节数 |
|--------|----------|--------|
| 省地市区县码A1 | BCD | 3 |
| 终端地址A2 | BIN | 3 |
| 主站地址A3 | BIN | 1 |

#### 5.1.4.2 省地市区县码A1

- 省地市区县码按 **GB 2260-2007** 的规定执行
- 当此通信的最终发起端和接收端为终端时，省地市区县码表示终端所属的省份、地市以及区县
- **A1组成**：
  - 高字节：表示省份
  - 中间字节：表示地市码
  - 低字节：表示区县码
- **传输顺序**：按照低字节在前，高字节在后的顺序传输

#### 5.1.4.3 终端地址A2

- **选址范围**：1~16777216
- **特殊地址**：
  - A2=000000H：无效地址
  - A2=FFFFFFH：系统广播地址
- **传输顺序**：按照低字节在前，高字节在后的顺序传输

#### 5.1.4.4 主站地址A3

- A3的D0~D7组成0~255个主站地址MSA
- A3支持应用系统采用网络通道与终端交换数据时，有多个主站进行操作的情况

**MSA使用规则：**
- **主站启动的发送帧**：MSA应为非零值
- **终端响应帧**：MSA应与主站发送帧的MSA相同
- **终端启动发送帧**：MSA应为零
- **主站响应帧**：MSA也应为零

## 6.1 应用层（链路用户数据）格式定义

### 6.1.1 应用层功能码AFN

应用层功能码AFN由一字节组成，采用二进制编码表示，具体定义见表7：

**表7 应用层功能码定义**

| 应用层功能码AFN | 应用功能定义 |
|----------------|--------------|
| 00H | 确认/否定 |
| 02H | 链路接口检测 |
| 04H | 写参数 |
| 06H | 身份认证 |
| 0AH | 读参数 |
| 0CH | 读当前数据 |
| 0DH | 读历史数据 |
| 0EH | 读事件记录 |
| 0FH | 文件传输 |
| 10H | 中继转发 |
| 12H | 读任务数据 |
| 13H | 读告警数据 |
| 14H | 级联命令 |
| 15H | 用户自定义数据 |
| 16H | 数据安全传输 |
| 17H | 数据转发加密 |
| 其他 | 备用 |

### 6.1.2 帧序列域SEQ

#### 6.1.2.1 帧序列域SEQ定义

帧序列域SEQ为1字节，用于描述被之间的传输序列的变化规则，由主受报文长度限制，数据无法在一帧内传输，需要分帧传输（每帧都应有数据标识、数据内容标识，都可以作为独立的报文处理）。SEQ定义见图6：

```
+-----+-----+-----+-----+-------------+
| D7  | D6  | D5  | D4  |   D3 D0     |
+-----+-----+-----+-----+-------------+
| TpV | FIR | FIN | CON | PSEQ/RSEQ   |
+-----+-----+-----+-----+-------------+
```

**图6 SEQ定义**

#### 6.1.2.2 帧时间标签有效位TpV

- **TpV=0**：表示无时间标签Tp
- **TpV=1**：表示带有时间标签Tp（Tp定义见本部分6.1.8）

#### 6.1.2.3 首帧标志FIR、末帧标志FIN

- **FIR**：置"1"，报文的第一帧
- **FIN**：置"1"，报文的最后一帧

FIR、FIN组合状态所表示的含义见表8：

**表8 帧标志**

| FIR | FIN | 应用说明 |
|-----|-----|----------|
| 0 | 0 | 多帧：中间帧 |
| 0 | 1 | 多帧：结束帧 |
| 1 | 0 | 多帧：第1帧，有后续帧 |
| 1 | 1 | 单帧 |

### 6.1.3 信息点标识DA

信息点标识DA格式见图10：

```
+----------+----+----+----+----+----+----+----+----+
| 信息点DA | DA1| D7 | D6 | D5 | D4 | D3 | D2 | D1 | D0 |
|          | DA2| D7 | D6 | D5 | D4 | D3 | D2 | D1 | D0 |
+----------+----+----+----+----+----+----+----+----+----+
```

**图10 信息点标识定义**

信息点标识DA由信息点元DA1和信息点组DA2两个字节构成。

DA2用二进制编码方式表示信息点组。一个信息点组可表示一信息点组的1~8个测量点，以此共同构成信息点标识DA（n=1~2032）。格式见图11：

```
+----------+----+----+----+----+----+----+----+----+
| 信息点组DA2 |    | 信息点元DA1 |              |
| Pn-D0    | D7 | D6 | D5 | D4 | D3 | D2 | D1 | D0 |
|    1     | p8 | P7 | p6 | p5 | p4 | p3 | p2 | p1 |
|    2     | p16| P15| p14| p13| p12| p11| p10| p9 |
|    3     | p24| P23| p22| p21| p20| p19| p18| p17|
|   ......  |......|......|......|......|......|......|......|......|
|   254    |P2032|P2031|P2030|P2029|P2028|P2027|P2026|P2025|
+----------+----+----+----+----+----+----+----+----+
```

**图11 信息点格式**

**信息点标识规则：**
- 当DA1和DA2全为"0"时，表示终端测量点，用p0表示
- 当DA1=FFH且DA2=FFH时，表示除了终端信息点以外的所有测量点
- 例如DA2=01H，DA1=01H表示测量点p1；DA2=01H，DA1=03H，表示测量点p1、p2

### 6.1.4 数据标识编码DI

数据标识编码DI由四个字节构成，用来区分不同的数据标识，四个字节分别用DI3、DI2、DI1和DI0代表，每字节采用十六进制编码，具体定义见附录A1，格式见图12：

```
+--------+--------+--------+--------+
|  DI3   |  DI2   |  DI1   |  DI0   |
+--------+--------+--------+--------+
```

**图12 数据标识编码格式**

**数据标识编码规则：**

对于只有数据标识无数据标识内容的帧，一个DA、DI的值可以表示多个信息点和一个数据标识；对于有数据标识和数据标识内容的帧，每个DA、DI的值唯一表示一个信息点和一个数据标识，每个DA、DI后面紧跟其数据标识的内容。

**示例说明：**

如，读测量点p1、p2的当前正向有功总电能：

**下行报文中：**
```
+---------------------------+
| 信息点：DA=0103H          |
| 数据标识：DI=00010000H    |
+---------------------------+
```

**上行报文中：**
```
+---------------------------+
| 信息点1：DA=0101H         |
| 数据标识：DI=00010000H    |
| 数据标识内容：DATA1       |
| 信息点2：DA=0102H         |
| 数据标识：DI=00010000H    |
| 数据标识内容：DATA2       |
+---------------------------+
```

### 6.1.5 数据标识内容

数据标识内容为按数据标识所组织的数据，包括参数、命令等。具体数据标识内容参见附录。

**数据组织的顺序规则：**按pn从小到大的次序，即：完成一个信息点p1的数据标识编码的处理后，再进行下一个p1+1的处理。终端在响应主站读取多个时间点的数据请求时，按时间的先后顺序组织回复报文，即先组织第一个时间点的DA数据，再组织下一个时间点的DA数据。

**终端在响应主站对终端的参数或数据请求时，数据标识内容应符合以下规则：**

1) **如果终端没有所需信息点标识的所有数据标识内容，则将应答报文中DA的对应标志位清除，即上行报文中清除对应信息点标识。**

2) **如果终端仅是没有所需信息点标识的部分数据标识中内容，则应将该数据标识中的所缺部分的数据标识内容填充"FFH"。**

3) **如果终端所有信息点标识的所有数据标识编码所对应内容都不存在，则按否定报文回复，详见6.2.1。**

4) **如果终端没有所需信息点标识的所有数据标识内容，则将应答报文中DA的对应标志位清除，即上行报文中清除对应信息点标识。**

**示例说明：**

如，读测量点p1~p3的当前正向、反向有功总电能：

**下行报文中：**
```
+---------------------------+
| 信息点：DA=0107H          |
| 数据标识1：DI=00010000H   |
| 信息点：DA=0107H          |
| 数据标识2：DI=00020000H   |
+---------------------------+
```

**若测量点p2无任何数据，上行报文中：**
```
+---------------------------+
| 信息点1：DA=0101H         |
| 数据标识1：DI=00010000H   |
+---------------------------+
```

```
+---------------------------+
| 数据标识内容：DATA1       |
| 信息点1：DA=0101H         |
| 数据标识2：DI=00020000H   |
| 数据标识内容：DATA2       |
| 信息点3：DA=0104H         |
| 数据标识1：DI=00010000H   |
| 数据标识内容：DATA3       |
| 信息点3：DA=0104H         |
| 数据标识2：DI=00020000H   |
| 数据标识内容：DATA4       |
+---------------------------+
```

### 6.1.6 数据时间域

数据时间域包括数据时间1、数据时间2、数据密度，具体定义见表9：

**表9 数据域定义**

| 数据域 | 说明 |
|--------|------|
| 数据时间1 | 起始时间或单一时间点 |
| 数据时间2 | 结束时间 |
| 数据密度 | 数据采集间隔 |

#### 6.1.6.1 数据时间1

数据时间1采用BCD编码，具体定义见表10：

**表10 数据时间定义**

| 字节名称 | 说明 |
|----------|------|
| 年 (YYYY) | BCD码 (1970~2099, 表示1970~2099年) |
| 月 (MM) | BCD码 (01~12, 表示01~12月) |
| 日 (DD) | BCD码 (01~31, 表示01~31日) |
| 时 (hh) | BCD码 (00~23, 表示00~23时) |
| 分 (mm) | BCD码 (00~59, 表示00~59分) |

**传输顺序：**按照年、月、日、时、分的顺序传输。

**数据说明：**
- **分数据、时数据：**指终端在对应具体时刻采集的数据
- **日数据：**指终端在次日0:00冻结的数据，对应的数据时间为当日
- **月数据：**指终端在次月冻结日冻结的数据，对应的数据时间为当月

#### 6.1.6.2 数据时间2

定义同6.1.6.1。

数据时间1和数据时间2分别表示起始时间和结束时间时，按前闭后开区间定义。

**时间传输格式：**按照年、月、日、时、分的顺序传输。如2013年5月9日13时14分，传输顺序为：20 13 05 09 13 14。

**读历史数据示例：**

1) **读2013年5月9日的历史日数据，数据时间密度为1天，则下行报文中：**
```
+---------------------------+
| 数据起始时间：20 13 05 09 00 00 |
| 数据结束时间：20 13 05 10 00 00 |
| 数据时间密度：6                |
+---------------------------+
```

**上行报文中：**
```
+---------------------------+
| 数据1：5月9日历史日数据         |
| 数据时间：20 13 05 09 00 00    |
+---------------------------+
```

历史日数据内容见附录C.3，包括5月9日冻结电量、停电统计、电压监测等数据。

2) **读2013年5月的历史月数据，数据时间密度为1月，则下行报文中：**
```
+---------------------------+
| 数据起始时间：20 13 05 01 00 00 |
| 数据结束时间：20 13 06 01 00 00 |
| 数据时间密度：7                |
+---------------------------+
```

**上行报文中：**
```
+---------------------------+
| 数据1：5月历史月数据            |
| 数据时间：20 13 05 01 00 00    |
+---------------------------+
```

历史月数据内容见附录C.4，包括5月冻结电量、停电统计、电压监测等数据。

#### 6.1.6.3 数据密度

数据密度由一字节组成，采用二进制编码表示，具体定义见表11：

**表11 数据密度定义**

| m | 数据密度间隔时间 | 数据时刻排列次序 |
|---|------------------|------------------|
| 0 | 终端历史数据存储密度 | 按终端实际存储数据的时间间隔 |
| 1 | 1 分钟 | 0分、1分、2分、......、59分 |
| 2 | 5 分钟 | 0分、5分、10分、......、55分 |
| 3 | 15 分钟 | 0分、15分、30分、......、45分 |
| 4 | 30 分钟 | 0分、30分 |
| 5 | 60 分钟 | 0分 |
| 6 | 1 日 | 1日、2日、3日、...... |
| 7 | 1 月 | 1月、2月、...... |
| 8 | 结算日 | |
| 其它 | 备用 | |

**数据密度说明：**
- 曲线数据，如间隔为15分钟的曲线数据，数据密度m=3
- 日数据，数据密度为m=6
- 月数据，数据密度为m=7
- 若以任务形式读取与结算日相关的数据，则必须采用自描述任务数据格式

### 6.1.7 消息认证码PW

消息认证码PW为16字节，用于重要报文中。

PW按照系统定义的认证算法生成。发送端在报文中包含PW，接收端进行验证，验证通过则响应命令，否则拒绝。

终端或主站接收到包含PW的报文后，必须认证通过后才响应。

PW字段的认证方法由终端参数定义（见附录A.7和附录K）。

当采用安全模块加密方法时，PW字段内的MAC值需要验证，用于安全认证、参数写、数据加密。

MAC值占用PW字段的前4字节，按低字节在前、高字节在后的顺序传输。

对于读参数、读当前数据、读历史数据、读任务数据、读事件记录、告警上行报文、主站下行报文等操作，PW字段必须包含数据验证信息（MAC）、计数值、随机数（RN）。

### 6.1.8 时间标签Tp

时间标签用于交换网络通道中，帮助从动站判断所接收报文的时序性和时效性，用于同时建立多个通信服务的传输服务中。

时间标签Tp为5字节，格式见表12：

**表12 时间标签格式**

| 数据名称 | 数据格式 | 单位 | 字节数 |
|----------|----------|------|--------|
| 启动帧发送时标 | DDhhmmss | 日时分秒 | 4 |
| 允许发送传输延时时间 | BIN | 分 | 1 |

**时间标签Tp生成和使用：**

时间标签Tp由启动站生成，通过报文传输给从动站。从动站利用Tp判断所接收报文的时序性和时效性，判断有效则响应该帧，判断无效则不响应。

**启动帧发送时标：**记录启动帧发送的时刻。

**允许发送传输延时时间：**表示启动站允许的从启动站开始发送报文到从动站接收到报文的最大传输延时时间。

**从动站时效性判断规则：**

1) **丢弃条件：**从动站当前时间与Tp中"启动帧发送时标"的时间差大于Tp中"允许发送传输延时时间"，则从动站丢弃该报文。

2) **响应条件：**从动站当前时间与Tp中"启动帧发送时标"的时间差不大于Tp中"允许发送传输延时时间"，则从动站响应。

3) **不判断条件：**Tp中"允许发送传输延时时间"为"0"，则从动站不进行上述两个判断。

### 6.1.9 应用层数据格式

应用层（链路用户数据）格式定义见图5：

```
+------------------+------------------------------------------+
| 应用层功能码AFN  |                                          |
+------------------+                                          |
| 帧序列域SEQ      |                                          |
+------------------+                                          |
| 信息点标识1      |                                          |
+------------------+                                          |
| 数据标识码1      | 信息体1                                  |
+------------------+                                          |
| 数据标识内容1    | 附属部分根据不同应用层功能码可选         |
+------------------+                                          |
| 数据时间域       |                                          |
+------------------+------------------------------------------+
|      ......      |                                          |
+------------------+                                          |
| 信息点标识n      |                                          |
+------------------+                                          |
| 数据标识码n      | 信息体n                                  |
+------------------+                                          |
| 数据标识内容n    | 附属部分根据不同应用层功能码可选         |
+------------------+                                          |
| 数据时间域       |                                          |
+------------------+------------------------------------------+
| 消息认证码PW     |                                          |
+------------------+ 附属部分根据不同应用层功能码可选         |
| 时间标签TP       |                                          |
+------------------+------------------------------------------+
```

**应用层结构说明：**
- **应用层功能码AFN**：定义应用层功能
- **帧序列域SEQ**：帧序列控制
- **信息体**：包含多个信息点的数据
  - 信息点标识
  - 数据标识码
  - 数据标识内容
  - 数据时间域
- **附属部分**：根据不同应用层功能码可选
  - 消息认证码PW
  - 时间标签TP

---

## 6.2 应用层功能码帧格式定义

### 6.2.1 确认/否定 (AFN=00H)

#### 6.2.1.1 报文格式

确认/否定报文是对接收到的需要确认的报文（CON=1）的响应，也是终端因条件不满足而无法执行主站请求的数据操作时的否定响应。本报文为单帧报文，帧序列域中FIR=1，FIN=1，CON=0。格式见图13：

**图13 确认/否定报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=00H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据标识内容 1 | 00:表示确定 01:表示否定 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 2 | 数据标识编码 2 | 数据标识内容 2 | 00:表示确定 01:表示否定 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 m | 数据标识编码 m | 数据标识内容 m | 00:表示确定 01:表示否定 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据标识内容 1 | 00:表示确定 01:表示否定 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据标识内容 m | 00:表示确定 01:表示否定 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

**关键特性：**
- **报文类型：** 单帧报文
- **帧序列域设置：** FIR=1，FIN=1，CON=0
- **确认/否定标识：** 数据标识内容字段中，00表示确定，01表示否定
- **支持多信息点：** 可同时确认或否定多个信息点和数据标识

#### 6.2.1.2 数据标识编码定义

数据标识编码定义见6.1.4。当数据标识编码为E0000000H时，表示全确认/否定响应，其数据格式见附录A.1。

#### 6.2.1.3 数据标识内容定义

数据标识内容定义见6.1.5。全确认/否定报文帧格式见图14：

**图14 确认/否定报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=00H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识=0000H | 数据标识编码=E0000000H | 数据标识内容 | 00:表示确定 01:表示否定 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

**使用场景：**
在确认链路接口检测、身份认证及密钥协商、任务主动上送报文、告警主动上送报文时，使用本帧报文进行确认或否认。

### 6.2.2 链路接口检测 (AFN=02H)

链路接口检测报文用于交换网络数据通道，如GPRS/CDMA/3G/4G/5G/以太网等，用于终端登录网络、终端退出网络、终端在线保持心跳。

#### 6.2.2.1 上行报文

##### 6.2.2.1.1 报文格式

上行报文格式见图15：

**图15 链路接口检测报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=02H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

##### 6.2.2.1.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.2.1.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.2.1.4 数据标识内容定义

数据标识内容定义见6.1.5，具体内容见附录A.15。

#### 6.2.2.2 下行报文

链路接口检测下行报文为确认/否定报文（AFN=00H），详见6.2.1。

### 6.2.3 写参数 (AFN=04H)

#### 6.2.3.1 下行报文

##### 6.2.3.1.1 报文格式

下行报文格式见图16：

**图16 写参数下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=04H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 2 | 数据标识编码 2 | 数据标识内容 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

**关键特性：**
- **唯一标识：** 下行报文的每个DA、DI的值唯一表示一个信息点和一个数据标识，后面紧跟其数据标识的内容
- **整体加密：** 所有信息点标识、数据标识编码和数据标识内容整体加密后使用写参数报文下发
- **安全机制：** 包含消息认证码PW和时间标签Tp

#### 6.2.3.2 上行报文

##### 6.2.3.2.1 报文格式

写参数上行(应答)报文格式见图17：

**图17 写参数上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=04H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | ERR1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 2 | ERR2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 m | ERRm |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | ERR1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | ERRm |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

##### 6.2.3.2.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.3.2.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.3.2.4 错误码ERR定义

错误码由1字节的二进制编码表示，定义见附录F。

### 6.2.4 安全认证 (AFN=06H)

本报文主要用于主站和终端之间的安全认证。

#### 6.2.4.1 下行报文

##### 6.2.4.1.1 报文格式

安全认证下行报文格式见图18：

**图18 安全认证下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=06H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

##### 6.2.4.1.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.4.1.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.4.1.4 数据标识内容定义

数据标识内容定义见6.1.5。具体内容参见附录A.7。

#### 6.2.4.2 上行报文

##### 6.2.4.2.1 报文格式

安全认证上行报文格式见图19：

**图19 安全认证上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=06H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 数据单元标识(DA=0) | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

##### 6.2.4.2.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.4.2.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.4.2.4 数据标识内容定义

数据标识内容定义见6.1.5。具体内容参见附录A.7。

### 6.2.5 读参数 (AFN=0AH)

#### 6.2.5.1 下行报文

##### 6.2.5.1.1 报文格式

读参数下行报文格式见图20：

**图20 读参数下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0AH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 2 | 数据标识编码 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 m | 数据标识编码 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

**关键特性：**
下行报文中，若只有数据标识无数据标识内容，则每个DA、DI的值可以表示多个信息点和一个数据标识。

#### 6.2.5.2 上行报文

##### 6.2.5.2.1 报文格式

读参数命令上行(应答)报文格式见图21：

**图21 读参数命令应答报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0AH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 2 | 数据标识编码 2 | 数据标识内容 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 m | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

**关键特性：**
上行报文中，每个DA、DI的值唯一表示一个信息点和一个数据标识，后面紧跟其数据标识的内容。

##### 6.2.5.2.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.5.2.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.5.2.4 数据标识内容定义

数据标识内容定义见6.1.5。

### 6.2.6 读当前数据 (AFN=0CH)

本报文用于读取终端、测量点的当前数据。

#### 6.2.6.1 下行报文

##### 6.2.6.1.1 报文格式

读当前数据下行报文格式见图22：

**图22 读当前数据下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0CH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 2 | 数据标识编码 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 m | 数据标识编码 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

**关键特性：**
下行报文中，若只有数据标识无数据标识内容，则每个DA、DI的值可以表示多个信息点和一个数据标识。

#### 6.2.6.2 上行报文

##### 6.2.6.2.1 报文格式

读当前数据上行(应答)报文格式见图23：

**图23 读当前数据上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0CH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 2 | 数据标识内容 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

**关键特性：**
上行报文中，每个DA、DI的值唯一表示一个信息点和一个数据标识，后面紧跟其数据标识的内容。

### 6.2.7 读历史数据 (AFN=0DH)

历史数据指终端存储的各类数据。本报文用于读取终端存储的历史日数据、历史月数据以及曲线数据。

#### 6.2.7.1 下行报文

##### 6.2.7.1.1 报文格式

读历史数据下行报文格式见图24：

**图24 读历史数据下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0DH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据起始时间 1 | 数据结束时间 1 | 数据密度 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 2 | 数据标识编码 2 | 数据起始时间 2 | 数据结束时间 2 | 数据密度 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 n | 数据起始时间 n | 数据结束时间 n | 数据密度 n |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

**关键特性：**
下行报文中，若只有数据标识无数据标识内容，则每个DA、DI的值可以表示多个信息点和一个数据标识。

#### 6.2.7.2 上行报文

##### 6.2.7.2.1 报文格式

读历史数据上行(应答)报文格式见图25：

**图25 读历史数据上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0DH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据标识内容 1 | 数据时间 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 2 | 数据标识内容 2 | 数据时间 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 m | 数据标识内容 m | 数据时间 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据标识内容 1 | 数据时间 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据标识内容 m | 数据时间 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

**关键特性：**
上行报文中，每个DA、DI的值唯一表示一个信息点和一个数据标识，后面紧跟数据标识的内容和数据时间。

##### 6.2.7.2.2 数据标识内容数定义

信息点数据标识编码对应的标识内容数由1字节BIN码表示。

### 6.2.8 读事件记录 (AFN=0EH)

本报文用于读取终端存储的事件记录。

#### 6.2.8.1 下行报文

##### 6.2.8.1.1 报文格式

读事件记录下行报文格式见图26：

**图26 读事件记录下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0EH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据起始时间 1 | 数据结束时间 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 n | 数据起始时间 n | 数据结束时间 n |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

##### 6.2.8.1.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.8.1.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.8.1.4 数据时间域定义

数据时间域定义见6.1.6。

#### 6.2.8.2 上行报文

##### 6.2.8.2.1 报文格式

读事件记录上行(应答)报文格式见图27：

**图27 读事件记录上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0EH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 2 | 数据标识内容 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

### 6.2.9 文件传输 (AFN=0FH)

本报文用于主站和终端之间的文件数据传输，如程序文件等。

#### 6.2.9.1 下行报文

##### 6.2.9.1.1 报文格式

文件传输下行报文格式见图28：

**图28 文件传输下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0FH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

**关键特性：**
下行报文中，当DA=00H时，每帧只包含一个DA、DI。当DI表示"文件传输启动"或"传输文件内容"时，数据标识内容紧跟数据标识编码；当DI表示"文件信息查询"时，只有数据标识编码，无数据标识内容。

#### 6.2.9.2 文件传输启动、传输文件内容的上行报文

##### 6.2.9.2.1 报文格式

文件传输启动、传输内容的上行(应答)报文格式见图29：

**图29 文件传输上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0FH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | ERR |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

##### 6.2.9.2.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.9.2.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.9.2.4 错误码ERR定义

错误码由1字节的二进制编码表示，定义见附录F。

#### 6.2.9.3 查询文件信息的上行报文

##### 6.2.9.3.1 报文格式

查询文件信息的上行报文格式见图30：

**图30 查询文件信息上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=0FH|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

**关键特性：**
上行报文中，DA=00H，DI表示查询文件信息，后面紧跟数据标识的内容。

### 6.2.10 中继转发 (AFN=10H)

本报文用于终端中继转发与本终端相连的智能设备和主站间的数据传输，具体应用如：主站通过终端直接对终端下的电能表进行远程抄表。

#### 6.2.10.1 下行报文

##### 6.2.10.1.1 报文格式

中继转发下行报文格式见图31：

**图31 中继转发下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=10H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

##### 6.2.10.1.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.10.1.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.10.1.4 数据标识内容定义

数据标识内容定义见6.1.5。

#### 6.2.10.2 上行报文

##### 6.2.10.2.1 报文格式

中继转发上行(应答)报文格式见图32：

**图32 中继转发上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=10H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

##### 6.2.10.2.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.10.2.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.10.2.4 数据标识内容定义

数据标识内容定义见6.1.5。

### 6.2.11 读任务数据 (AFN=12H)

本报文用于抄读终端的任务数据，任务参数包括普通任务和中继任务，任务参数需通过写参数命令进行设置。

#### 6.2.11.1 下行报文

每帧下行报文只能读取一个任务。

##### 6.2.11.1.1 报文格式

读任务数据下行报文格式见图33：

**图33 读任务数据下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=12H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码(任务号) | 数据起始时间 | 数据结束时间 | 数据密度 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

##### 6.2.11.1.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.11.1.3 数据标识编码定义

数据标识编码定义见6.1.4。数据标识编码 E0000301H~E00003FDH 表示普通任务号1~253。

##### 6.2.11.1.4 数据起始时间

数据起始时间定义见6.1.6.1。

##### 6.2.11.1.5 数据结束时间

数据结束时间定义见6.1.6.2。数据起始时间和数据结束时间是指数据冻结（或存储）时间。

##### 6.2.11.1.6 数据密度

数据密度定义见6.1.6.3。数据密度是指数据的间隔时间，如数据密度m=3表示从任务数据中每隔15分钟抽取一个值，与任务参数中的数据抽取倍率参数无关，如果数据密度m小于任务参数定义的采样周期，则终端应答否认报文。如数据密度m=0表示按任务参数定义的定时采样周期及数据抽取倍率抽取数据。

#### 6.2.11.2 上行报文

每帧只能应答一个任务数据内容，如果任务数据内容太长，需要进行分帧处理。

##### 6.2.11.2.1 报文格式

读任务数据上行(应答)报文格式见图34：

**图34 读任务数据上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=12H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码(对应的任务号编码) | 任务数据内容 1 | 数据时间 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 任务数据内容 2 | 数据时间 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 数据标识内容 n | 数据时间 n |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

### 6.2.12 读告警数据 (AFN=13H)

本报文用于读取终端储存的告警数据，数据起始时间和数据结束时间按前闭后开区间定义。

#### 6.2.12.1 下行报文

##### 6.2.12.1.1 报文格式

读告警数据下行报文格式见图35：

**图35 读告警数据下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=13H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据起始时间 1 | 数据结束时间 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 m | 数据起始时间 m | 数据结束时间 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据起始时间 1 | 数据结束时间 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据起始时间 m | 数据结束时间 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

#### 6.2.12.2 上行报文

##### 6.2.12.2.1 报文格式

读告警数据上行(应答)报文格式见图36：

**图36 读告警数据上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=13H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 2 | 数据标识内容 2 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 1 | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 1 | 数据标识内容 1 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|      ......      |      ......      |      ......      |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 n | 数据标识编码 m | 数据标识内容 m |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

### 6.2.13 用户自定义数据 (AFN=15H)

前置机接收到此命令格式(AFN=15H)的报文后直接转发。用户自定义数据内容必须包含厂家代码。用户自定义数据用于实现各厂家的特定命令，为非标准报文。前置机接收到AFN=15H报文后直接转发，用户自定义数据内容必须包含厂家代码。厂家解析程序可以下行报文的形式向前置机发送命令。前置机接收到上行非标准报文后，转发给厂家解析程序。厂家解析程序也可以按标准报文格式以上行报文的形式向前置机发送解析结果，前置机再转发给应用服务器。主站为每个厂家提供唯一代码，厂家代码用于验证终端的厂家。

#### 6.2.13.1 下行报文

##### 6.2.13.1.1 报文格式

用户自定义数据下行报文格式见图37：

**图37 用户自定义数据下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=15H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

#### 6.2.13.2 上行报文

##### 6.2.13.2.1 报文格式

用户自定义数据上行报文格式见图38：

**图38 用户自定义数据上行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=15H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
|   CS   |  16H   |
+--------+--------+
```

### 6.2.14 数据安全传输 (AFN=16H)

本报文用于主站与终端通信数据的加密保护。

#### 6.2.14.1 下行报文

##### 6.2.14.1.1 报文格式

数据安全传输下行报文格式见图39：

**图39 数据安全传输下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=16H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

##### 6.2.14.1.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.14.1.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.14.1.4 数据标识内容定义

数据标识内容定义见6.1.5。具体内容参见附录0。

#### 6.2.14.2 上行报文

##### 6.2.14.2.1 报文格式

数据安全传输上行(应答)报文格式见图40：

**图40 数据安全传输应答报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=16H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识(DA=0) | 数据标识编码 | 数据内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

### 6.2.15 数据转加密 (AFN=17H)

本报文用于终端与智能电能表数据转加密通信。

#### 6.2.15.1 下行报文

##### 6.2.15.1.1 报文格式

数据转加密下行报文格式见图41：

**图41 数据转加密下行报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=17H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

##### 6.2.15.1.2 信息点标识定义

信息点标识定义见6.1.3。

##### 6.2.15.1.3 数据标识编码定义

数据标识编码定义见6.1.4。

##### 6.2.15.1.4 数据标识内容定义

数据标识内容定义见6.1.5。具体内容参见附录P。

#### 6.2.15.2 上行报文

##### 6.2.15.2.1 报文格式

中继转发上行(应答)报文格式见图42：

**图42 数据转加密应答报文格式**

```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  68H   |   L    |   L    |  68H   |   C    |   A    | AFN=17H|  SEQ   |
+--------+--------+--------+--------+--------+--------+--------+--------+
| 信息点标识 | 数据标识编码 | 数据标识内容 |
+--------+--------+--------+--------+--------+--------+--------+--------+
| PW(见本部分6.1.7) | Tp(见本部分6.1.8) | CS | 16H |
+--------+--------+--------+--------+--------+--------+--------+--------+
```

---

## 附录F 错误代码定义

### F.1 错误代码定义

出错否定代码 ERR定义见下表F.1：

**表F.1 出错否定代码ERR 定义**

| ERR | 说明 |
|-----|------|
| 00H | 正确 |
| 01H | 中继命令没有返回 |
| 02H | 设置内容非法 |
| 03H | 密码权限不足 |
| 04H | 无此数据项 |
| 05H | 命令时间失效 |
| 06H | 目标地址不存在 |
| 07H | 校验失败 |

**错误代码说明：**

- **00H - 正确**：操作成功执行，无错误
- **01H - 中继命令没有返回**：中继转发命令执行失败，未收到响应
- **02H - 设置内容非法**：参数设置内容不符合规范要求
- **03H - 密码权限不足**：密码验证失败或权限不足
- **04H - 无此数据项**：请求的数据项不存在
- **05H - 命令时间失效**：命令执行时间已过期或无效
- **06H - 目标地址不存在**：指定的目标地址无效或不存在
- **07H - 校验失败**：数据校验或验证失败

---

*注：本文档将持续更新，记录南网上行规约协议的完整规范信息*
