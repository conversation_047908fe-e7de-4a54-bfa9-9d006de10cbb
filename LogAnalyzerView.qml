import QtQuick 2.12
import QtQuick.Layouts 1.12
import Qt.labs.platform 1.1
import QtQuick.Controls 2.12
import QtQuick.Dialogs 1.3

/**
 * @brief 日志分析器视图组件
 * @details 提供日志文件的加载、显示和处理功能
 * @input termDiskPath - termdisk路径
 * @output logFilesList - 处理后的日志文件列表
 */
Item {
    id: logAnalyzerRoot
    anchors.fill: parent
    objectName: "logAnalyzerView"

    // 添加背景色用于调试
    Rectangle {
        anchors.fill: parent
        color: "#f5f7fa"
        z: -1
    }
    
    // 属性定义
    property var progressDialog: null
    property var resultDialog: null
    property bool windowVisible: false
    property string termDiskPath: ""
    property var logFilesList: []
    // 组件加载完成时的处理
    Component.onCompleted: {
        console.log("LogAnalyzerView组件加载完成");
        console.log("LogAnalyzerView尺寸:", width, "x", height);
        console.log("LogAnalyzerView可见性:", visible);
        console.log("LogAnalyzerView termDiskPath:", termDiskPath);
        console.log("LogAnalyzerView windowVisible:", windowVisible);
        
        // 初始化对话框属性
        progressDialog = progressDialogComponent;
        resultDialog = resultDialogComponent;
        
        // 确保progressDialog和resultDialog已经初始化
        if (!progressDialog) {
            console.error("progressDialog初始化失败");
        }
        
        if (!resultDialog) {
            console.error("resultDialog初始化失败");
        }
        
        // 检查logs目录中是否有日志文件
        if (termDiskPath && termDiskPath.length > 0 && typeof archiveHandler !== "undefined" && archiveHandler) {
            var parentDir = termDiskPath.substring(0, termDiskPath.lastIndexOf("/"));
            var logsDir = parentDir + "/logs";
            var hasLogs = archiveHandler.checkLogsExist(logsDir);
            
            if (!hasLogs) {
                // 没有日志文件，执行bat文件
                console.log("logs目录中没有日志文件，自动执行bat文件");
                
                // 显示等待框
                progressDialog.messageText = "正在处理日志文件，请稍候...";
                progressDialog.open();
                
                // 执行bat文件
                archiveHandler.processLogsWithBat();
            } else {
                // 有日志文件，加载列表
                console.log("logs目录中已有日志文件，加载日志列表");
                loadLogFilesList();
            }
        } else {
            console.log("termDiskPath为空或archiveHandler不可用，无法检查日志");
        }
    }
    // 属性变化处理
    onWindowVisibleChanged: {
        if (windowVisible) {
            // 检查logs目录中是否有日志文件
            if (termDiskPath && termDiskPath.length > 0 && typeof archiveHandler !== "undefined" && archiveHandler) {
                var parentDir = termDiskPath.substring(0, termDiskPath.lastIndexOf("/"));
                var logsDir = parentDir + "/logs";
                var hasLogs = archiveHandler.checkLogsExist(logsDir);
                
                if (!hasLogs) {
                    // 没有日志文件，执行bat文件
                    console.log("logs目录中没有日志文件，自动执行bat文件");
                    
                    // 显示等待框
                    progressDialog.messageText = "正在处理日志文件，请稍候...";
                    progressDialog.open();
                    
                    // 执行bat文件
                    archiveHandler.processLogsWithBat();
                } else {
                    // 有日志文件，加载列表
                    console.log("logs目录中已有日志文件，加载日志列表");
                    loadLogFilesList();
                }
            } else {
                console.log("termDiskPath为空或archiveHandler不可用，无法检查日志");
            }
            
            refreshCustomScripts(); // 刷新自定义脚本按钮
        }
    }
    
    onTermDiskPathChanged: {
        if (termDiskPath && windowVisible) 
        {
            loadLogFilesList();
        }
    }
    
    // 进度对话框组件
    Popup {
        id: progressDialogComponent
        width: 450
        height: 220
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2
        modal: false
        closePolicy: Popup.NoClose
        
        property string messageText: "正在处理日志文件，请稍候..."
        property color headerColor: "#0078d7"
        property string title: "日志处理中"
        
        background: Rectangle {
            color: "#ffffff"
            border.color: "#dddddd"
            border.width: 1
            radius: 5
        }
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 0
            
            // 自定义标题栏
            Rectangle {
                Layout.fillWidth: true
                height: 40
                color: progressDialogComponent.headerColor
                radius: 5
                
                Label {
                    text: progressDialogComponent.title
                    color: "white"
                    font.bold: true
                    font.pixelSize: 14
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.left: parent.left
                    anchors.leftMargin: 15
                }
            }
            
            // 内容区域
            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 15
                spacing: 20
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 15
                    
                    BusyIndicator {
                        running: true
                        Layout.alignment: Qt.AlignVCenter
                    }
                    
                    Label {
                        text: progressDialogComponent.messageText
                        Layout.fillWidth: true
                        wrapMode: Text.Wrap
                        font.bold: true
                        font.pixelSize: 14
                    }
                }
                
                Rectangle {
                    Layout.fillWidth: true
                    height: 1
                    color: "#eeeeee"
                }
                
                Label {
                    text: "日志处理正在后台进行，您可以继续使用其他功能"
                    Layout.fillWidth: true
                    horizontalAlignment: Text.AlignHCenter
                    font.italic: true
                    color: progressDialogComponent.headerColor
                }
                
                Label {
                    text: "处理完成后将自动通知您并刷新日志列表"
                    Layout.fillWidth: true
                    horizontalAlignment: Text.AlignHCenter
                    font.italic: true
                }
                
                Button {
                    text: "在后台继续处理"
                    Layout.alignment: Qt.AlignHCenter
                    Layout.preferredWidth: 150
                    
                    background: Rectangle {
                        color: parent.down ? Qt.darker(progressDialogComponent.headerColor, 1.2) : 
                              (parent.hovered ? Qt.darker(progressDialogComponent.headerColor, 1.1) : progressDialogComponent.headerColor)
                        radius: 4
                    }
                    
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                    
                    onClicked: {
                        progressDialogComponent.close();
                    }
                }
            }
        }
    }
    
    // 结果对话框组件
    Popup {
        id: resultDialogComponent
        width: 450
        height: 220
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2
        modal: true
        closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
        
        property bool success: false
        property string messageText: ""
        property string title: success ? "处理成功" : "处理失败"
        
        background: Rectangle {
            color: "#ffffff"
            border.color: "#dddddd"
            border.width: 1
            radius: 5
        }
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 0
            
            // 自定义标题栏
            Rectangle {
                Layout.fillWidth: true
                height: 40
                color: resultDialogComponent.success ? "#4CAF50" : "#F44336"
                radius: 5
                
                Label {
                    text: resultDialogComponent.success ? "处理成功" : "处理失败"
                    color: "white"
                    font.bold: true
                    font.pixelSize: 14
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.left: parent.left
                    anchors.leftMargin: 15
                }
            }
            
            // 内容区域
            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 15
                spacing: 20
                
                Label {
                    text: resultDialogComponent.messageText
                    Layout.fillWidth: true
                    wrapMode: Text.Wrap
                    horizontalAlignment: Text.AlignHCenter
                    font.pixelSize: 14
                }
                
                RowLayout {
                    Layout.alignment: Qt.AlignHCenter
                    spacing: 20
                    
                    Button {
                        text: "刷新列表"
                        visible: resultDialogComponent.success
                        
                        background: Rectangle {
                            color: parent.down ? "#388E3C" : (parent.hovered ? "#43A047" : "#4CAF50")
                            radius: 4
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        
                        onClicked: {
                            loadLogFilesList();
                            resultDialogComponent.accept();
                        }
                    }
                    
                    Button {
                        text: "确定"
                        
                        background: Rectangle {
                            color: parent.down ? "#0069c0" : (parent.hovered ? "#0078d7" : "#0086f0")
                            radius: 4
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                        
                        onClicked: {
                            resultDialogComponent.accept();
                        }
                    }
                }
            }
        }
        
        function accept() {
            close();
            loadLogFilesList();
        }
    }
    
    // UI组件
    Label {
        id: statusLabel
        anchors {
            top: parent.top
            left: parent.left
            right: parent.right
            margins: 10
        }
        text: termDiskPath ?
            qsTr("termdisk路径: ") + termDiskPath :
            qsTr("未设置termdisk路径 - LogAnalyzerView已加载")
        font.pixelSize: 12
        elide: Text.ElideMiddle
        wrapMode: Text.Wrap
        color: "#333333"

        Component.onCompleted: {
            console.log("statusLabel已创建，文本:", text);
        }
    }
    
    // 操作按钮区域
    RowLayout {
        id: buttonRow
        anchors {
            top: statusLabel.bottom
            left: parent.left
            right: parent.right
            margins: 10
        }
        spacing: 10
        
        // 默认日志脚本按钮
        Button {
            id: manualMergeButton
            text: qsTr("默认日志脚本")
            Layout.preferredWidth: 120
            
            background: Rectangle {
                color: parent.down ? "#D84315" : (parent.hovered ? "#E64A19" : "#FF5722")
                radius: 4
            }
            
            contentItem: Text {
                text: parent.text
                color: "white"
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: 12
            }
            
            onClicked: {
                if (termDiskPath.length > 0 && typeof archiveHandler !== "undefined" && archiveHandler) {
                    console.log("执行默认日志脚本");
                    // 调用C++端的方法处理日志，生成的文件会覆盖logs目录中的同名文件
                    archiveHandler.processLogsWithBat();
                } else {
                    console.log("termDiskPath为空，无法处理日志");
                    
                    // 显示错误对话框
                    var errorDialog = Qt.createQmlObject('
                        import QtQuick 2.12
                        import QtQuick.Controls 2.12
                        import QtQuick.Layouts 1.12
                        
                        Popup {
                            id: errorDialogPopup
                            width: 400
                            height: 150
                            x: (parent.width - width) / 2
                            y: (parent.height - height) / 2
                            parent: logAnalyzerRoot
                            modal: true
                            closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
                            
                            property string title: "错误"
                            
                            background: Rectangle {
                                color: "#ffffff"
                                border.color: "#dddddd"
                                border.width: 1
                                radius: 5
                            }
                            
                            ColumnLayout {
                                anchors.fill: parent
                                spacing: 0
                                
                                // 自定义标题栏
                                Rectangle {
                                    Layout.fillWidth: true
                                    height: 40
                                    color: "#F44336"
                                    radius: 5
                                    
                                    Label {
                                        text: errorDialogPopup.title
                                        color: "white"
                                        font.bold: true
                                        font.pixelSize: 14
                                        anchors.verticalCenter: parent.verticalCenter
                                        anchors.left: parent.left
                                        anchors.leftMargin: 15
                                    }
                                }
                                
                                // 内容区域
                                ColumnLayout {
                                    Layout.fillWidth: true
                                    Layout.fillHeight: true
                                    Layout.margins: 15
                                    spacing: 20
                                    
                                    Label {
                                        text: "未设置termdisk路径，无法处理日志"
                                        Layout.fillWidth: true
                                        wrapMode: Text.Wrap
                                        horizontalAlignment: Text.AlignHCenter
                                    }
                                    
                                    Button {
                                        text: "确定"
                                        Layout.alignment: Qt.AlignHCenter
                                        
                                        onClicked: {
                                            errorDialogPopup.close();
                                            errorDialogPopup.destroy();
                                        }
                                    }
                                }
                            }
                            
                            Component.onCompleted: open()
                        }
                    ', logAnalyzerRoot, "errorDialog");
                }
            }
        }
        
        // 自定义脚本按钮区域
        Flow {
            id: customScriptsFlow
            Layout.fillWidth: true
            spacing: 10
            
            // 这里将动态添加自定义脚本按钮
            Component.onCompleted: {
                // 调用刷新自定义脚本按钮函数
                refreshCustomScripts();
            }
        }
    }

    // 内容区域中的标题行和列表视图
    Rectangle {
        id: contentArea
        anchors {
            top: buttonRow.bottom
            left: parent.left
            right: parent.right
            bottom: parent.bottom
            margins: 10
        }
        border.color: "#dddddd"
        border.width: 1

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 10
            spacing: 10
            
            // 标题行
            Rectangle {
                Layout.fillWidth: true
                height: 40
                color: "#f0f0f0"
                
                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 10
                    
                    Label {
                        text: qsTr("日志文件")
                        font.bold: true
                        Layout.fillWidth: true
                        Layout.preferredWidth: 7
                    }
                    
                    Label {
                        text: qsTr("操作")
                        font.bold: true
                        Layout.preferredWidth: 3
                        horizontalAlignment: Text.AlignHCenter
                    }
                }
            }
            
            // 日志文件列表
            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true
                clip: true
                
                ListView {
                    id: logFilesListView
                    anchors.fill: parent
                    model: logFilesList
                    spacing: 5
                    
                    delegate: Rectangle {
                        width: logFilesListView.width
                        height: 60
                        color: index % 2 === 0 ? "#f9f9f9" : "#ffffff"
                        border.color: "#eeeeee"
                        border.width: 1
                        radius: 3
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 10
                            spacing: 10

                            // 文件名/路径列
                            ColumnLayout {
                                Layout.fillWidth: true
                                Layout.preferredWidth: 7
                                spacing: 2
                                
                                Label {
                                    text: modelData.name
                                    font.bold: true
                                    Layout.fillWidth: true
                                    elide: Text.ElideMiddle
                                    font.pixelSize: 13
                                }
                                
                                Label {
                                    text: modelData.path
                                    Layout.fillWidth: true
                                    elide: Text.ElideMiddle
                                    font.pixelSize: 11
                                    color: "#666666"
                                    
                                    // 添加工具提示，鼠标悬停时显示完整路径
                                    ToolTip.visible: filePathMouseArea.containsMouse
                                    ToolTip.text: modelData.path
                                    
                                    MouseArea {
                                        id: filePathMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                    }
                                }
                            }
                            
                            // 操作按钮区域
                            RowLayout {
                                spacing: 10
                                Layout.preferredWidth: 3
                                
                                Button {
                                    text: qsTr("打开")
                                    Layout.preferredWidth: 70
                                    
                                    background: Rectangle {
                                        color: parent.down ? "#005fa3" : (parent.hovered ? "#0078d7" : "#0086f0")
                                        radius: 4
                                    }
                                    
                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                        font.pixelSize: 12
                                    }
                                    
                                    onClicked: {
                                        var filePath = modelData.path;
                                        console.log("用Notepad++打开文件:", filePath);
                                        
                                        // 调用Notepad++打开文件
                                        openFileWithNotepad(filePath);
                                    }
                                }
                                
                                Button {
                                    text: qsTr("目录")
                                    Layout.preferredWidth: 70
                                    
                                    background: Rectangle {
                                        color: parent.down ? "#616161" : (parent.hovered ? "#757575" : "#9E9E9E")
                                        radius: 4
                                    }
                                    
                                    contentItem: Text {
                                        text: parent.text
                                        color: "white"
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                        font.pixelSize: 12
                                    }
                                    
                                    onClicked: {
                                        var filePath = modelData.path;
                                        console.log("打开文件所在目录:", filePath);
                                        
                                        // 调用系统方法打开文件所在目录
                                        if (typeof archiveHandler !== "undefined" && archiveHandler &&
                                            typeof archiveHandler.openFileDirectory === "function") {
                                            archiveHandler.openFileDirectory(filePath);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 空状态显示
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.width * 0.8
                        height: 150
                        color: "#f5f5f5"
                        visible: logFilesListView.count === 0
                        radius: 5
                        
                        ColumnLayout {
                            anchors.centerIn: parent
                            spacing: 10
                            
                            Text {
                                text: qsTr("暂无日志文件")
                                font.pixelSize: 16
                                Layout.alignment: Qt.AlignHCenter
                                color: "#888888"
                            }
                            
                            Text {
                                text: termDiskPath ? qsTr("点击合并默认日志按钮处理日志文件") : qsTr("请先选择一个包含termdisk的压缩文件")
                                font.pixelSize: 14
                                Layout.alignment: Qt.AlignHCenter
                                color: "#0078d7"
                                font.italic: true
                            }
                            
                            Button {
                                text: qsTr("刷新列表")
                                Layout.alignment: Qt.AlignHCenter
                                Layout.topMargin: 10
                                
                                background: Rectangle {
                                    color: parent.down ? "#005fa3" : (parent.hovered ? "#0078d7" : "#0086f0")
                                    radius: 4
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: "white"
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    font.pixelSize: 12
                                }
                                
                                onClicked: loadLogFilesList()
                            }
                        }
                    }
                }
            }
        }
    }
    // 文件选择对话框
    FileDialog {
        id: fileDialog
        title: qsTr("选择日志文件")
        folder: termDiskPath ? "file:///" + termDiskPath : StandardPaths.standardLocations(StandardPaths.DocumentsLocation)[0]
        nameFilters: [qsTr("日志文件 (*.log *.txt)"), qsTr("所有文件 (*)")]
        onAccepted: {
            var filePath = fileDialog.file.toString();
            console.log("选择的文件路径: " + filePath);
            
            // 将文件路径格式化为本地路径
            var localFilePath = formatLocalPath(filePath);
            
            // 检查文件是否已存在于列表中
            var fileExists = false;
            for (var i = 0; i < logFilesList.length; i++) {
                if (logFilesList[i].path === localFilePath) {
                    fileExists = true;
                    break;
                }
            }
            
            // 如果文件不在列表中，则添加
            if (!fileExists) {
                logFilesList.push({
                    path: localFilePath,
                    name: localFilePath.split('/').pop(),
                    timestamp: new Date().getTime()
                });
                logFilesListView.model = logFilesList;
            }
        }
    }
    
    // 加载日志文件列表
    /**
     * @brief 加载日志文件列表
     * @details 从termdisk的父目录中加载日志文件列表
     * @input termDiskPath - termdisk路径
     * @output logFilesList - 加载后的日志文件列表
     */
    function loadLogFilesList() {
        console.log("开始加载日志文件列表");
        
        // 初始化为空列表
        logFilesList = [];
        console.log("已清空日志文件列表");
        
        // 检查termdisk的父目录中是否有logs目录，这是BAT脚本创建的
        if (termDiskPath) {
            console.log("termDiskPath存在:", termDiskPath);
            var parentDir = termDiskPath.substring(0, termDiskPath.lastIndexOf("/"));
            var batLogsDir = parentDir + "/logs";
            console.log("检查BAT脚本创建的logs目录:", batLogsDir);
            
            if (typeof archiveHandler !== "undefined" && archiveHandler) {
                console.log("archiveHandler对象可用");
                // 先检查logs目录是否存在日志文件
                var hasLogs = archiveHandler.checkLogsExist(batLogsDir);
                console.log("logs目录中是否存在日志文件:", hasLogs);
                
                if (hasLogs) {
                    console.log("logs目录中存在日志文件，加载日志列表");
                    var batLogFiles = archiveHandler.listLogFiles(batLogsDir);
                    console.log("获取到的日志文件数量:", batLogFiles ? batLogFiles.length : 0);
                    
                    // 定义所有可能的日志类型
                    var allLogTypes = [
                        "user.log", "user_err.log",
                        "gprs0", "gprs0frame", "gprs1", "gprs1frame", 
                        "pppd", "irsevdiag", "irsevframe", 
                        "watchgprs", "commdiag", "commframe"
                    ];
                    
                    for (var i = 0; i < batLogFiles.length; i++) {
                        var filePath = batLogFiles[i];
                        var fileName = filePath.split('/').pop();
                        var displayName = fileName;
                        
                        // 根据文件名确定合适的显示名称
                        if (fileName === "user.log.amalgamation.log") {
                            displayName = "用户日志文件 (合并)";
                        } else if (fileName === "user_err.log.amalgamation.log") {
                            displayName = "错误日志文件 (合并)";
                        } else {
                            // 检查是否为通信日志文件
                            for (var j = 0; j < allLogTypes.length; j++) {
                                var logType = allLogTypes[j];
                                if (fileName === logType + ".amalgamation.log") {
                                    displayName = logType + " 通信日志文件 (合并)";
                                    break;
                                }
                            }
                        }
                        
                        // 获取文件大小和修改时间
                        var fileInfo = getFileInfo(filePath);
                        
                        logFilesList.push({
                            path: filePath,
                            name: displayName,
                            size: fileInfo.size,
                            modified: fileInfo.modified,
                            timestamp: new Date().getTime() - i // 使用递减的时间戳确保顺序
                        });
                        
                        console.log("添加日志文件:", filePath, "显示为:", displayName, "大小:", fileInfo.size, "修改时间:", fileInfo.modified);
                    }
                    
                    // 按修改时间排序，最新的在前面
                    logFilesList.sort(function(a, b) {
                        return b.modified - a.modified;
                    });
                    console.log("日志文件列表排序完成，共", logFilesList.length, "个文件");
                } else {
                    console.log("logs目录中没有找到日志文件");
                    
                    // 只在窗口可见时才自动调用loghandle.bat处理日志
                    if (windowVisible && typeof archiveHandler !== "undefined" && archiveHandler && typeof archiveHandler.processLogsWithBat === "function") {
                        console.log("自动执行loghandle.bat处理日志");
                        // 显示进度对话框
                        if (progressDialog) {
                            progressDialog.messageText = "正在处理日志文件，请稍候...";
                            progressDialog.open();
                        }
                        // 调用C++方法处理日志
                        archiveHandler.processLogsWithBat();
                    } else {
                        console.log("日志窗口不可见或无法调用processLogsWithBat函数，跳过自动处理日志");
                        console.log("windowVisible:", windowVisible);
                        console.log("archiveHandler可用:", typeof archiveHandler !== "undefined" && archiveHandler);
                        console.log("processLogsWithBat函数可用:", typeof archiveHandler !== "undefined" && archiveHandler && typeof archiveHandler.processLogsWithBat === "function");
                    }
                }
            } else {
                console.log("archiveHandler对象不可用");
            }
        } else {
            console.log("termDiskPath为空");
        }
        
        // 更新ListView的模型
        console.log("更新ListView模型，日志文件数量:", logFilesList.length);
        logFilesListView.model = logFilesList;
    }
    
    // 获取文件信息（大小和修改时间）
    /**
     * @brief 获取文件信息
     * @details 从C++端获取文件的大小和修改时间
     * @input filePath - 文件的完整路径
     * @output fileInfo - 包含文件大小和修改时间的对象
     */
    function getFileInfo(filePath) {
        var fileInfo = {
            size: "未知",
            modified: 0
        };
        
        if (typeof archiveHandler !== "undefined" && archiveHandler) {
            try {
                var info = archiveHandler.getFileInfo(filePath);
                if (info) {
                    fileInfo.size = formatFileSize(info.size);
                    fileInfo.modified = info.lastModified;
                }
            } catch (e) {
                console.error("获取文件信息失败:", e);
            }
        }
        
        return fileInfo;
    }
    
    // 格式化文件大小
    /**
     * @brief 格式化文件大小
     * @details 将字节数格式化为KB、MB或GB
     * @input bytes - 字节数
     * @output formattedSize - 格式化后的文件大小字符串
     */
    function formatFileSize(bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return (bytes / 1024).toFixed(2) + " KB";
        } else if (bytes < 1024 * 1024 * 1024) {
            return (bytes / (1024 * 1024)).toFixed(2) + " MB";
        } else {
            return (bytes / (1024 * 1024 * 1024)).toFixed(2) + " GB";
        }
    }
    
    // 处理C++端合并日志的信号
    /**
     * @brief 处理日志合并完成通知
     * @details 当C++端完成日志合并时，更新日志文件列表
     * @input outputFilePath - 合并后的文件路径
     */
    function handleLogFileMerged(outputFilePath) {
        console.log("收到日志合并完成通知，合并文件路径:", outputFilePath);
        
        // 将文件路径格式化为本地路径
        var localFilePath = formatLocalPath(outputFilePath);
        var fileName = localFilePath.split('/').pop();
        var displayName = fileName;
        
        // 定义通信日志类型
        var commLogTypes = [
            "gprs0", "gprs0frame", "irsevdiag", "jldiag", 
            "ndwdiag", "pppd", "watchgprs"
        ];
        
        // 根据文件名确定日志类型
        if (fileName.includes("user_err")) {
            displayName = "错误日志文件 (合并)";
        } else if (fileName.includes("user_logs")) {
            displayName = "用户日志文件 (合并)";
        } else {
            // 检查是否为合并后的通信日志文件
            for (var i = 0; i < commLogTypes.length; i++) {
                var logType = commLogTypes[i];
                if (fileName.includes(logType)) {
                    displayName = logType + " 通信日志文件 (合并)";
                    break;
                }
            }
        }
        
        // 更新日志文件列表
        // 检查文件是否已存在于列表中
        var fileExists = false;
        var existingIndex = -1;
        
        for (var i = 0; i < logFilesList.length; i++) {
            if (logFilesList[i].path === localFilePath) {
                fileExists = true;
                existingIndex = i;
                break;
            }
        }
        
        // 如果文件已存在，则更新它
        if (fileExists) {
            logFilesList[existingIndex] = {
                path: localFilePath,
                name: displayName,
                timestamp: new Date().getTime()
            };
        } else {
            // 否则添加到列表
            logFilesList.push({
                path: localFilePath,
                name: displayName,
                timestamp: new Date().getTime()
            });
        }
        
        // 更新列表视图
        logFilesListView.model = logFilesList;
    }
    
    // 处理C++端读取的日志文件内容
    /**
     * @brief 处理日志文件内容
     * @details 当C++端读取日志文件内容时，创建一个窗口显示内容
     * @input filePath - 文件的完整路径
     * @input content - 文件的文本内容
     */
    function handleLogFileContent(filePath, content) {
        console.log("收到日志文件内容，路径:", filePath, "内容长度:", content.length);
        
        // 将文件路径格式化为本地路径
        var localFilePath = formatLocalPath(filePath);
        
        // 创建一个新窗口显示日志内容
        var component = Qt.createComponent("LogContentWindow.qml");
        if (component.status === Component.Ready) {
            var logWindow = component.createObject(logAnalyzerRoot, {
                "filePath": localFilePath,
                "content": content,
                "width": 800,
                "height": 600
            });
            if (logWindow === null) {
                // 创建对象失败
                console.error("创建LogContentWindow对象失败");
                showErrorDialog("无法创建日志内容窗口");
            } else {
                logWindow.show();
            }
        } else if (component.status === Component.Error) {
            // 组件创建失败
            console.error("创建LogContentWindow组件失败:", component.errorString());
            showErrorDialog("无法创建日志内容窗口: " + component.errorString());
        } else {
            // 组件加载未完成，等待完成
            component.statusChanged.connect(function() {
                if (component.status === Component.Ready) {
                    var logWindow = component.createObject(logAnalyzerRoot, {
                        "filePath": localFilePath,
                        "content": content,
                        "width": 800,
                        "height": 600
                    });
                    if (logWindow === null) {
                        console.error("创建LogContentWindow对象失败");
                        showErrorDialog("无法创建日志内容窗口");
                    } else {
                        logWindow.show();
                    }
                } else if (component.status === Component.Error) {
                    console.error("创建LogContentWindow组件失败:", component.errorString());
                    showErrorDialog("无法创建日志内容窗口: " + component.errorString());
                }
            });
        }
    }
    
    // 处理批处理开始的信号
    function handleBatchProcessStarted(message) {
        console.log("LogAnalyzerView收到批处理开始信号:", message);
        
        // 显示进度对话框
        if (progressDialog) {
            progressDialog.messageText = message;
            progressDialog.open();
        }
    }
    
    // 处理批处理结束的信号
    function handleBatchProcessFinished(success, message) {
        console.log("LogAnalyzerView收到批处理结束信号:", success, message);
        
        // 关闭进度对话框
        if (progressDialog && progressDialog.visible) {
            progressDialog.close();
        }
        
        // 显示结果对话框
        if (resultDialog) {
            resultDialog.success = success;
            resultDialog.messageText = message;
            resultDialog.open();
        }
        
        // 刷新日志文件列表
        loadLogFilesList();
    }
    
    // 辅助函数：格式化本地路径
    /**
     * @brief 格式化本地路径
     * @details 将Qt的file://前缀转换为本地路径
     * @input filePath - 文件的完整路径
     * @output localFilePath - 格式化后的本地路径
     */
    function formatLocalPath(filePath) {
        var localFilePath = filePath;
        if (localFilePath.startsWith("file:///")) {
            localFilePath = localFilePath.substring(8); // 去掉"file:///"前缀
        } else if (localFilePath.startsWith("file://")) {
            localFilePath = localFilePath.substring(7); // 去掉"file://"前缀
        }
        return localFilePath;
    }
    
    // 使用Notepad++打开文件
    /**
     * @brief 使用Notepad++打开文件
     * @details 尝试使用Notepad++打开指定的文件
     * @input filePath - 文件的完整路径
     */
    function openFileWithNotepad(filePath) {
        if (typeof archiveHandler !== "undefined" && archiveHandler) {
            // 尝试使用相对路径
            var notepadPath = "./tools/npp.8.6.1.portable.minimalist.x64/notepad++.exe";
            
            // 如果相对路径不成功，C++代码会尝试使用这个固定路径作为备用
            // D:/Documents/QT/NW_ManageTools/tools/npp.8.6.1.portable.minimalist.x64/notepad++.exe
            
            console.log("Notepad++相对路径:", notepadPath);
            console.log("打开文件:", filePath);
            
            // 调用C++方法打开文件
            var success = archiveHandler.openFileWithApp(notepadPath, filePath);
            if (!success) {
                console.log("打开Notepad++失败，请确保工具路径正确");
                
                // 创建一个错误对话框
                var errorDialog = Qt.createQmlObject('
                    import QtQuick 2.12
                    import QtQuick.Controls 2.12
                    import QtQuick.Layouts 1.12
                    
                    Popup {
                        id: errorDialogPopup
                        width: 400
                        height: 150
                        x: (parent.width - width) / 2
                        y: (parent.height - height) / 2
                        parent: logAnalyzerRoot
                        modal: true
                        closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
                        
                        property string title: "错误"
                        
                        background: Rectangle {
                            color: "#ffffff"
                            border.color: "#dddddd"
                            border.width: 1
                            radius: 5
                        }
                        
                        ColumnLayout {
                            anchors.fill: parent
                            spacing: 0
                            
                            // 自定义标题栏
                            Rectangle {
                                Layout.fillWidth: true
                                height: 40
                                color: "#F44336"
                                radius: 5
                                
                                Label {
                                    text: errorDialogPopup.title
                                    color: "white"
                                    font.bold: true
                                    font.pixelSize: 14
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.left: parent.left
                                    anchors.leftMargin: 15
                                }
                            }
                            
                            // 内容区域
                            ColumnLayout {
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                Layout.margins: 15
                                spacing: 20
                                
                                Label {
                                    text: "无法打开文件，请确保Notepad++安装路径正确"
                                    Layout.fillWidth: true
                                    wrapMode: Text.Wrap
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                
                                Button {
                                    text: "确定"
                                    Layout.alignment: Qt.AlignHCenter
                                    
                                    onClicked: {
                                        errorDialogPopup.close();
                                        errorDialogPopup.destroy();
                                    }
                                }
                            }
                        }
                        
                        Component.onCompleted: open()
                    }
                ', logAnalyzerRoot, "errorDialog");
                
                if (!errorDialog) {
                    console.error("创建错误对话框失败");
                }
            }
        }
    }
    // 处理日志进度对话框
    Popup {
        id: progressDialog
        width: 400
        height: 150
        anchors.centerIn: parent
        modal: false
        closePolicy: Popup.NoClose
        
        property string title: qsTr("处理日志")
        property string messageText: qsTr("正在处理日志文件，请稍候...")
        property color headerColor: "#0078d7"
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 0
            
            // 自定义标题栏
            Rectangle {
                Layout.fillWidth: true
                height: 40
                color: progressDialog.headerColor
                radius: 5
                
                Label {
                    text: progressDialog.title
                    color: "white"
                    font.bold: true
                    font.pixelSize: 14
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.left: parent.left
                    anchors.leftMargin: 10
                }
            }
            
            // 内容区域
            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 10
                spacing: 10
                
                BusyIndicator {
                    running: true
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Label {
                    text: progressDialog.messageText
                    Layout.fillWidth: true
                    horizontalAlignment: Text.AlignHCenter
                    wrapMode: Text.Wrap
                } 
            }
        }
    }
    
    // 通知对话框
    Popup {
        id: notificationDialog
        width: 400
        height: 150
        anchors.centerIn: parent
        modal: true
        closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
        
        property string title: qsTr("通知")
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 0
            
            // 自定义标题栏
            Rectangle {
                Layout.fillWidth: true
                height: 40
                color: "#f0f0f0"
                
                Label {
                    id: notificationTitle
                    text: qsTr("操作完成")
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.left: parent.left
                    anchors.leftMargin: 10
                    font.bold: true
                    font.pixelSize: 14
                    color: "#333333"
                }
            }
            
            // 内容区域
            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                Layout.margins: 10
                spacing: 10
                
                Label {
                    id: notificationText
                    text: qsTr("操作已完成")
                    Layout.fillWidth: true
                    horizontalAlignment: Text.AlignHCenter
                    wrapMode: Text.Wrap
                }
                
                Button {
                    text: qsTr("确定")
                    Layout.alignment: Qt.AlignHCenter
                    
                    
                    onClicked: {
                        notificationDialog.close();
                    }
                }
            }
        }
    }
    
    // 添加显示错误对话框的辅助函数
    /**
     * @brief 显示错误对话框
     * @details 创建一个错误对话框并显示错误消息
     * @input errorMessage - 错误消息
     */
    function showErrorDialog(errorMessage) {
        console.log("显示错误对话框:", errorMessage);
        var errorDialog = Qt.createQmlObject('
            import QtQuick 2.12
            import QtQuick.Controls 2.12
            import QtQuick.Layouts 1.12
            
            Popup {
                id: errorDialogPopup
                width: 400
                height: 150
                x: (parent.width - width) / 2
                y: (parent.height - height) / 2
                parent: logAnalyzerRoot
                modal: true
                closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
                
                property string title: "错误"
                
                background: Rectangle {
                    color: "#ffffff"
                    border.color: "#dddddd"
                    border.width: 1
                    radius: 5
                }
                
                ColumnLayout {
                    anchors.fill: parent
                    spacing: 0
                    
                    // 自定义标题栏
                    Rectangle {
                        Layout.fillWidth: true
                        height: 40
                        color: "#F44336"
                        radius: 5
                        
                        Label {
                            text: errorDialogPopup.title
                            color: "white"
                            font.bold: true
                            font.pixelSize: 14
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left: parent.left
                            anchors.leftMargin: 15
                        }
                    }
                    
                    // 内容区域
                    ColumnLayout {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.margins: 15
                        spacing: 20
                        
                        Label {
                            text: "' + errorMessage + '"
                            Layout.fillWidth: true
                            wrapMode: Text.Wrap
                            horizontalAlignment: Text.AlignHCenter
                        }
                        
                        Button {
                            text: "确定"
                            Layout.alignment: Qt.AlignHCenter
                            
                            onClicked: {
                                errorDialogPopup.close();
                                errorDialogPopup.destroy();
                            }
                        }
                    }
                }
                
                Component.onCompleted: open()
            }
        ', logAnalyzerRoot);
        
        if (!errorDialog) {
            console.error("创建错误对话框失败");
        }
    }
    
    // 拷贝新日志文件到logs目录
    /**
     * @brief 拷贝新日志文件到logs目录
     * @details 如果C++端没有实现copyNewLogsToLogsDir方法，则在QML端处理
     * @input logsDir - logs目录路径
     */
    function copyNewLogsToLogsDir(logsDir) {
        console.log("在QML端处理日志文件拷贝，目标目录:", logsDir);
        
        // 检查archiveHandler是否存在
        if (typeof archiveHandler === "undefined" || !archiveHandler) {
            console.error("archiveHandler不存在，无法执行拷贝操作");
            return;
        }
        
        // 获取termdisk的父目录
        var parentDir = "";
        if (termDiskPath) {
            parentDir = termDiskPath.substring(0, termDiskPath.lastIndexOf("/"));
        } else {
            console.error("termDiskPath为空，无法确定源目录位置");
            return;
        }
        
        // 可能的源目录，这些是BAT脚本可能生成日志文件的位置
        var sourceDirs = [
            parentDir + "/output",
            parentDir + "/temp",
            parentDir + "/logs_temp"
        ];
        
        // 检查源目录是否存在
        var sourceDir = "";
        for (var i = 0; i < sourceDirs.length; i++) {
            // 这里需要C++端提供一个方法来检查目录是否存在
            if (typeof archiveHandler.directoryExists === "function" && 
                archiveHandler.directoryExists(sourceDirs[i])) {
                sourceDir = sourceDirs[i];
                break;
            }
        }
        
        if (!sourceDir) {
            console.error("未找到源目录，无法执行拷贝操作");
            return;
        }
        
        console.log("找到源目录:", sourceDir);
        
        // 确保目标目录存在
        if (typeof archiveHandler.ensureDirectoryExists === "function") {
            archiveHandler.ensureDirectoryExists(logsDir);
        }
        
        // 拷贝日志文件
        if (typeof archiveHandler.copyFiles === "function") {
            // 拷贝所有.log和.amalgamation.log文件
            archiveHandler.copyFiles(sourceDir, logsDir, "*.log");
            archiveHandler.copyFiles(sourceDir, logsDir, "*.amalgamation.log");
            console.log("日志文件拷贝完成");
        } else {
            console.error("C++端未实现copyFiles方法，无法执行拷贝操作");
        }
    } 

    // 刷新自定义脚本按钮
    /**
     * @brief 刷新自定义脚本按钮
     * @details 清除并重新加载自定义脚本按钮
     */
    function refreshCustomScripts() {
        console.log("刷新自定义脚本按钮");
        
        // 清除现有的按钮
        for (var i = customScriptsFlow.children.length - 1; i >= 0; i--) {
            customScriptsFlow.children[i].destroy();
        }
        
        // 重新加载脚本按钮
        if (typeof archiveHandler !== "undefined" && archiveHandler) {
            var scripts = archiveHandler.listCustomScripts();
            if (scripts && scripts.length > 0) {
                console.log("找到自定义脚本数量:", scripts.length);
                
                // 遍历脚本列表并创建按钮
                for (var i = 0; i < scripts.length; i++) {
                    try {
                        // 解析脚本信息
                        var scriptInfo = JSON.parse(scripts[i]);
                        var scriptName = scriptInfo.name;
                        var scriptPath = scriptInfo.path;
                        
                        // 创建按钮
                        var buttonComponent = Qt.createComponent("CustomScriptButton.qml");
                        if (buttonComponent.status === Component.Ready) {
                            var button = buttonComponent.createObject(customScriptsFlow, {
                                "text": scriptName,
                                "scriptPath": scriptPath
                            });
                            
                            // 使用立即执行函数表达式(IIFE)解决闭包问题
                            (function(currentPath) {
                                button.scriptClicked.connect(function(path) {
                                    console.log("执行自定义脚本:", currentPath);
                                    if (termDiskPath.length > 0 && typeof archiveHandler !== "undefined" && archiveHandler) {
                                        archiveHandler.executeCustomScript(currentPath);
                                    } else {
                                        console.log("termDiskPath为空，无法执行脚本");
                                        showErrorDialog("未设置termdisk路径，无法执行脚本");
                                    }
                                });
                            })(scriptPath);
                            
                            console.log("创建自定义脚本按钮:", scriptName, scriptPath);
                        } else if (buttonComponent.status === Component.Error) {
                            console.error("创建按钮组件失败:", buttonComponent.errorString());
                        }
                    } catch (e) {
                        console.error("处理脚本信息时出错:", e);
                    }
                }
            } else {
                console.log("未找到自定义脚本");
            }
        }
    }
}
