import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import QtQuick.Dialogs 1.3
import Qt.labs.platform 1.1
import QtQml 2.12
import QtGraphicalEffects 1.0

Window {
    id: mainWindow
    width: 1440
    height: 800
    visible: true
    title: qsTr("南网工具集")
    color: "#f5f7fa"
    
    property string termDiskPath: ""
    
    // 处理lastExtractionReused属性变化的函数
    function lastExtractionReusedChanged(reused) {
        console.log("解压复用状态变化:", reused);
    }
    
    // 用于接收C++端extractionCompleted信号的函数
    function extractArchive(termDiskPath) {
        console.log("收到C++端解压完成信号，路径:", termDiskPath);

        // 确保关闭等待提示框
        if (waitingDialog.visible) {
            console.log("关闭等待提示框");
            waitingDialog.close();
        }

        mainWindow.termDiskPath = termDiskPath;
        if (termDiskPath.length > 0) {
            // 检查是否使用了已存在的目录
            if (archiveHandler.lastExtractionReused) {
                extractResultText.text = qsTr("使用已存在的目录: ") + termDiskPath;
            } else {
                extractResultText.text = qsTr("提取成功，termdisk路径: ") + termDiskPath;
            }

            // 启用功能模块
            enableFunctionModules(true);
        } else {
            extractResultText.text = qsTr("未找到termdisk目录，请确认选择的是正确的压缩文件或目录");

            // 显示错误对话框
            errorDialog.text = qsTr("未找到termdisk目录，请确认选择的是正确的压缩文件或目录");
            errorDialog.open();

            // 禁用功能模块
            enableFunctionModules(false);
        }
        extractResultText.visible = true;

        // 刷新历史记录列表
        loadHistoryList();
    }

    // 处理文件的公共函数，供文件选择和拖拽功能共用
    function processSelectedFile(filePath) {
        console.log("处理选择的文件路径: " + filePath);
        archiveFilePath.text = filePath;

        // 重置取消状态
        waitingDialog.isCancelled = false;

        // 更新等待对话框文本为解压文件
        waitingDialogText.text = qsTr("正在解压文件，请稍候...");

        // 确保等待提示框是可见的
        waitingDialog.visible = true;
        waitingDialog.open();

        // 添加备用提示，以防waitingDialog不显示
        extractResultText.text = qsTr("正在解压文件，请稍候...");
        extractResultText.visible = true;

        // 使用计时器延迟调用，确保UI有时间显示等待对话框
        var timer = Qt.createQmlObject('import QtQuick 2.0; Timer {}', mainWindow);
        timer.interval = 100;
        timer.repeat = false;
        timer.triggered.connect(function() {
            // 检查是否已取消
            if (!waitingDialog.isCancelled) {
                // 调用C++方法提取文件
                archiveHandler.extractArchive(filePath);
            }
            timer.destroy();
        });
        timer.start();
    }

    // 启用/禁用功能模块
    function enableFunctionModules(enabled) {
        for (var i = 0; i < navigationModel.count; i++) {
            var item = navigationModel.get(i);
            if (item.type !== "fileSelection") {
                navigationModel.setProperty(i, "enabled", enabled);
            }
        }
    }

    // termDiskPath变化时的处理
    onTermDiskPathChanged: {
        console.log("termDiskPath变化:", termDiskPath);

        // 更新各个Loader中的termDiskPath
        if (logAnalyzerLoader.item) {
            logAnalyzerLoader.item.termDiskPath = termDiskPath;
        }
        if (paramViewerLoader.item) {
            paramViewerLoader.item.termDiskPath = termDiskPath;
        }
        if (databaseToolLoader.item) {
            databaseToolLoader.item.termDiskPath = termDiskPath;
        }
        if (protocolParserLoader.item) {
            protocolParserLoader.item.termDiskPath = termDiskPath;
        }

    }
    
    // 用于接收C++端extractionFailed信号的函数
    function handleExtractionFailed(errorMessage) {
        console.log("收到C++端解压失败信号，错误:", errorMessage);
        
        // 确保关闭等待提示框
        if (waitingDialog.visible) {
            console.log("关闭等待提示框");
            waitingDialog.close();
        }
        
        // 显示错误信息
        extractResultText.text = qsTr("解压失败: ") + errorMessage;
        extractResultText.visible = true;
        
        // 显示错误对话框
        errorDialog.text = qsTr("解压失败: ") + errorMessage;
        errorDialog.open();
    }
    
    // 加载历史记录列表
    function loadHistoryList() {
        console.log("加载历史记录列表");
        
        // 清空现有的历史记录
        historyModel.clear();
        
        // 调用C++方法获取tmp目录下的子目录列表
        if (typeof archiveHandler !== "undefined" && archiveHandler) {
            var tmpDirs = archiveHandler.listTmpDirectories();
            if (tmpDirs && tmpDirs.length > 0) {
                console.log("找到历史记录数量:", tmpDirs.length);
                
                // 遍历目录列表并添加到模型中
                for (var i = 0; i < tmpDirs.length; i++) {
                    try {
                        // 解析目录信息
                        var dirInfo = JSON.parse(tmpDirs[i]);
                        historyModel.append({
                            name: dirInfo.name,
                            path: dirInfo.path,
                            modified: dirInfo.modified
                        });
                        console.log("添加历史记录:", dirInfo.name, dirInfo.path);
                    } catch (e) {
                        console.error("处理目录信息时出错:", e);
                    }
                }
                
                // 按修改时间排序，最新的在前面
                for (var i = 0; i < historyModel.count; i++) {
                    for (var j = i + 1; j < historyModel.count; j++) {
                        if (historyModel.get(j).modified > historyModel.get(i).modified) {
                            historyModel.move(j, i, 1);
                        }
                    }
                }
            } else {
                console.log("未找到历史记录");
            }
        }
    }
    
    // 处理C++端的日志处理完成信号
    function logProcessingCompleted(logsDir) {
        console.log("收到C++端日志处理完成信号，日志目录:", logsDir);

        // 如果日志分析视图已加载，通知它
        if (logAnalyzerLoader.item && typeof logAnalyzerLoader.item.loadLogFilesList === "function") {
            logAnalyzerLoader.item.loadLogFilesList();
        }
    }

    // 处理C++端的批处理结束信号
    function handleBatchProcessFinished(success, message) {
        console.log("收到C++端批处理结束信号:", success, message);

        // 如果日志分析视图已加载，通知它
        if (logAnalyzerLoader.item && typeof logAnalyzerLoader.item.handleBatchProcessFinished === "function") {
            logAnalyzerLoader.item.handleBatchProcessFinished(success, message);
        }
    }

    // 处理C++端的批处理开始信号
    function handleBatchProcessStarted(message) {
        console.log("收到C++端批处理开始信号:", message);

        // 如果日志分析视图已加载，通知它
        if (logAnalyzerLoader.item && typeof logAnalyzerLoader.item.handleBatchProcessStarted === "function") {
            logAnalyzerLoader.item.handleBatchProcessStarted(message);
        }
    }

    // 处理C++端的日志文件内容
    function handleLogFileContent(filePath, content) {
        console.log("收到C++端日志文件内容，路径:", filePath, "内容长度:", content.length);

        // 如果日志分析视图已加载，通知它
        if (logAnalyzerLoader.item && typeof logAnalyzerLoader.item.handleLogFileContent === "function") {
            logAnalyzerLoader.item.handleLogFileContent(filePath, content);
        }
    }

    // 组件加载完成时的处理
    Component.onCompleted: {
        // 加载历史记录列表
        loadHistoryList();
    }
    
    // 设置全局字体
    FontLoader {
        id: microsoftYaHei
        name: "Microsoft YaHei"
    }
    
    // 错误消息对话框
    MessageDialog {
        id: errorDialog
        title: qsTr("错误")
        text: ""
        buttons: StandardButton.Ok
    }
    
    // 主内容区域 - 左右分栏布局
    RowLayout {
        id: mainContent
        anchors {
            top: parent.top
            left: parent.left
            right: parent.right
            bottom: parent.bottom
            margins: 10
        }
        spacing: 0

        // 左侧导航栏
        Rectangle {
            id: leftSidebar
            Layout.preferredWidth: sidebarCollapsed ? 60 : 280
            Layout.fillHeight: true
            color: "#2c3e50"
            radius: 8

            property bool sidebarCollapsed: false

            Behavior on Layout.preferredWidth {
                NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
            }

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5

                // 收缩/展开按钮
                Button {
                    id: collapseBtn
                    Layout.fillWidth: true
                    Layout.preferredHeight: 40

                    background: Rectangle {
                        color: parent.hovered ? "#34495e" : "transparent"
                        radius: 6
                    }

                    contentItem: Text {
                        text: leftSidebar.sidebarCollapsed ? "☰" : "◀"
                        color: "white"
                        font.pixelSize: 16
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    onClicked: {
                        leftSidebar.sidebarCollapsed = !leftSidebar.sidebarCollapsed
                    }
                }

                Rectangle {
                    Layout.fillWidth: true
                    height: 1
                    color: "#34495e"
                }

                // 导航项目列表
                ListView {
                    id: navigationList
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    model: navigationModel
                    delegate: navigationDelegate
                    currentIndex: 0

                    // 导航项目数据模型
                    ListModel {
                        id: navigationModel
                        ListElement { name: "首页"; icon: "🏠"; type: "fileSelection"; enabled: true }
                        ListElement { name: "日志管理"; icon: "📝"; type: "logAnalyzer"; enabled: false }
                        ListElement { name: "参数查看"; icon: "⚙️"; type: "paramViewer"; enabled: false }
                        ListElement { name: "数据库工具"; icon: "📊"; type: "databaseTool"; enabled: false }
                        ListElement { name: "协议解析"; icon: "🔍"; type: "protocolParser"; enabled: true }
                        ListElement { name: "帮助"; icon: "❓"; type: "help"; enabled: true }
                    }

                    // 导航项目委托
                    Component {
                        id: navigationDelegate

                        Rectangle {
                            width: navigationList.width
                            height: 50
                            color: {
                                if (index === navigationList.currentIndex) return "#3498db"
                                if (navMouseArea.containsMouse) return "#34495e"
                                return "transparent"
                            }
                            radius: 6
                            opacity: enabled ? 1.0 : 0.5

                            Behavior on color {
                                ColorAnimation { duration: 200 }
                            }

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 10

                                Text {
                                    text: icon
                                    color: "white"
                                    font.pixelSize: 18
                                    Layout.preferredWidth: 30
                                    horizontalAlignment: Text.AlignHCenter
                                }

                                Text {
                                    text: name
                                    color: "white"
                                    font {
                                        family: microsoftYaHei.name
                                        pixelSize: 14
                                        bold: index === navigationList.currentIndex
                                    }
                                    Layout.fillWidth: true
                                    visible: !leftSidebar.sidebarCollapsed
                                    opacity: leftSidebar.sidebarCollapsed ? 0 : 1

                                    Behavior on opacity {
                                        NumberAnimation { duration: 200 }
                                    }
                                }
                            }

                            MouseArea {
                                id: navMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                cursorShape: enabled ? Qt.PointingHandCursor : Qt.ArrowCursor

                                onClicked: {
                                    if (enabled) {
                                        navigationList.currentIndex = index
                                        rightContent.currentView = type
                                    }
                                }
                            }

                            // 工具提示（收缩状态下显示）
                            ToolTip {
                                visible: leftSidebar.sidebarCollapsed && navMouseArea.containsMouse
                                text: name
                                delay: 500
                            }
                        }
                    }
                }
            }
        }

        // 右侧内容区域
        Rectangle {
            id: rightContent
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.leftMargin: 10
            color: rightContentDropArea.containsDrag ? "#f0f8ff" : "#ecf0f1"
            radius: 8
            border.color: rightContentDropArea.containsDrag ? "#1976D2" : "transparent"
            border.width: rightContentDropArea.containsDrag ? 2 : 0

            property string currentView: "fileSelection"

            // 整个右侧栏的拖拽区域
            DropArea {
                id: rightContentDropArea
                anchors.fill: parent
                keys: ["text/uri-list"]

                onEntered: {
                    console.log("文件拖拽进入右侧栏");
                    // 只有在首页时才处理拖拽
                    if (rightContent.currentView === "fileSelection") {
                        // 视觉反馈已通过rightContent的color和border属性处理
                    }
                }

                onExited: {
                    console.log("文件拖拽离开右侧栏");
                }

                onDropped: {
                    console.log("文件拖拽放下到右侧栏");

                    // 只有在首页时才处理拖拽
                    if (rightContent.currentView === "fileSelection") {
                        if (drop.hasUrls) {
                            var urls = drop.urls;
                            if (urls.length > 0) {
                                var filePath = urls[0];
                                console.log("拖拽的文件路径: " + filePath);
                                processSelectedFile(filePath);
                            }
                        }
                    }
                }
            }

            // 拖拽提示覆盖层
            Rectangle {
                anchors.fill: parent
                color: "#80E3F2FD"
                radius: 8
                visible: rightContentDropArea.containsDrag && rightContent.currentView === "fileSelection"
                z: 1000

                Rectangle {
                    anchors.centerIn: parent
                    width: 300
                    height: 100
                    color: "white"
                    radius: 10
                    border.color: "#1976D2"
                    border.width: 2

                    layer.enabled: true
                    layer.effect: DropShadow {
                        horizontalOffset: 0
                        verticalOffset: 4
                        radius: 12.0
                        samples: 25
                        color: "#40000000"
                    }

                    Column {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "📁"
                            font.pixelSize: 32
                            anchors.horizontalCenter: parent.horizontalCenter
                        }

                        Text {
                            text: qsTr("拖拽文件到此处")
                            font {
                                family: microsoftYaHei.name
                                pixelSize: 16
                                bold: true
                            }
                            color: "#1976D2"
                            anchors.horizontalCenter: parent.horizontalCenter
                        }
                    }
                }
            }

            // 文件选择视图（合并历史记录）
            Rectangle {
                id: fileSelectionView
                anchors.fill: parent
                anchors.margins: 15
                color: "transparent"
                visible: rightContent.currentView === "fileSelection"

                ColumnLayout {
                    anchors.fill: parent
                    spacing: 20

                    // 文件选择区域
                    Rectangle {
                        id: fileSelectionArea
                        Layout.fillWidth: true
                        height: 80
                        radius: 10
                        color: "white"
                        border.color: rightContentDropArea.containsDrag ? "#1976D2" : "#e0e0e0"
                        border.width: rightContentDropArea.containsDrag ? 2 : 1

                        layer.enabled: true
                        layer.effect: DropShadow {
                            horizontalOffset: 0
                            verticalOffset: 2
                            radius: 8.0
                            samples: 17
                            color: "#20000000"
                        }

                        RowLayout {
                            anchors {
                                fill: parent
                                margins: 15
                            }
                            spacing: 15

                            TextField {
                                id: archiveFilePath
                                Layout.fillWidth: true
                                Layout.preferredHeight: 45
                                readOnly: true
                                placeholderText: qsTr("选择压缩文件或将文件拖拽页面")
                                font {
                                    family: microsoftYaHei.name
                                    pixelSize: 14
                                }
                                selectByMouse: true

                                background: Rectangle {
                                    radius: 6
                                    border.color: archiveFilePath.activeFocus ? "#1976D2" : "#e0e0e0"
                                    border.width: 1
                                }

                                // 添加工具提示，鼠标悬停时显示完整路径
                                ToolTip {
                                    visible: parent.hovered && parent.text !== ""
                                    text: parent.text
                                    delay: 500
                                }
                            }

                            Button {
                                id: selectFileBtn
                                text: qsTr("选择文件")
                                Layout.preferredWidth: 120
                                Layout.preferredHeight: 45

                                background: Rectangle {
                                    radius: 6
                                    gradient: Gradient {
                                        GradientStop { position: 0.0; color: selectFileBtn.pressed ? "#1565C0" : "#1976D2" }
                                        GradientStop { position: 1.0; color: selectFileBtn.pressed ? "#0D47A1" : "#1565C0" }
                                    }
                                }

                                contentItem: Text {
                                    text: selectFileBtn.text
                                    color: "white"
                                    font {
                                        family: microsoftYaHei.name
                                        pixelSize: 14
                                        bold: true
                                    }
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }

                                onClicked: {
                                    fileDialog.open()
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    cursorShape: Qt.PointingHandCursor
                                    onPressed: mouse.accepted = false
                                }
                            }
                        }
                    }

                    // 提取结果文本
                    Label {
                        id: extractResultText
                        visible: false
                        font {
                            family: microsoftYaHei.name
                            pixelSize: 14
                        }
                        Layout.alignment: Qt.AlignHCenter
                        wrapMode: Text.WordWrap
                        Layout.maximumWidth: parent.width
                        padding: 10

                        background: Rectangle {
                            color: "#E3F2FD"
                            radius: 6
                            border.color: "#BBDEFB"
                            border.width: 1
                            visible: extractResultText.visible
                        }
                    }

                    // 历史记录区域
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.minimumHeight: 200
                        radius: 10
                        color: "white"
                        border.color: "#e0e0e0"
                        border.width: 1
                        visible: historyModel.count > 0

                        layer.enabled: true
                        layer.effect: DropShadow {
                            horizontalOffset: 0
                            verticalOffset: 2
                            radius: 8.0
                            samples: 17
                            color: "#20000000"
                        }

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 10

                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 10

                                Rectangle {
                                    width: 4
                                    height: 20
                                    color: "#1976D2"
                                    radius: 2
                                }

                                Text {
                                    text: qsTr("历史记录")
                                    font {
                                        family: microsoftYaHei.name
                                        pixelSize: 16
                                        bold: true
                                    }
                                    color: "#333333"
                                    Layout.fillWidth: true
                                }

                                Button {
                                    implicitWidth: 32
                                    implicitHeight: 32

                                    background: Rectangle {
                                        color: parent.down ? "#e0e0e0" : (parent.hovered ? "#f0f0f0" : "transparent")
                                        radius: 16
                                    }

                                    contentItem: Text {
                                        text: "🔄"
                                        font.pixelSize: 16
                                        horizontalAlignment: Text.AlignHCenter
                                        verticalAlignment: Text.AlignVCenter
                                    }

                                    ToolTip.visible: hovered
                                    ToolTip.text: qsTr("刷新历史记录")

                                    onClicked: {
                                        loadHistoryList();
                                    }
                                }
                            }

                            Rectangle {
                                Layout.fillWidth: true
                                height: 1
                                color: "#e0e0e0"
                            }

                            ListView {
                                id: historyListView
                                Layout.fillWidth: true
                                Layout.fillHeight: true
                                clip: true
                                model: ListModel { id: historyModel }

                                delegate: Rectangle {
                                    width: historyListView.width
                                    height: 50
                                    color: index % 2 === 0 ? "#ffffff" : "#f9f9f9"

                                    Rectangle {
                                        width: parent.width
                                        height: 1
                                        color: "#eeeeee"
                                        anchors.bottom: parent.bottom
                                    }

                                    RowLayout {
                                        anchors.fill: parent
                                        anchors.margins: 10
                                        spacing: 10

                                        Text {
                                            text: name
                                            Layout.fillWidth: true
                                            elide: Text.ElideMiddle
                                            font {
                                                family: microsoftYaHei.name
                                                pixelSize: 14
                                            }
                                            color: "#333333"

                                            // 添加工具提示，鼠标悬停时显示完整路径
                                            ToolTip.visible: historyItemMouseArea.containsMouse
                                            ToolTip.text: path

                                            MouseArea {
                                                id: historyItemMouseArea
                                                anchors.fill: parent
                                                hoverEnabled: true
                                            }
                                        }

                                        Button {
                                            text: qsTr("打开")
                                            implicitWidth: 60
                                            implicitHeight: 36

                                            background: Rectangle {
                                                color: parent.down ? "#1565C0" : (parent.hovered ? "#1976D2" : "#2196F3")
                                                radius: 6
                                            }

                                            contentItem: Text {
                                                text: parent.text
                                                color: "white"
                                                font {
                                                    family: microsoftYaHei.name
                                                    pixelSize: 14
                                                }
                                                horizontalAlignment: Text.AlignHCenter
                                                verticalAlignment: Text.AlignVCenter
                                            }

                                            onClicked: {
                                                // 使用新的openExistingDirectory方法处理已存在的目录
                                                console.log("打开历史记录目录:", path);

                                                // 重置取消状态
                                                waitingDialog.isCancelled = false;

                                                // 更新等待对话框文本
                                                waitingDialogText.text = qsTr("正在打开目录，查找termdisk...");

                                                // 显示等待提示框
                                                waitingDialog.visible = true;
                                                waitingDialog.open();

                                                // 添加备用提示
                                                extractResultText.text = qsTr("正在打开目录，查找termdisk...");
                                                extractResultText.visible = true;

                                                // 使用计时器延迟调用，确保UI有时间显示等待对话框
                                                var timer = Qt.createQmlObject('import QtQuick 2.0; Timer {}', mainWindow);
                                                timer.interval = 100;
                                                timer.repeat = false;
                                                timer.triggered.connect(function() {
                                                    // 检查是否已取消
                                                    if (!waitingDialog.isCancelled) {
                                                        // 调用C++方法打开已存在的目录
                                                        archiveHandler.openExistingDirectory(path);
                                                    }
                                                    timer.destroy();
                                                });
                                                timer.start();
                                            }
                                        }

                                        Button {
                                            text: qsTr("删除")
                                            implicitWidth: 60
                                            implicitHeight: 36

                                            background: Rectangle {
                                                color: parent.down ? "#C62828" : (parent.hovered ? "#D32F2F" : "#F44336")
                                                radius: 6
                                            }

                                            contentItem: Text {
                                                text: parent.text
                                                color: "white"
                                                font {
                                                    family: microsoftYaHei.name
                                                    pixelSize: 14
                                                }
                                                horizontalAlignment: Text.AlignHCenter
                                                verticalAlignment: Text.AlignVCenter
                                            }

                                            onClicked: {
                                                console.log("删除历史记录:", path);

                                                // 检查是否删除的是当前选择的路径
                                                var isCurrentPath = false;
                                                if (mainWindow.termDiskPath && mainWindow.termDiskPath.length > 0) {
                                                    // 检查当前termDiskPath是否在要删除的路径下
                                                    var currentPath = mainWindow.termDiskPath.replace(/\\/g, "/");
                                                    var deletePath = path.replace(/\\/g, "/");

                                                    // 如果当前路径以删除路径开头，说明当前路径在要删除的目录下
                                                    if (currentPath.startsWith(deletePath)) {
                                                        isCurrentPath = true;
                                                        console.log("要删除的是当前选择的路径或其父目录");
                                                    }
                                                }

                                                // 直接调用C++方法删除目录
                                                if (archiveHandler && typeof archiveHandler.removeDirectory === "function") {
                                                    var success = archiveHandler.removeDirectory(path);

                                                    if (success) {
                                                        // 从模型中移除该项
                                                        historyModel.remove(index);

                                                        // 如果删除的是当前选择的路径，清空termDiskPath
                                                        if (isCurrentPath) {
                                                            console.log("清空当前termDiskPath");
                                                            mainWindow.termDiskPath = "";

                                                            // 禁用功能模块
                                                            enableFunctionModules(false);

                                                            // 显示特殊消息
                                                            extractResultText.text = qsTr("成功删除当前选择的历史记录: ") + name + qsTr("，已清空当前路径");
                                                        } else {
                                                            // 显示普通成功消息
                                                            extractResultText.text = qsTr("成功删除历史记录: ") + name;
                                                        }

                                                        extractResultText.visible = true;
                                                        console.log("历史记录删除成功");
                                                    } else {
                                                        // 显示错误消息
                                                        errorDialog.text = qsTr("删除历史记录失败，请检查文件权限或稍后重试");
                                                        errorDialog.open();

                                                        console.error("历史记录删除失败");
                                                    }
                                                } else {
                                                    errorDialog.text = qsTr("删除功能不可用，请重启应用程序");
                                                    errorDialog.open();
                                                }
                                            }
                                        }
                                    }
                                }

                                ScrollBar.vertical: ScrollBar {
                                    active: true
                                }
                            }
                        }
                    }
                }
            }


            // 功能模块视图容器
            Rectangle {
                id: moduleViewContainer
                anchors.fill: parent
                anchors.margins: 15
                color: "transparent"
                visible: rightContent.currentView !== "fileSelection"

                // 日志分析视图
                Loader {
                    id: logAnalyzerLoader
                    anchors.fill: parent
                    active: rightContent.currentView === "logAnalyzer"
                    source: active ? "LogAnalyzerView.qml" : ""

                    onLoaded: {
                        if (item) {
                            item.termDiskPath = Qt.binding(function() { return mainWindow.termDiskPath; });
                            item.objectName = "logAnalyzerView";
                            item.windowVisible = true;
                        }
                    }
                }

                // 参数查看视图
                Loader {
                    id: paramViewerLoader
                    anchors.fill: parent
                    active: rightContent.currentView === "paramViewer"
                    source: active ? "ParamViewerView.qml" : ""

                    onLoaded: {
                        if (item) {
                            item.termDiskPath = Qt.binding(function() { return mainWindow.termDiskPath; });
                        }
                    }
                }

                // 数据库工具视图
                Loader {
                    id: databaseToolLoader
                    anchors.fill: parent
                    active: rightContent.currentView === "databaseTool"
                    source: active ? "DatabaseToolView.qml" : ""

                    onLoaded: {
                        if (item) {
                            item.termDiskPath = Qt.binding(function() { return mainWindow.termDiskPath; });
                            if (typeof dbToolCpp !== "undefined" && dbToolCpp) {
                                item.dbToolCpp = dbToolCpp;
                            }
                        }
                    }
                }

                // 协议解析器视图
                Loader {
                    id: protocolParserLoader
                    anchors.fill: parent
                    active: rightContent.currentView === "protocolParser"
                    source: active ? "ProtocolParserView.qml" : ""

                    onLoaded: {
                        if (item) {
                            item.termDiskPath = Qt.binding(function() { return mainWindow.termDiskPath; });
                        }
                    }
                }

                // 帮助视图
                Loader {
                    id: helpLoader
                    anchors.fill: parent
                    active: rightContent.currentView === "help"
                    source: active ? "HelpView.qml" : ""
                }

            }
        }
    }
    
    // 版权信息
    Text {
        anchors {
            bottom: parent.bottom
            horizontalCenter: parent.horizontalCenter
            bottomMargin: 5
        }
        text: qsTr("© 2025 威胜信息 - 版本 1.0")
        font {
            family: microsoftYaHei.name
            pixelSize: 12
        }
        color: "#888888"
    }
    



    // 使用Qt.labs.platform的FileDialog
    FileDialog {
        id: fileDialog
        title: qsTr("选择文件")
        folder: StandardPaths.writableLocation(StandardPaths.HomeLocation)
        nameFilters: [qsTr("压缩文件 (*.zip *.tar *.tgz *.gz *.rar)"), qsTr("所有文件 (*)")]
        onAccepted: {
            var filePath = fileDialog.file.toString();
            processSelectedFile(filePath);
        }
    }

    // 等待提示框
    Popup {
        id: waitingDialog
        width: 400
        height: 200
        anchors.centerIn: parent
        modal: true
        closePolicy: Popup.NoAutoClose
        z: 1000 // 确保对话框显示在最上层
        parent: mainWindow.contentItem // 确保对话框的父对象是主窗口的contentItem
        
        // 添加可见性变化监听
        onVisibleChanged: {
            console.log("waitingDialog 可见性变化:", visible);
            if (visible) {
                // 启动进度条动画
                progressAnimation.restart();
            }
        }
        
        // 添加取消操作属性
        property bool isCancelled: false
        
        background: Rectangle {
            radius: 10
            color: "white"
            border.color: "#e0e0e0"
            border.width: 1
            
            layer.enabled: true
            layer.effect: DropShadow {
                horizontalOffset: 0
                verticalOffset: 3
                radius: 8.0
                samples: 17
                color: "#30000000"
            }
        }
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 15
            
            RowLayout {
                Layout.fillWidth: true
                spacing: 15
                
                BusyIndicator {
                    running: true
                    Layout.preferredWidth: 40
                    Layout.preferredHeight: 40
                    
                    contentItem: Item {
                        implicitWidth: 40
                        implicitHeight: 40
                        
                        RotationAnimator {
                            target: spinner
                            running: waitingDialog.visible
                            from: 0
                            to: 360
                            duration: 1500
                            loops: Animation.Infinite
                        }
                        
                        Rectangle {
                            id: spinner
                            anchors.centerIn: parent
                            width: 40
                            height: 40
                            radius: width / 2
                            color: "transparent"
                            border.width: 4
                            border.color: "#1976D2"
                            opacity: 1
                            
                            Rectangle {
                                width: parent.width / 3
                                height: parent.height / 3
                                radius: width / 2
                                color: "#1976D2"
                                anchors {
                                    top: parent.top
                                    horizontalCenter: parent.horizontalCenter
                                    topMargin: -parent.border.width / 2
                                }
                            }
                        }
                    }
                }
                
                Text {
                    id: waitingDialogText
                    text: qsTr("正在解压文件，请稍候...")
                    font {
                        family: microsoftYaHei.name
                        pixelSize: 16
                        bold: true
                    }
                    color: "#333333"
                    Layout.fillWidth: true
                }
            }
            
            Text {
                text: qsTr("解压过程可能需要一些时间，取决于文件大小")
                font {
                    family: microsoftYaHei.name
                    pixelSize: 14
                }
                color: "#666666"
                Layout.fillWidth: true
                horizontalAlignment: Text.AlignHCenter
            }
            
            Rectangle {
                Layout.fillWidth: true
                height: 6
                radius: 3
                color: "#f0f0f0"
                
                Rectangle {
                    id: progressBar
                    width: parent.width * progressAnimation.progress
                    height: parent.height
                    radius: 3
                    
                    gradient: Gradient {
                        orientation: Gradient.Horizontal
                        GradientStop { position: 0.0; color: "#1976D2" }
                        GradientStop { position: 1.0; color: "#64B5F6" }
                    }
                    
                    // 进度条动画
                    NumberAnimation {
                        id: progressAnimation
                        target: progressBar
                        property: "width"
                        from: 0
                        to: progressBar.parent.width
                        duration: 20000 // 20秒动画，给用户更好的进度感知
                        easing.type: Easing.OutQuad
                    }
                }
            }
            
            // 添加取消按钮
            Button {
                text: qsTr("取消")
                Layout.alignment: Qt.AlignHCenter
                Layout.preferredWidth: 120
                Layout.preferredHeight: 36
                
                background: Rectangle {
                    color: parent.down ? "#d32f2f" : (parent.hovered ? "#f44336" : "#e57373")
                    radius: 4
                }
                
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    font.pixelSize: 14
                }
                
                onClicked: {
                    // 标记为已取消
                    waitingDialog.isCancelled = true;
                    
                    // 关闭对话框
                    waitingDialog.close();
                    
                    // 显示取消消息
                    extractResultText.text = qsTr("解压操作已取消");
                    extractResultText.visible = true;
                }
            }
        }
    }
}
