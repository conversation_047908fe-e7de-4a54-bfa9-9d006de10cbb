#ifndef LOGANALYZER_H
#define LOGANALYZER_H

#include <QObject>
#include <QWidget>
#include <QVBoxLayout>
#include <QPushButton>
#include <QFileInfo>
#include <QLabel>
#include <QTextEdit>

class LogAnalyzer : public QWidget
{
    Q_OBJECT

public:
    explicit LogAnalyzer(QWidget* parent = nullptr);
    ~LogAnalyzer();

    QWidget* createLogAnalyzerWidget();
    
    // 设置termdisk路径
    void setTermDiskPath(const QString& path) { m_termDiskPath = path; }
    QString getTermDiskPath() const { return m_termDiskPath; }

private slots:
    void analyzeLog();
    void clearLog();

private:
    QTextEdit* m_logDisplay;
    QPushButton* m_analyzeButton;
    QPushButton* m_clearButton;
    QLabel* m_statusLabel;
    QString m_termDiskPath;  // 存储termdisk路径
};

#endif // LOGANALYZER_H 