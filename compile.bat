@echo off
setlocal enabledelayedexpansion
:: need QT:5.12,mingw64  cmake:3.14   
echo ==========================================
echo        NW_ManageTools Build Script
echo        QT version :5.12 ,toolchain:mingw64
echo        CMake version :3.14
echo        set path:\Qt5.12.10\5.12.10\mingw73_64\bin
echo        set path:\Qt5.12.10\Tools\mingw730_64\bin
echo ==========================================

set BUILD_TYPE=Debug
set CLEAN_BUILD=0
set PACKAGE_MODE=0

if "%1"=="debug" set BUILD_TYPE=Debug
if "%1"=="release" set BUILD_TYPE=Release
if "%1"=="clean" set CLEAN_BUILD=1
if "%1"=="package" (
    set BUILD_TYPE=Release
    set PACKAGE_MODE=1
)
if "%2"=="debug" set BUILD_TYPE=Debug
if "%2"=="release" set BUILD_TYPE=Release
if "%2"=="clean" set CLEAN_BUILD=1
if "%2"=="package" (
    set BUILD_TYPE=Release
    set PACKAGE_MODE=1
)

echo Build Type: %BUILD_TYPE%

echo Checking environment...
where cmake >nul 2>&1
if errorlevel 1 (
    echo ERROR: CMake not found
    pause
    exit /b 1
)

where qmake >nul 2>&1
if errorlevel 1 (
    echo WARNING: qmake not found
)

if %CLEAN_BUILD%==1 (
    echo Cleaning build directory...
    if exist build rmdir /s /q build
)

if not exist build mkdir build
cd build

echo Configuring project...
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=%BUILD_TYPE%
if errorlevel 1 (
    echo Configuration failed!
    cd ..
    pause
    exit /b 1
)

echo Building project...
cmake --build . --config %BUILD_TYPE% --parallel
if errorlevel 1 (
    echo Build failed!
    cd ..
    pause
    exit /b 1
)

echo.
echo ==========================================
echo        Build completed successfully!
echo ==========================================

echo Copying tools and config folders...
if exist ..\tools (
    if not exist tools (
        xcopy /E /I /Y ..\tools tools
        echo Tools folder copied
    ) else (
        echo Tools folder already exists
    )
) else (
    echo WARNING: tools folder not found in source
)

if exist ..\config (
    if not exist config (
        xcopy /E /I /Y ..\config config
        echo Config folder copied
    ) else (
        echo Config folder already exists
    )
) else (
    echo WARNING: config folder not found in source
)

if exist NW_ManageTools.exe (
    echo Executable: build\NW_ManageTools.exe
) else if exist %BUILD_TYPE%\NW_ManageTools.exe (
    echo Executable: build\%BUILD_TYPE%\NW_ManageTools.exe
) else (
    echo WARNING: Executable not found
)

cd ..

if %PACKAGE_MODE%==1 (
    echo.
    echo ==========================================
    echo        Starting packaging process...
    echo ==========================================

    :: Create deploy directory
    if exist deploy rmdir /s /q deploy
    mkdir deploy

    :: Find and copy executable
    if exist build\NW_ManageTools.exe (
        copy build\NW_ManageTools.exe deploy\
        set EXE_FOUND=1
    ) else if exist build\Release\NW_ManageTools.exe (
        copy build\Release\NW_ManageTools.exe deploy\
        set EXE_FOUND=1
    ) else (
        echo ERROR: Executable not found for packaging
        pause
        exit /b 1
    )

    :: Copy tools and config folders to deploy
    if exist tools (
        xcopy /E /I /Y tools deploy\tools
        echo Tools folder copied to deploy
    )

    if exist config (
        xcopy /E /I /Y config deploy\config
        echo Config folder copied to deploy
    )

    :: Run windeployqt
    echo Running windeployqt...
    where windeployqt >nul 2>&1
    if errorlevel 1 (
        echo WARNING: windeployqt not found, skipping Qt deployment
    ) else (
        windeployqt --release --qmldir . deploy\NW_ManageTools.exe
        if errorlevel 1 (
            echo WARNING: windeployqt failed
        ) else (
            echo Qt dependencies deployed successfully
        )
    )


    
    set tt=%DATE:~0,4%%DATE:~5,2%%DATE:~8,2%

    
    echo !tt!
    :: Use PowerShell to create zip
    echo Using PowerShell to create zip...
    powershell -Command "Compress-Archive -Path 'deploy\*' -DestinationPath 'NW_ManageTools_!tt!.zip' -Force"
    if errorlevel 1 (
        echo ERROR: Failed to create archive
        echo Deploy folder ready at: deploy\
    ) else (
        echo.
        echo ==========================================
        echo        Packaging completed successfully!
        echo ==========================================
        echo Archive created: NW_ManageTools_!tt!.zip

        :: Clean up deploy folder
        echo Cleaning up deploy folder...
        rmdir /s /q deploy
        echo Deploy folder cleaned up
    )
)

echo.
echo Usage:
echo   compile.bat           - Build Debug version
echo   compile.bat release   - Build Release version
echo   compile.bat debug     - Build Debug version
echo   compile.bat clean     - Clean and build
echo   compile.bat package   - Build Release and create package
echo.
pause
