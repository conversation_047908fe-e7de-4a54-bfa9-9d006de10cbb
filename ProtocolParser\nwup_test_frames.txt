南网上行协议测试报文集合
======================

以下是一些典型的南网上行协议报文，可以用于测试解析器功能：

1. 链路接口检测报文 (AFN=02H)
   终端登录报文：
   68 1A 1A 68 CB 44 02 01 00 00 01 02 C0 00 00 F1 00 00 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F 10 A7 16
   
   说明：
   - 68H: 起始字符
   - 1A 1A: 长度L (26字节用户数据)
   - 68H: 第二个起始字符
   - CB: 控制域C (上行响应，PRM=1, DIR=1, 功能码=11)
   - 44 02 01 00 00 01: 地址域A (省地市区县码+终端地址+主站地址)
   - 02: AFN=02H (链路接口检测)
   - C0: SEQ (FIR=1, FIN=1, CON=1)
   - 00 00: 信息点标识DA
   - F1 00 00 00: 数据标识编码DI (终端登录)
   - 01 02...10: 数据标识内容 (16字节登录信息)
   - A7: 校验和CS
   - 16H: 结束字符

2. 读当前数据报文 (AFN=0CH)
   读正向有功总电能：
   68 0E 0E 68 CB 44 02 01 00 00 01 0C C0 01 01 00 01 00 00 12 34 56 78 CB 16
   
   说明：
   - AFN=0CH (读当前数据)
   - DA=0101H (信息点1)
   - DI=00010000H (当前正向有功总电能)
   - 12 34 56 78: 电能数据 (BCD格式，表示78563412*0.01=785634.12 kWh)

3. 读事件记录报文 (AFN=0EH)
   读停电事件记录：
   68 1C 1C 68 CB 44 02 01 00 00 01 0E C0 01 01 EE 01 00 00 24 05 15 10 30 00 24 05 15 11 00 00 01 02 03 04 05 06 07 08 B8 16
   
   说明：
   - AFN=0EH (读事件记录)
   - DA=0101H (信息点1)
   - DI=EE010000H (终端停电事件记录)
   - 24 05 15 10 30 00: 停电发生时间 (2024年5月15日10:30:00)
   - 24 05 15 11 00 00: 停电结束时间 (2024年5月15日11:00:00)
   - 01 02 03 04 05 06 07 08: 事件相关数据

4. 写参数报文 (AFN=04H)
   设置终端地址：
   68 12 12 68 8A 44 02 01 00 00 01 04 C0 00 00 F0 01 00 00 44 02 01 00 00 01 9F 16
   
   说明：
   - AFN=04H (写参数)
   - DA=0000H (终端信息点)
   - DI=F0010000H (终端地址参数)
   - 44 02 01 00 00 01: 新的终端地址参数值

5. 确认/否定报文 (AFN=00H)
   确认报文：
   68 0B 0B 68 88 44 02 01 00 00 01 00 C0 00 00 E0 00 00 00 00 A3 16
   
   说明：
   - AFN=00H (确认/否定)
   - DA=0000H (终端信息点)
   - DI=E0000000H (全确认/否定)
   - 00: 确认标志 (00H=确认, 01H=否定)

6. 读历史数据报文 (AFN=0DH)
   读日电能数据：
   68 17 17 68 CB 44 02 01 00 00 01 0D C0 01 01 00 01 00 00 24 05 14 00 00 24 05 15 00 00 06 12 34 56 78 24 05 14 00 00 C2 16
   
   说明：
   - AFN=0DH (读历史数据)
   - DA=0101H (信息点1)
   - DI=00010000H (正向有功总电能)
   - 24 05 14 00 00: 起始时间 (2024年5月14日00:00)
   - 24 05 15 00 00: 结束时间 (2024年5月15日00:00)
   - 06: 数据密度 (日数据)
   - 12 34 56 78: 历史电能数据
   - 24 05 14 00 00: 数据时间

使用方法：
========
1. 将上述十六进制字符串复制到你的测试程序中
2. 使用 QByteArray::fromHex() 转换为字节数组
3. 调用 NWUPParser::parseFrame() 进行解析
4. 检查解析结果的各个字段

示例代码：
=========
QByteArray testFrame = QByteArray::fromHex("681A1A68CB4402010000010240000F100000001020304050607080910111213141516A716");
NWUPParser parser;
ProtocolParseResult result = parser.parseFrame(testFrame);
if (result.isValid) {
    qDebug() << "解析成功:" << result.summary;
    qDebug() << "详细信息:" << result.detailInfo;
} else {
    qDebug() << "解析失败:" << result.errorMessage;
}