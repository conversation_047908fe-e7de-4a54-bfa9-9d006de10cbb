#ifndef DLT645PARSER_H
#define DLT645PARSER_H

#include <QObject>
#include <QByteArray>
#include <QString>
#include <QVariantMap>
#include <QDateTime>
#include <memory>

#include "protocoltypes.h"
#include "dataidentifierconfig.h"

/**
 * @brief DL/T645-2007协议解析器
 * 
 * 专门用于解析DL/T645-2007电能表通信协议报文
 */
class DLT645Parser : public QObject
{
    Q_OBJECT

public:
    explicit DLT645Parser(QObject *parent = nullptr);
    ~DLT645Parser();

    /**
     * @brief 设置数据标识配置
     * @param config 数据标识配置实例
     */
    void setDataConfig(DataIdentifierConfig* config);

    /**
     * @brief 解析DL/T645-2007协议报文
     * @param frameData 帧数据
     * @return 协议解析结果
     */
    ProtocolParseResult parseFrame(const QByteArray &frameData);

    /**
     * @brief 解析地址域
     * @param addressData 地址域数据（6字节）
     * @return 地址字符串
     */
    QString parseAddress(const QByteArray &addressData);

    /**
     * @brief 解析控制码
     * @param controlCode 控制码
     * @return 控制码信息
     */
    QVariantMap parseControlCode(quint8 controlCode);

    /**
     * @brief 解析数据域
     * @param dataField 数据域（已减33H处理）
     * @param controlCode 控制码
     * @param rawDataField 原始数据域（加33H的），可选参数
     * @return 数据域解析结果
     */
    QVariantMap parseDataField(const QByteArray &dataField, quint8 controlCode, const QByteArray &rawDataField = QByteArray());

    /**
     * @brief 解析数据标识
     * @param dataId 数据标识（4字节）
     * @return 数据标识信息
     */
    QVariantMap parseDataIdentifier(const QByteArray &dataId);


    /**
     * @brief 解析BCD编码的数值
     * @param bcdData BCD数据
     * @param decimalPlaces 小数位数
     * @return 数值字符串
     */
    QString parseBCDValue(const QByteArray &bcdData, int decimalPlaces = 2);

    /**
     * @brief 根据格式字符串解析数据
     * @param data 原始数据
     * @param format 格式字符串（如"XXX.X", "XX.XXXX"等）
     * @param encoding 编码格式（BCD/ASCII）
     * @return 解析后的数值字符串
     */
    QString parseDataByFormat(const QByteArray &data, const QString &format, const QString &encoding = "BCD");

    /**
     * @brief 判断格式字符串是否为时间格式
     * @param format 格式字符串
     * @return 是否为时间格式
     */
    bool isTimeFormat(const QString &format);

    /**
     * @brief 解析特殊的日期时间数据
     * @param data 原始数据
     * @param dataId 数据标识
     * @param format 时间格式字符串
     * @return 解析后的日期时间字符串
     */
    QString parseSpecialDateTimeData(const QByteArray &data, quint32 dataId, const QString &format = "");

    /**
     * @brief 解析时间格式数据
     * @param data 原始数据
     * @param dataId 数据标识
     * @param format 时间格式字符串
     * @return 解析后的时间字符串
     */
    QString parseTimeFormatData(const QByteArray &data, quint32 dataId, const QString &format = "");

    /**
     * @brief 根据格式字符串解析时间数据
     * @param data 原始数据
     * @param format 时间格式字符串 (如YYMMDDWW, HHMMSS等)
     * @return 解析后的时间字符串
     */
    QString parseTimeByFormat(const QByteArray &data, const QString &format);



    /**
     * @brief BCD码转换为整数（不进行有效性检查）
     * @param bcd BCD码字节
     * @return 转换后的整数
     */
    int bcdToInt(quint8 bcd);



    /**
     * @brief 判断是否为状态字、特征字和模式字数据
     * @param dataId 数据标识
     * @return 是否为状态字/特征字数据
     */
    bool isFeatureWordData(quint32 dataId);

    /**
     * @brief 解析状态字、特征字和模式字数据
     * @param data 原始数据
     * @param dataId 数据标识 (04000501-04000507, 04000601-04000603, 04000701-04000705, 04000801, 04000901-04000902)
     * @return 解析后的描述字符串
     */
    QString parseFeatureWordData(const QByteArray &data, quint32 dataId);

    /**
     * @brief 解析状态字/特征字字节的各个bit位
     * @param featureByte 字节数据
     * @param dataId 数据标识
     * @param byteIndex 字节索引
     * @return 描述列表
     */
    QStringList parseFeatureWordByte(quint8 featureByte, quint32 dataId, int byteIndex);

    /**
     * @brief 解析数据块内容
     * @param dataContent 数据块的原始内容
     * @param blockConfig 数据块配置信息
     * @return 解析后的数据块内容（包含各个数据项的解析结果）
     */
    QVariantMap parseDataBlockContent(const QByteArray &dataContent, const DataItemConfig &blockConfig);

    /**
     * @brief 解析复合数据内容
     * @param dataContent 复合数据的处理后内容（减33H后）
     * @param complexConfig 复合数据配置信息
     * @param rawDataContent 复合数据的原始内容（加33H的），可选参数
     * @return 解析后的复合数据内容（包含各个字段的解析结果）
     */
    QVariantMap parseComplexDataContent(const QByteArray &dataContent, const DataItemConfig &complexConfig, const QByteArray &rawDataContent = QByteArray());

    /**
     * @brief 解析可变长度数据项内容
     * @param dataContent 可变长度数据的处理后内容（减33H后）
     * @param variableConfig 可变长度数据配置信息
     * @param rawDataContent 可变长度数据的原始内容（加33H的），可选参数
     * @return 解析后的可变长度数据内容（包含各个子项的解析结果）
     */
    QVariantMap parseVariableDataContent(const QByteArray &dataContent, const DataItemConfig &variableConfig, const QByteArray &rawDataContent = QByteArray());

    /**
     * @brief 解析状态字
     * @param statusData 状态字数据
     * @param statusType 状态字类型
     * @return 状态字解析结果
     */
    QVariantMap parseStatusWord(const QByteArray &statusData, int statusType);

    /**
     * @brief 解析错误信息字
     * @param errorCode 错误码
     * @return 错误信息
     */
    QVariantMap parseErrorCode(quint8 errorCode);



private:
    /**
     * @brief 对数据域进行减33H处理
     * @param data 原始数据域
     * @return 处理后的数据域
     */
    QByteArray processDataField(const QByteArray &data);

    /**
     * @brief 获取功能码描述
     * @param functionCode 功能码
     * @return 功能描述
     */
    QString getFunctionDescription(quint8 functionCode);

    /**
     * @brief 获取数据标识描述
     * @param dataId 数据标识
     * @return 数据标识描述
     */
    QString getDataIdentifierDescription(quint32 dataId);



    /**
     * @brief 解析读数据应答
     * @param dataField 数据域（已减33H处理）
     * @param rawDataField 原始数据域（加33H的），可选参数
     * @return 解析结果
     */
    QVariantMap parseReadDataResponse(const QByteArray &dataField, const QByteArray &rawDataField = QByteArray());

    /**
     * @brief 解析读数据请求
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parseReadDataRequest(const QByteArray &dataField);

    /**
     * @brief 解析写数据请求
     * @param dataField 数据域（已减33H处理）
     * @param rawDataField 原始数据域（加33H的），可选参数
     * @return 解析结果
     */
    QVariantMap parseWriteDataRequest(const QByteArray &dataField, const QByteArray &rawDataField = QByteArray());

    /**
     * @brief 基于配置文件解析数据内容（统一解析逻辑）
     * @param dataId 数据标识符（4字节）
     * @param dataContent 数据内容
     * @param rawDataField 原始数据域（可选）
     * @param result 解析结果（输入输出参数）
     * @return 解析结果
     */
    QVariantMap parseDataContentWithConfig(const QByteArray &dataId, const QByteArray &dataContent, const QByteArray &rawDataField, QVariantMap &result);

    /**
     * @brief 解析校时数据
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parseTimeCalibrationData(const QByteArray &dataField);

    /**
     * @brief 解析冻结命令数据
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parseFreezeCommandData(const QByteArray &dataField);

    /**
     * @brief 解析密码相关数据
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parsePasswordData(const QByteArray &dataField);

    /**
     * @brief 解析读后续数据请求
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parseReadContinuousDataRequest(const QByteArray &dataField);

    /**
     * @brief 解析读通信地址应答
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parseReadAddressResponse(const QByteArray &dataField);

    /**
     * @brief 解析写通信地址请求
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parseWriteAddressRequest(const QByteArray &dataField);

    /**
     * @brief 解析更改通信速率请求
     * @param dataField 数据域
     * @return 解析结果
     */
    QVariantMap parseChangeRateRequest(const QByteArray &dataField);

    /**
     * @brief 创建解析错误结果
     * @param errorMessage 错误信息
     * @return 错误结果
     */
    ProtocolParseResult createErrorResult(const QString &errorMessage);

    /**
     * @brief 创建解析成功结果
     * @param parsedData 解析数据
     * @param summary 摘要
     * @param detailInfo 详细信息
     * @return 成功结果
     */
    ProtocolParseResult createSuccessResult(const QVariantMap &parsedData,
                                          const QString &summary,
                                          const QString &detailInfo);

    /**
     * @brief 格式化字节数组为十六进制字符串
     * @param data 字节数组
     * @param separator 分隔符
     * @return 十六进制字符串
     */
    QString formatHexString(const QByteArray &data, const QString &separator = " ");



private:
    // 数据标识映射表
    QMap<quint32, QString> m_dataIdMap;

    // 功能码映射表
    QMap<quint8, QString> m_functionCodeMap;

    // 错误码映射表
    QMap<quint8, QString> m_errorCodeMap;

    // 数据标识配置管理器（单例引用）
    DataIdentifierConfig* m_dataConfig;

    ProtocolType m_protocol;
    /**
     * @brief 初始化映射表
     */
    void initializeMappings();
};

#endif // DLT645PARSER_H
