#include "archivehandler.h"
#include <QGuiApplication>
#include <QApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QDir>
#include <QProcess>
#include <QTemporaryDir>
#include <QFileInfo>
#include <QDirIterator>
#include <QDebug>
#include <QUrl>
#include <QStandardPaths>
#include <QTextCodec>
#include <QDateTime>
#include <QQmlEngine>
#include <QQmlComponent>
#include <QDesktopServices>
#include <QJsonObject>
#include <QJsonDocument>
#include <QCoreApplication>

// 导入功能模块头文件
#include "LogAnalyzer/loganalyzer.h"
#include "ParamViewer/paramviewer.h"
#include "DatabaseTool/databasetool.h"
#include "DatabaseTool/dataparser.h"
#include "ProtocolParser/messageparser.h"
#include "ProtocolParser/protocoltypes.h"
#include "ProtocolParser/dataidentifierconfig.h"
#include "ProtocolParser/dlt645parser.h"

// 定义全局变量
QQmlApplicationEngine* g_enginePtr = nullptr;

ArchiveHandler::ArchiveHandler(QObject *parent) 
    : QObject(parent), m_tempDir(nullptr), m_lastExtractionReused(false) 
{
}

ArchiveHandler::~ArchiveHandler() 
{
    if (m_tempDir) {
        delete m_tempDir;
    }
}

bool ArchiveHandler::lastExtractionReused() const 
{ 
    return m_lastExtractionReused; 
}

void ArchiveHandler::setLastExtractionReused(bool reused) 
{ 
    if (m_lastExtractionReused != reused) {
        m_lastExtractionReused = reused;
        emit lastExtractionReusedChanged(reused);
    }
}

QString ArchiveHandler::termDiskPath() const 
{
    return m_termDiskPath;
}

bool ArchiveHandler::openExistingDirectory(const QString &directoryPath) 
{
    qDebug() << "打开已存在的目录:" << directoryPath;

    // 重置复用状态
    setLastExtractionReused(false);

    // 检查目录是否存在
    QDir dir(directoryPath);
    if (!dir.exists()) {
        qWarning() << "指定的目录不存在:" << directoryPath;
        emit extractionFailed(QString("指定的目录不存在: %1").arg(directoryPath));
        return false;
    }

    // 获取目录名称，用于termdisk查找
    QFileInfo dirInfo(directoryPath);
    QString dirName = dirInfo.fileName();

    qDebug() << "在目录中查找termdisk:" << directoryPath << "目录名:" << dirName;

    // 在指定目录中查找termdisk
    QString termDiskDir = findTermDiskDir(directoryPath, dirName);
    if (!termDiskDir.isEmpty()) {
        qDebug() << "在已存在目录中找到termdisk目录:" << termDiskDir;
        m_termDiskPath = termDiskDir;
        emit termDiskPathChanged();

        // 设置标志，表示使用了已存在的目录
        setLastExtractionReused(true);

        // 通知QML解压提取完成
        emit extractionCompleted(m_termDiskPath);
        return true;
    } else {
        qWarning() << "在已存在目录中未找到termdisk目录:" << directoryPath;
        emit extractionCompleted("");
        return false;
    }
}

bool ArchiveHandler::hasTermDiskPath() const 
{
    return !m_termDiskPath.isEmpty();
}

QWidget* ArchiveHandler::createLogAnalyzerWidget()
{
    LogAnalyzer* analyzer = new LogAnalyzer();
    analyzer->setTermDiskPath(m_termDiskPath);
    return analyzer->createLogAnalyzerWidget();
}

QWidget* ArchiveHandler::createParamViewerWidget()
{
    ParamViewer* viewer = new ParamViewer();
    viewer->setTermDiskPath(m_termDiskPath);
    return viewer->createParamViewerWidget();
}

QWidget* ArchiveHandler::createDatabaseWidget()
{
    DatabaseTool* dbTool = new DatabaseTool();
    dbTool->setTermDiskPath(m_termDiskPath);
    return dbTool->createDatabaseWidget();
}

QWidget* ArchiveHandler::createProtocolParserWidget()
{
    MessageParser* parser = new MessageParser();
    // 不再需要设置配置文件路径，因为DataIdentifierConfig已经是单例，在主程序启动时已经初始化
    qDebug() << "创建协议解析器，使用全局DataIdentifierConfig单例";
    return parser->createProtocolParserWidget();
}

// 静态函数实现
void handleLastExtractionReusedChanged(bool reused) 
{
    if (g_enginePtr) {
        QObject *rootObject = g_enginePtr->rootObjects().first();
        if (rootObject) {
            QMetaObject::invokeMethod(rootObject, "lastExtractionReusedChanged",
                                  Q_ARG(QVariant, reused));
        }
    }
}

bool ArchiveHandler::extractArchive(const QString &archivePath)
{
    // 重置复用状态
    setLastExtractionReused(false);

    // 使用项目目录下的tmp文件夹，而不是系统临时目录
    QString projectDir = QDir::currentPath();
    QString tmpDir = projectDir + "/tmp";

    qDebug() << "开始解压文件:" << archivePath;
    qDebug() << "当前工作目录:" << projectDir;
    qDebug() << "解压目标目录:" << tmpDir;

    // 确保tmp目录存在
    QDir dir;
    if (!dir.exists(tmpDir)) {
        dir.mkpath(tmpDir);
        qDebug() << "创建tmp目录:" << tmpDir;
    }

    // 处理文件名称
    QFileInfo fileInfo(archivePath);
    QString fileName = fileInfo.fileName();
    QString localFile = archivePath;

    // 移除可能的文件URL前缀
    if (localFile.startsWith("file:///")) {
        localFile = QUrl(localFile).toLocalFile();
    } else if (localFile.startsWith("file://")) {
        // 修复file://格式的URL（没有第三个斜杠）
        localFile = "file:///" + localFile.mid(7);
        localFile = QUrl(localFile).toLocalFile();
    }
    qDebug() << "解压文件路径:" << localFile;

    // 检查文件是否存在
    if (!QFileInfo::exists(localFile)) {
        qWarning() << "文件不存在:" << localFile;
        emit extractionFailed("文件不存在: " + localFile);
        return false;
    }

    // 检查是否是已存在的目录路径（从历史记录中选择）
    bool isExistingDir = QDir(localFile).exists() && !QFile(localFile).exists();
    if (isExistingDir) {
        qDebug() << "选择了已存在的目录:" << localFile;

        // 直接设置该目录为termDiskPath，不检查termdisk
        m_termDiskPath = localFile;
        emit termDiskPathChanged();

        // 设置标志，表示使用了已存在的目录
        setLastExtractionReused(true);

        // 通知QML解压提取完成
        emit extractionCompleted(m_termDiskPath);
        return true;
    }

    // 获取不带扩展名的文件名，处理可能包含日期时间的文件名
    QString fileBaseName;
    qDebug() << "处理文件名:" << fileName;

    // 对于所有文件，都使用不带扩展名的文件名作为目录名
    fileBaseName = fileInfo.completeBaseName();
    qDebug() << "使用不带扩展名的文件名作为目录名:" << fileBaseName;

    // 确保文件名不包含特殊字符
    fileBaseName.replace(':', '_');
    fileBaseName.replace(' ', '_');

    // 检查tmp目录下是否已经存在与压缩包同名的文件夹
    QString existingDirPath = tmpDir + "/" + fileBaseName;
    bool dirExists = QDir(existingDirPath).exists();

    qDebug() << "检查目录是否存在:" << existingDirPath << "结果:" << dirExists;

    QString extractDir = tmpDir;

    // 如果已存在同名目录，则直接使用该目录，不再解压
    if (dirExists) {
        qDebug() << "找到已存在的目录，跳过解压步骤:" << existingDirPath;

        // 查找termdisk目录
        QString termDiskDir = findTermDiskDir(existingDirPath, fileBaseName);
        if (!termDiskDir.isEmpty()) {
            qDebug() << "在已存在目录中找到termdisk目录:" << termDiskDir;
            m_termDiskPath = termDiskDir;
            emit termDiskPathChanged();

            // 设置标志，表示使用了已存在的目录
            setLastExtractionReused(true);

            // 通知QML解压提取完成
            emit extractionCompleted(m_termDiskPath);
            return true;
        } else {
            qWarning() << "在已存在目录中未找到termdisk目录";
            // 如果在已存在目录中找不到termdisk，继续执行解压操作
        }
    } else {
        qDebug() << "未找到已存在的目录，需要解压文件";
    }

    // 如果没有找到已存在的目录或者在已存在目录中没有找到termdisk，则执行解压操作
    if (!dirExists || m_termDiskPath.isEmpty()) {
        // 不再清空旧的tmp目录内容，保留现有文件

        qDebug() << "解压目标目录:" << extractDir;

        bool success = false;

        // 根据文件扩展名选择解压方法，直接解压到tmp目录
        if (fileName.endsWith(".zip", Qt::CaseInsensitive)) {
            qDebug() << "解压zip文件直接到tmp目录:" << extractDir;
            success = extractZip(localFile, extractDir);
        } else if (fileName.endsWith(".rar", Qt::CaseInsensitive)) {
            qDebug() << "解压rar文件直接到tmp目录:" << extractDir;
            success = extractRar(localFile, extractDir);
        } else if (fileName.contains("term") || fileName.contains("Term")) {
            // 特殊处理没有明确扩展名的终端日志文件（如term50_2025.07.01-11.14.21）
            // 尝试使用zip格式解压
            qDebug() << "检测到可能的终端日志文件，尝试使用zip格式解压到tmp目录:" << fileName;
            success = extractZip(localFile, extractDir);

            // 如果zip解压失败，尝试rar格式
            if (!success) {
                qDebug() << "zip解压失败，尝试使用rar格式";
                success = extractRar(localFile, extractDir);
            }
        } else {
            // 对于其他未知格式，尝试常见的压缩格式
            qDebug() << "未知格式文件，尝试多种解压方式到tmp目录:" << fileName;

            // 先尝试zip
            success = extractZip(localFile, extractDir);

            // 如果zip失败，尝试rar
            if (!success) {
                qDebug() << "zip解压失败，尝试使用rar格式";
                success = extractRar(localFile, extractDir);
            }

            if (!success) {
                qWarning() << "无法识别的压缩文件格式:" << fileName;
                emit extractionFailed("无法识别的压缩文件格式: " + fileName);
                return false;
            }
        }

        if (!success) {
            qWarning() << "解压失败";
            emit extractionFailed("解压文件失败: " + fileName);
            return false;
        }
    }

    // 查找termdisk目录
    QString termDiskDir;
    if (!fileBaseName.isEmpty()) {
        // 优先在刚解压的目录中查找
        termDiskDir = findTermDiskDir(extractDir, fileBaseName);
    } else {
        // 兼容旧的查找方式
        termDiskDir = findTermDiskDir(extractDir);
    }

    if (!termDiskDir.isEmpty()) {
        qDebug() << "找到termdisk目录:" << termDiskDir;
        m_termDiskPath = termDiskDir;
        emit termDiskPathChanged();

        // 通知QML解压提取完成
        emit extractionCompleted(m_termDiskPath);
        return true;
    }

    qWarning() << "termdisk directory not found in extracted files";
    // 通知QML解压完成，但未找到termdisk目录
    emit extractionCompleted("");
    return false;
}

void ArchiveHandler::readLogFile(const QString& filePath)
{
    qDebug() << "C++: 开始读取日志文件:" << filePath;

    QString localFile = filePath;
    // 移除可能的文件URL前缀
    if (localFile.startsWith("file:///")) {
        localFile = QUrl(localFile).toLocalFile();
    }
    qDebug() << "C++: 本地文件路径:" << localFile;

    QFile file(localFile);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "C++: 无法打开文件:" << localFile;
        // 通知QML文件读取失败
        emit logFileReadCompleted(filePath, "无法打开文件: " + localFile);
        return;
    }

    // 读取文件内容
    QByteArray data = file.readAll();
    file.close();

    // 使用GB2312编码解码文本
    QTextCodec *codec = QTextCodec::codecForName("GB18030"); // GB18030包含GB2312
    if (!codec) {
        qWarning() << "C++: 无法找到GB18030解码器，尝试GB2312";
        codec = QTextCodec::codecForName("GB2312");
    }

    if (!codec) {
        qWarning() << "C++: 无法找到GB2312解码器，尝试系统默认编码";
        codec = QTextCodec::codecForLocale();
    }

    QString content;
    if (codec) {
        content = codec->toUnicode(data);
        qDebug() << "C++: 成功解码文件内容，长度:" << content.length();
    } else {
        qWarning() << "C++: 无法找到合适的编码器";
        content = QString::fromUtf8(data); // 尝试使用UTF-8解码
        qDebug() << "C++: 使用UTF-8解码，长度:" << content.length();
    }

    // 通知QML文件读取完成
    emit logFileReadCompleted(filePath, content);
}

QVariantMap ArchiveHandler::getFileInfo(const QString& filePath)
{
    QVariantMap result;

    QString localFile = filePath;
    // 移除可能的文件URL前缀
    if (localFile.startsWith("file:///")) {
        localFile = QUrl(localFile).toLocalFile();
    }

    QFileInfo fileInfo(localFile);
    if (fileInfo.exists()) {
        result["exists"] = true;
        result["size"] = fileInfo.size();
        result["lastModified"] = fileInfo.lastModified().toMSecsSinceEpoch(); // 最后修改时间（毫秒）
        result["created"] = fileInfo.birthTime().toMSecsSinceEpoch(); // 创建时间（毫秒）
        result["isDir"] = fileInfo.isDir();
        result["isFile"] = fileInfo.isFile();
        result["fileName"] = fileInfo.fileName();
        result["filePath"] = fileInfo.filePath();
    } else {
        result["exists"] = false;
    }

    return result;
}

bool ArchiveHandler::checkLogsExist(const QString& dirPath)
{
    QString localDir = dirPath;
    // 移除可能的文件URL前缀
    if (localDir.startsWith("file:///")) {
        localDir = QUrl(localDir).toLocalFile();
    }

    QDir dir(localDir);
    if (!dir.exists()) {
        qDebug() << "目录不存在:" << localDir;
        return false;
    }

    // 设置过滤器，只查找日志文件
    QStringList filters;
    filters << "*.log" << "*.amalgamation.log";
    dir.setNameFilters(filters);

    QFileInfoList fileList = dir.entryInfoList(QDir::Files);
    return !fileList.isEmpty();
}

QStringList ArchiveHandler::listLogFiles(const QString& dirPath)
{
    QStringList result;

    QString localDir = dirPath;
    // 移除可能的文件URL前缀
    if (localDir.startsWith("file:///")) {
        localDir = QUrl(localDir).toLocalFile();
    }

    QDir dir(localDir);
    if (!dir.exists()) {
        qDebug() << "目录不存在:" << localDir;
        return result;
    }

    // 设置过滤器，只查找日志文件
    QStringList filters;
    filters << "*.log" << "*.amalgamation.log";
    dir.setNameFilters(filters);

    QFileInfoList fileList = dir.entryInfoList(QDir::Files);
    foreach (const QFileInfo &fileInfo, fileList) {
        // 使用QUrl格式化路径，以便在QML中使用
        QString filePath = QUrl::fromLocalFile(fileInfo.absoluteFilePath()).toString();
        result.append(filePath);
    }

    return result;
}

void ArchiveHandler::processLogsWithBat()
{
    if (m_termDiskPath.isEmpty()) {
        qDebug() << "termdisk路径为空，无法处理日志";
        emit logProcessingCompleted("");
        return;
    }

    // 获取termdisk的父目录
    QString termDiskDir = m_termDiskPath;
    if (termDiskDir.startsWith("file:///")) {
        termDiskDir = QUrl(termDiskDir).toLocalFile();
    }
    QString parentDir = QFileInfo(termDiskDir).absolutePath();
    QString logsDir = parentDir + "/logs";

    // 确保logs目录存在
    QDir dir;
    if (!dir.exists(logsDir)) {
        dir.mkpath(logsDir);
    }

    // 从可执行文件目录下的tools文件夹中查找bat文件
    QString appDir = QCoreApplication::applicationDirPath();
    QString sourceBatPath = appDir + "/tools/loghandle.bat";
    QString destBatPath = parentDir + "/loghandle.bat";

    qDebug() << "查找bat文件路径:" << sourceBatPath;

    if (!QFile::exists(sourceBatPath)) {
        qDebug() << "可执行文件目录下未找到loghandle.bat:" << sourceBatPath;

        // 尝试在当前目录的tools文件夹中查找
        sourceBatPath = QDir::currentPath() + "/tools/loghandle.bat";
        qDebug() << "尝试在当前目录下查找:" << sourceBatPath;

        if (!QFile::exists(sourceBatPath)) {
            qDebug() << "在当前目录下也未找到loghandle.bat";
            emit logProcessingCompleted("");
            return;
        }
    }

    // 拷贝到目标目录
    if (QFile::exists(destBatPath)) {
        QFile::remove(destBatPath);
    }
    if (!QFile::copy(sourceBatPath, destBatPath)) {
        qDebug() << "拷贝loghandle.bat失败:" << sourceBatPath << "->" << destBatPath;
        emit logProcessingCompleted("");
        return;
    }
    qDebug() << "已将loghandle.bat拷贝到:" << destBatPath;

    // 通知QML批处理开始
    // 使用全局应用程序引擎获取根对象
    if (g_enginePtr) {
        QObject *rootObject = g_enginePtr->rootObjects().first();
        if (rootObject) {
            QMetaObject::invokeMethod(rootObject, "handleBatchProcessStarted",
                                Q_ARG(QVariant, QString("正在处理日志文件，请稍候...")));
        }
    }

    // 创建一个新的QProcess对象用于后台执行
    QProcess *process = new QProcess(this);
    process->setWorkingDirectory(parentDir);

    // 连接信号，在进程完成时处理结果
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
        [this, process, logsDir](int exitCode, QProcess::ExitStatus exitStatus) {
            bool success = (exitCode == 0 && exitStatus == QProcess::NormalExit);
            QByteArray output = process->readAllStandardOutput();
            QByteArray error = process->readAllStandardError();

            qDebug() << "BAT执行完成，退出码:" << exitCode << "状态:" << exitStatus;
            qDebug() << "标准输出:" << output;
            if (!error.isEmpty()) {
                qDebug() << "错误输出:" << error;
            }

            // 通知QML批处理结束
            if (g_enginePtr) {
                QObject *rootObject = g_enginePtr->rootObjects().first();
                if (rootObject) {
                    QString message = success ?
                        "日志处理成功完成！" :
                        "日志处理过程中出现问题，请检查日志";

                    QMetaObject::invokeMethod(rootObject, "handleBatchProcessFinished",
                                        Q_ARG(QVariant, success),
                                        Q_ARG(QVariant, message));
                }
            }

            // 通知处理完成
            emit logProcessingCompleted(success ? logsDir : "");

            // 删除进程对象
            process->deleteLater();
        });

    // 启动批处理
    process->start(destBatPath, QStringList());
    if (!process->waitForStarted(3000)) {
        qDebug() << "启动BAT失败";
        process->deleteLater();

        // 通知QML批处理失败
        if (g_enginePtr) {
            QObject *rootObject = g_enginePtr->rootObjects().first();
            if (rootObject) {
                QMetaObject::invokeMethod(rootObject, "handleBatchProcessFinished",
                                    Q_ARG(QVariant, false),
                                    Q_ARG(QVariant, QString("无法启动批处理脚本")));
            }
        }

        emit logProcessingCompleted("");
    } else {
        qDebug() << "批处理脚本已在后台启动";
    }
}

bool ArchiveHandler::openFileWithApp(const QString &appPath, const QString &filePath)
{
    qDebug() << "打开应用程序:" << appPath << "文件:" << filePath;

    QString localAppPath = appPath;
    QString localFilePath = filePath;

    // 移除可能的文件URL前缀
    if (localAppPath.startsWith("file:///")) {
        localAppPath = QUrl(localAppPath).toLocalFile();
    } else if (localAppPath.startsWith("file://")) {
        // 修复file://格式的URL（没有第三个斜杠）
        localAppPath = "file:///" + localAppPath.mid(7);
        localAppPath = QUrl(localAppPath).toLocalFile();
    }

    if (localFilePath.startsWith("file:///")) {
        localFilePath = QUrl(localFilePath).toLocalFile();
    } else if (localFilePath.startsWith("file://")) {
        // 修复file://格式的URL（没有第三个斜杠）
        localFilePath = "file:///" + localFilePath.mid(7);
        localFilePath = QUrl(localFilePath).toLocalFile();
    }

    qDebug() << "本地应用程序路径:" << localAppPath;
    qDebug() << "本地文件路径:" << localFilePath;

    // 检查应用程序和文件是否存在
    if (!QFile::exists(localAppPath)) {
        qWarning() << "应用程序不存在:" << localAppPath;
        return false;
    }

    if (!QFile::exists(localFilePath)) {
        qWarning() << "文件不存在:" << localFilePath;
        return false;
    }

    // 使用QProcess启动应用程序
    QProcess process;
    QStringList arguments;
    arguments << localFilePath;

    process.startDetached(localAppPath, arguments);
    return true;
}

bool ArchiveHandler::openFileDirectory(const QString &filePath)
{
    qDebug() << "打开文件所在目录:" << filePath;

    QString localFilePath = filePath;

    // 移除可能的文件URL前缀
    if (localFilePath.startsWith("file:///")) {
        localFilePath = QUrl(localFilePath).toLocalFile();
    } else if (localFilePath.startsWith("file://")) {
        // 修复file://格式的URL（没有第三个斜杠）
        localFilePath = "file:///" + localFilePath.mid(7);
        localFilePath = QUrl(localFilePath).toLocalFile();
    }

    qDebug() << "本地文件路径:" << localFilePath;

    // 获取文件所在目录
    QFileInfo fileInfo(localFilePath);
    QString dirPath = fileInfo.absolutePath();

    qDebug() << "目录路径:" << dirPath;

    // 检查目录是否存在
    if (!QDir(dirPath).exists()) {
        qWarning() << "目录不存在:" << dirPath;
        return false;
    }

#ifdef Q_OS_WIN
    // Windows下使用explorer打开目录
    QProcess process;
    // 不要使用/select参数，直接打开目录
    process.startDetached("explorer.exe", QStringList() << QDir::toNativeSeparators(dirPath));
    qDebug() << "启动explorer.exe打开目录:" << QDir::toNativeSeparators(dirPath);
#else
    // 其他平台使用系统默认文件管理器
    QDesktopServices::openUrl(QUrl::fromLocalFile(dirPath));
    qDebug() << "使用QDesktopServices打开目录:" << dirPath;
#endif

    return true;
}

bool ArchiveHandler::saveBatFile(const QString &filePath, const QString &content)
{
    qDebug() << "C++: 保存BAT文件:" << filePath;

    QString localFilePath = filePath;
    // 移除可能的文件URL前缀
    if (localFilePath.startsWith("file:///")) {
        localFilePath = QUrl(localFilePath).toLocalFile();
    }

    // 确保目录存在
    QFileInfo fileInfo(localFilePath);
    QDir dir = fileInfo.dir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            qWarning() << "C++: 无法创建目录:" << dir.path();
            return false;
        }
    }

    QFile file(localFilePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "C++: 无法打开文件进行写入:" << localFilePath;
        return false;
    }

    // 将内容转换为ANSI编码（Windows批处理文件需要）
    QTextCodec *codec = QTextCodec::codecForName("System");
    QByteArray encodedContent = codec->fromUnicode(content);

    // 写入文件
    qint64 bytesWritten = file.write(encodedContent);
    file.close();

    if (bytesWritten == -1) {
        qWarning() << "C++: 写入文件失败:" << localFilePath;
        return false;
    }

    qDebug() << "C++: BAT文件保存成功:" << localFilePath;
    return true;
}

bool ArchiveHandler::executeBatFile(const QString &filePath)
{
    qDebug() << "C++: 执行BAT文件:" << filePath;

    QString localFilePath = filePath;
    // 移除可能的文件URL前缀
    if (localFilePath.startsWith("file:///")) {
        localFilePath = QUrl(localFilePath).toLocalFile();
    }

    // 检查文件是否存在
    QFileInfo fileInfo(localFilePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        qWarning() << "C++: BAT文件不存在:" << localFilePath;
        return false;
    }

    // 获取工作目录（BAT文件所在目录的上一级）
    QString workingDir = fileInfo.dir().path();
    workingDir = QDir(workingDir).absolutePath();
    if (workingDir.endsWith("/custom_rules") || workingDir.endsWith("\\custom_rules")) {
        workingDir = QDir(workingDir).filePath("..");
        workingDir = QDir(workingDir).absolutePath();
    }

    // 创建进程对象
    QProcess *process = new QProcess(this);
    process->setWorkingDirectory(workingDir);

    // 设置环境变量
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    process->setProcessEnvironment(env);

    // 连接信号
    connect(process, &QProcess::readyReadStandardOutput, [process]() {
        QString output = QString::fromLocal8Bit(process->readAllStandardOutput());
        qDebug() << "BAT输出:" << output;
    });

    connect(process, &QProcess::readyReadStandardError, [process]() {
        QString error = QString::fromLocal8Bit(process->readAllStandardError());
        qWarning() << "BAT错误:" << error;
    });

    // 批处理完成后的处理
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [this, process, localFilePath](int exitCode, QProcess::ExitStatus exitStatus) {
        qDebug() << "BAT执行完成，退出代码:" << exitCode;

        // 发送批处理完成信号
        bool success = (exitCode == 0 && exitStatus == QProcess::NormalExit);
        QString message;
        if (success) {
            message = "自定义合并规则执行成功！";
            // 获取logs目录
            QFileInfo fileInfo(localFilePath);
            QString logsDir = QDir(fileInfo.dir().filePath("../..")).absolutePath() + "/logs";
            emit logProcessingCompleted(logsDir);
        } else {
            message = "自定义合并规则执行失败，退出代码: " + QString::number(exitCode);
        }

        // 通知QML批处理完成
        emit batchProcessFinished(success, message);

        // 清理进程
        process->deleteLater();
    });

    // 通知QML批处理开始
    emit batchProcessStarted("正在执行自定义合并规则...");

    // 启动进程
    process->start("cmd.exe", QStringList() << "/c" << localFilePath);

    return true;
}

bool ArchiveHandler::directoryExists(const QString &dirPath)
{
    QString localDirPath = dirPath;
    // 移除可能的文件URL前缀
    if (localDirPath.startsWith("file:///")) {
        localDirPath = QUrl(localDirPath).toLocalFile();
    }

    QDir dir(localDirPath);
    return dir.exists();
}

bool ArchiveHandler::ensureDirectoryExists(const QString &dirPath)
{
    QString localDirPath = dirPath;
    // 移除可能的文件URL前缀
    if (localDirPath.startsWith("file:///")) {
        localDirPath = QUrl(localDirPath).toLocalFile();
    }

    QDir dir(localDirPath);
    if (!dir.exists()) {
        return dir.mkpath(".");
    }
    return true;
}

bool ArchiveHandler::copyFiles(const QString &sourceDir, const QString &destDir, const QString &filter)
{
    QString localSourceDir = sourceDir;
    QString localDestDir = destDir;

    // 移除可能的文件URL前缀
    if (localSourceDir.startsWith("file:///")) {
        localSourceDir = QUrl(localSourceDir).toLocalFile();
    }
    if (localDestDir.startsWith("file:///")) {
        localDestDir = QUrl(localDestDir).toLocalFile();
    }

    // 确保目标目录存在
    QDir destDirObj(localDestDir);
    if (!destDirObj.exists()) {
        if (!destDirObj.mkpath(".")) {
            qWarning() << "C++: 无法创建目标目录:" << localDestDir;
            return false;
        }
    }

    // 获取源目录中的文件
    QDir sourceDirObj(localSourceDir);
    QStringList nameFilters;
    nameFilters << filter;
    QFileInfoList fileList = sourceDirObj.entryInfoList(nameFilters, QDir::Files);

    bool success = true;
    for (const QFileInfo &fileInfo : fileList) {
        QString sourceFilePath = fileInfo.absoluteFilePath();
        QString destFilePath = destDirObj.filePath(fileInfo.fileName());

        // 如果目标文件已存在，先删除
        if (QFile::exists(destFilePath)) {
            if (!QFile::remove(destFilePath)) {
                qWarning() << "C++: 无法删除已存在的目标文件:" << destFilePath;
                success = false;
                continue;
            }
        }

        // 复制文件
        if (!QFile::copy(sourceFilePath, destFilePath)) {
            qWarning() << "C++: 无法复制文件:" << sourceFilePath << "到" << destFilePath;
            success = false;
        } else {
            qDebug() << "C++: 成功复制文件:" << sourceFilePath << "到" << destFilePath;
        }
    }

    return success;
}

void ArchiveHandler::manualMergeLogFiles(const QString &backupDir, bool isCompressed,
                                    const QString &suffix, bool includeLatest,
                                    const QString &latestLocation, const QString &logType)
{
    qDebug() << "手动合并日志文件:";
    qDebug() << "  备份目录:" << backupDir;
    qDebug() << "  是否压缩:" << isCompressed;
    qDebug() << "  后缀名:" << suffix;
    qDebug() << "  包含最新日志:" << includeLatest;
    qDebug() << "  最新日志位置:" << latestLocation;
    qDebug() << "  日志类型:" << logType;

    if (m_termDiskPath.isEmpty()) {
        qDebug() << "termdisk路径为空，无法处理日志";
        emit logProcessingCompleted("");
        return;
    }

    // 获取termdisk的父目录
    QString termDiskDir = m_termDiskPath;
    if (termDiskDir.startsWith("file:///")) {
        termDiskDir = QUrl(termDiskDir).toLocalFile();
    }

    // 确保logs目录存在
    QString logsDir = QFileInfo(termDiskDir).absolutePath() + "/logs";
    QDir dir;
    if (!dir.exists(logsDir)) {
        dir.mkpath(logsDir);
    }

    // 通知QML批处理开始
    if (g_enginePtr) {
        QObject *rootObject = g_enginePtr->rootObjects().first();
        if (rootObject) {
            QMetaObject::invokeMethod(rootObject, "handleBatchProcessStarted",
                                Q_ARG(QVariant, QString("正在手动合并日志文件，请稍候...")));
        }
    }

    // 创建一个新的QProcess对象用于后台执行
    QProcess *process = new QProcess(this);

    // 设置工作目录
    QString parentDir = QFileInfo(termDiskDir).absolutePath();
    process->setWorkingDirectory(parentDir);

    // 构建命令行参数
    QStringList arguments;

    // 根据日志类型和后缀构建命令
    QString sourceLogPath;
    QString outputLogPath = logsDir + "/manual_merged_" + logType;

    // 确定备份日志路径
    QString backupLogDir = backupDir;
    if (backupLogDir.isEmpty()) {
        backupLogDir = termDiskDir + "/data0/var-bak/log";
    }
    if (backupLogDir.startsWith("file:///")) {
        backupLogDir = QUrl(backupLogDir).toLocalFile();
    }

    // 根据日志类型确定子目录
    QString logSubDir;
    if (logType.startsWith("user")) {
        logSubDir = "userlog";
        outputLogPath += ".amalgamation.log";
    } else {
        logSubDir = "commlog";
        outputLogPath += ".amalgamation.log";
    }

    // 创建输出文件
    QFile outFile(outputLogPath);
    if (!outFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法创建输出文件:" << outputLogPath;

        // 通知QML批处理失败
        if (g_enginePtr) {
            QObject *rootObject = g_enginePtr->rootObjects().first();
            if (rootObject) {
                QMetaObject::invokeMethod(rootObject, "handleBatchProcessFinished",
                                    Q_ARG(QVariant, false),
                                    Q_ARG(QVariant, QString("无法创建输出文件")));
            }
        }

        process->deleteLater();
        return;
    }

    // 写入文件头
    QDateTime now = QDateTime::currentDateTime();
    QString header = QString("# 手动合并的%1日志文件\n"
                           "# 合并时间: %2\n"
                           "# 备份目录: %3\n"
                           "# 后缀名: %4\n"
                           "# 包含最新日志: %5\n"
                           "# 最新日志位置: %6\n"
                           "# =================================================================\n\n")
                           .arg(logType)
                           .arg(now.toString("yyyy-MM-dd HH:mm:ss"))
                           .arg(backupLogDir)
                           .arg(suffix)
                           .arg(includeLatest ? "是" : "否")
                           .arg(latestLocation);
    outFile.write(header.toUtf8());

    // 处理备份日志文件
    QString backupFilePath;
    if (isCompressed) {
        // 压缩文件路径
        backupFilePath = backupLogDir + "/" + logSubDir + "/" + logType + ".tgz." + suffix;
    } else {
        // 普通文件路径
        backupFilePath = backupLogDir + "/" + logSubDir + "/" + logType;
    }

    qDebug() << "处理备份日志文件:" << backupFilePath;

    if (QFile::exists(backupFilePath)) {
        if (isCompressed) {
            // 解压缩文件
            QString tempDir = parentDir + "/temp_logs";
            QDir().mkpath(tempDir);

            // 创建临时tgz文件
            QString tempTgzPath = tempDir + "/temp_" + QFileInfo(backupFilePath).fileName();

            // 复制文件
            if (QFile::copy(backupFilePath, tempTgzPath)) {
                qDebug() << "成功复制文件到:" << tempTgzPath;

                // 解压tgz文件
                QProcess tarProcess;
                QStringList tarArgs;
                tarArgs << "-xzf" << tempTgzPath << "-C" << tempDir;

                tarProcess.start("tar", tarArgs);
                if (!tarProcess.waitForFinished(60000)) { // 等待60秒
                    qDebug() << "解压超时:" << tempTgzPath;
                } else if (tarProcess.exitCode() != 0) {
                    qDebug() << "解压失败:" << tarProcess.readAllStandardError();
                } else {
                    // 查找解压后的文件
                    QString extractedFile = tempDir + "/" + logType;
                    if (QFile::exists(extractedFile)) {
                        // 读取文件内容
                        QFile logFile(extractedFile);
                        if (logFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
                            QByteArray content = logFile.readAll();
                            logFile.close();

                            // 写入合并文件
                            outFile.write("\n\n==================== 备份日志 ====================\n");
                            outFile.write(("# 文件路径: " + backupFilePath + "\n\n").toUtf8());
                            outFile.write(content);

                            qDebug() << "成功合并备份日志:" << backupFilePath;
                        }

                        // 删除临时文件
                        QFile::remove(extractedFile);
                    }
                }

                // 删除临时tgz文件
                QFile::remove(tempTgzPath);
            }
        } else {
            // 直接读取普通文件
            QFile logFile(backupFilePath);
            if (logFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
                QByteArray content = logFile.readAll();
                logFile.close();

                // 写入合并文件
                outFile.write("\n\n==================== 备份日志 ====================\n");
                outFile.write(("# 文件路径: " + backupFilePath + "\n\n").toUtf8());
                outFile.write(content);

                qDebug() << "成功合并备份日志:" << backupFilePath;
            }
        }
    } else {
        qDebug() << "备份日志文件不存在:" << backupFilePath;
        outFile.write("\n\n==================== 备份日志 ====================\n");
        outFile.write(("# 文件路径: " + backupFilePath + " (不存在)\n\n").toUtf8());
    }

    // 处理最新日志文件
    if (includeLatest) {
        QString latestFilePath = termDiskDir + "/" + latestLocation + "/" + logSubDir + "/" + logType;
        qDebug() << "处理最新日志文件:" << latestFilePath;

        if (QFile::exists(latestFilePath)) {
            QFile logFile(latestFilePath);
            if (logFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
                QByteArray content = logFile.readAll();
                logFile.close();

                // 写入合并文件
                outFile.write("\n\n==================== 最新日志 ====================\n");
                outFile.write(("# 文件路径: " + latestFilePath + "\n\n").toUtf8());
                outFile.write(content);

                qDebug() << "成功合并最新日志:" << latestFilePath;
            }
        } else {
            qDebug() << "最新日志文件不存在:" << latestFilePath;
            outFile.write("\n\n==================== 最新日志 ====================\n");
            outFile.write(("# 文件路径: " + latestFilePath + " (不存在)\n\n").toUtf8());
        }
    }

    // 关闭输出文件
    outFile.close();

    qDebug() << "日志手动合并完成，输出文件:" << outputLogPath;

    // 通知QML批处理结束
    if (g_enginePtr) {
        QObject *rootObject = g_enginePtr->rootObjects().first();
        if (rootObject) {
            QString message = "日志手动合并成功完成！";

            QMetaObject::invokeMethod(rootObject, "handleBatchProcessFinished",
                                Q_ARG(QVariant, true),
                                Q_ARG(QVariant, message));
        }
    }

    // 通知处理完成
    emit logProcessingCompleted(logsDir);

    // 删除进程对象
    process->deleteLater();
}

QStringList ArchiveHandler::listCustomScripts()
{
    QStringList result;

    // 获取tools/custom_rules目录路径
    QString appDir = QCoreApplication::applicationDirPath();
    QString customRulesDir = appDir + "/tools/custom_rules";

    // 如果应用程序目录下没有找到，尝试当前工作目录
    if (!QDir(customRulesDir).exists()) {
        customRulesDir = QDir::currentPath() + "/tools/custom_rules";
    }

    qDebug() << "查找自定义脚本目录:" << customRulesDir;

    // 检查目录是否存在
    QDir dir(customRulesDir);
    if (!dir.exists()) {
        qDebug() << "自定义脚本目录不存在:" << customRulesDir;
        return result;
    }

    // 设置过滤器，只查找bat和sh文件
    QStringList filters;
    filters << "*.bat" << "*.sh";
    dir.setNameFilters(filters);

    // 获取文件列表
    QFileInfoList fileList = dir.entryInfoList(QDir::Files, QDir::Name);
    foreach (const QFileInfo &fileInfo, fileList) {
        QVariantMap scriptInfo;
        scriptInfo["name"] = fileInfo.fileName();
        scriptInfo["path"] = fileInfo.absoluteFilePath();

        // 将文件信息转换为JSON字符串
        QJsonObject jsonObj = QJsonObject::fromVariantMap(scriptInfo);
        QJsonDocument doc(jsonObj);
        QString jsonStr = QString::fromUtf8(doc.toJson());

        result.append(jsonStr);
    }

    return result;
}

bool ArchiveHandler::executeCustomScript(const QString &scriptPath)
{
    qDebug() << "执行自定义脚本:" << scriptPath;

    if (m_termDiskPath.isEmpty()) {
        qDebug() << "termdisk路径为空，无法执行脚本";
        return false;
    }

    // 获取termdisk的父目录
    QString termDiskDir = m_termDiskPath;
    if (termDiskDir.startsWith("file:///")) {
        termDiskDir = QUrl(termDiskDir).toLocalFile();
    }
    QString parentDir = QFileInfo(termDiskDir).absolutePath();

    // 确保脚本文件存在
    QString localScriptPath = scriptPath;
    if (localScriptPath.startsWith("file:///")) {
        localScriptPath = QUrl(localScriptPath).toLocalFile();
    }

    if (!QFile::exists(localScriptPath)) {
        qDebug() << "脚本文件不存在:" << localScriptPath;
        return false;
    }

    // 获取脚本文件名
    QFileInfo scriptFileInfo(localScriptPath);
    QString scriptFileName = scriptFileInfo.fileName();
    QString destScriptPath = parentDir + "/" + scriptFileName;

    // 拷贝脚本到目标目录
    if (QFile::exists(destScriptPath)) {
        QFile::remove(destScriptPath);
    }
    if (!QFile::copy(localScriptPath, destScriptPath)) {
        qDebug() << "拷贝脚本失败:" << localScriptPath << "->" << destScriptPath;
        return false;
    }
    qDebug() << "已将脚本拷贝到:" << destScriptPath;

    // 通知QML批处理开始
    if (g_enginePtr) {
        QObject *rootObject = g_enginePtr->rootObjects().first();
        if (rootObject) {
            QMetaObject::invokeMethod(rootObject, "handleBatchProcessStarted",
                                Q_ARG(QVariant, QString("正在执行自定义脚本，请稍候...")));
        }
    }

    // 创建一个新的QProcess对象用于后台执行
    QProcess *process = new QProcess(this);
    process->setWorkingDirectory(parentDir);

    // 连接信号，在进程完成时处理结果
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
        [this, process, parentDir, scriptFileName](int exitCode, QProcess::ExitStatus exitStatus) {
            bool success = (exitCode == 0 && exitStatus == QProcess::NormalExit);
            QByteArray output = process->readAllStandardOutput();
            QByteArray error = process->readAllStandardError();

            qDebug() << "脚本执行完成，退出码:" << exitCode << "状态:" << exitStatus;
            qDebug() << "标准输出:" << output;
            if (!error.isEmpty()) {
                qDebug() << "错误输出:" << error;
            }

            // 通知QML批处理结束
            if (g_enginePtr) {
                QObject *rootObject = g_enginePtr->rootObjects().first();
                if (rootObject) {
                    QString message = success ?
                        "脚本 " + scriptFileName + " 执行成功完成！" :
                        "脚本 " + scriptFileName + " 执行过程中出现问题，请检查日志";

                    QMetaObject::invokeMethod(rootObject, "handleBatchProcessFinished",
                                        Q_ARG(QVariant, success),
                                        Q_ARG(QVariant, message));
                }
            }

            // 通知处理完成
            QString logsDir = parentDir + "/logs";
            emit logProcessingCompleted(success ? logsDir : "");

            // 删除进程对象
            process->deleteLater();
        });

    // 启动脚本
    process->start(destScriptPath, QStringList());
    if (!process->waitForStarted(3000)) {
        qDebug() << "启动脚本失败";
        process->deleteLater();

        // 通知QML批处理失败
        if (g_enginePtr) {
            QObject *rootObject = g_enginePtr->rootObjects().first();
            if (rootObject) {
                QMetaObject::invokeMethod(rootObject, "handleBatchProcessFinished",
                                    Q_ARG(QVariant, false),
                                    Q_ARG(QVariant, QString("无法启动脚本 " + scriptFileName)));
            }
        }

        return false;
    } else {
        qDebug() << "脚本已在后台启动";
        return true;
    }
}

bool ArchiveHandler::removeDirectory(const QString& dirPath)
{
    QString localDirPath = dirPath;
    // 移除可能的文件URL前缀
    if (localDirPath.startsWith("file:///")) {
        localDirPath = QUrl(localDirPath).toLocalFile();
    }

    QDir dir(localDirPath);
    if (!dir.exists()) {
        qDebug() << "要删除的目录不存在:" << localDirPath;
        return false;
    }

    qDebug() << "开始删除目录:" << localDirPath;

    // 递归删除目录及其所有内容
    bool success = dir.removeRecursively();

    if (success) {
        qDebug() << "成功删除目录:" << localDirPath;
    } else {
        qWarning() << "删除目录失败:" << localDirPath;
    }

    return success;
}

QStringList ArchiveHandler::listTmpDirectories()
{
    QStringList result;

    // 使用项目目录下的tmp文件夹
    QString projectDir = QDir::currentPath();
    QString tmpDir = projectDir + "/tmp";

    // 检查tmp目录是否存在
    QDir dir(tmpDir);
    if (!dir.exists()) {
        qDebug() << "tmp目录不存在:" << tmpDir;
        return result;
    }

    // 获取tmp目录下的子目录列表
    QFileInfoList dirList = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot, QDir::Time);
    qDebug() << "tmp目录下共有" << dirList.size() << "个子目录";

    foreach (const QFileInfo &dirInfo, dirList) {
        // 只添加目录信息，不检查termdisk
        QVariantMap dirData;
        dirData["name"] = dirInfo.fileName();
        dirData["path"] = dirInfo.absoluteFilePath();
        dirData["modified"] = dirInfo.lastModified().toMSecsSinceEpoch();

        // 将目录信息转换为JSON字符串
        QJsonObject jsonObj = QJsonObject::fromVariantMap(dirData);
        QJsonDocument doc(jsonObj);
        QString jsonStr = QString::fromUtf8(doc.toJson());

        result.append(jsonStr);
        qDebug() << "找到tmp子目录:" << dirInfo.fileName()
                 << "路径:" << dirInfo.absoluteFilePath();
    }

    qDebug() << "共找到" << result.size() << "个目录记录";
    return result;
}

bool ArchiveHandler::extractZip(const QString &zipFile, const QString &extractDir)
{
    // 使用可执行文件目录下的tools\7z2500-extra目录中的7za.exe进行解压
#ifdef Q_OS_WIN
    // 获取应用程序目录
    QString appDir = QCoreApplication::applicationDirPath();
    QString sevenZipPath = appDir + "/tools/7z2500-extra/7za.exe";

    // 检查7za.exe是否存在，如果不存在，尝试在当前目录下查找
    if (!QFileInfo::exists(sevenZipPath)) {
        qWarning() << "在应用程序目录下未找到7za.exe:" << sevenZipPath;

        // 尝试在当前目录的tools文件夹中查找
        sevenZipPath = QDir::currentPath() + "/tools/7z2500-extra/7za.exe";
        qDebug() << "尝试在当前目录下查找7za.exe:" << sevenZipPath;

        if (!QFileInfo::exists(sevenZipPath)) {
            qWarning() << "在当前目录下也未找到7za.exe";
            return false;
        }
    }

    QString program = sevenZipPath;
    QStringList arguments;

    // 确保目标目录存在
    QDir().mkpath(extractDir);

    // 修正7za命令行参数
    arguments << "x";
    arguments << QDir::toNativeSeparators(zipFile);
    arguments << "-o" + QDir::toNativeSeparators(extractDir);
    arguments << "-y";
    arguments << "-bsp0";
    arguments << "-bso0";
    arguments << "-bse0";

    qDebug() << "执行7za命令:" << program << arguments.join(" ");
#else
    QString program = "unzip";
    QStringList arguments;
    // 添加-q参数减少输出，提高性能
    arguments << "-o" << "-q" << zipFile << "-d" << extractDir;
#endif

    QProcess process;
    // 设置进程优先级为高，提高解压速度
    process.setProcessChannelMode(QProcess::MergedChannels);
    process.start(program, arguments);

    // 检查进程是否成功启动
    if (!process.waitForStarted(3000)) {
        qWarning() << "启动解压进程失败:" << process.errorString();
        return false;
    }

    // 使用事件循环而不是阻塞等待，提高响应性
    QEventLoop loop;
    QObject::connect(&process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                    &loop, &QEventLoop::quit);
    loop.exec();

    bool success = process.exitCode() == 0;
    if (!success) {
        QString errorOutput = process.readAllStandardOutput() + "\n" + process.readAllStandardError();
        qWarning() << "解压失败，退出码:" << process.exitCode() << "错误:" << errorOutput;
    } else {
        qDebug() << "解压成功完成";
    }
    return success;
}

bool ArchiveHandler::extractRar(const QString &rarFile, const QString &extractDir)
{
#ifdef Q_OS_WIN
    // Windows下使用系统的rar命令行工具解压
    QString program = "rar";
    QStringList arguments;

    // 确保目标目录存在
    QDir().mkpath(extractDir);

    // 使用rar标准命令行参数
    arguments << "x"; // 解压命令
    arguments << "-y"; // 对所有询问自动回复"是"
    arguments << QDir::toNativeSeparators(rarFile); // RAR文件路径
    arguments << QDir::toNativeSeparators(extractDir); // 目标目录

    qDebug() << "执行rar命令:" << program << arguments.join(" ");

    // 如果rar命令不可用，尝试使用WinRAR
    QProcess checkProcess;
    checkProcess.start(program, QStringList() << "-?" );
    bool rarAvailable = checkProcess.waitForStarted(1000);
    checkProcess.kill();

    if (!rarAvailable) {
        qDebug() << "rar命令不可用，尝试使用WinRAR";

        // 尝试查找WinRAR安装路径
        QStringList possiblePaths = {
            "C:/Program Files/WinRAR/WinRAR.exe",
            "C:/Program Files (x86)/WinRAR/WinRAR.exe"
        };

        bool found = false;
        foreach (const QString &path, possiblePaths) {
            if (QFile::exists(path)) {
                program = path;
                found = true;
                qDebug() << "找到WinRAR路径:" << program;
                break;
            }
        }

        if (!found) {
            qWarning() << "未找到RAR或WinRAR程序，回退到使用7za";

            // 回退到使用7za.exe
            QString appDir = QCoreApplication::applicationDirPath();
            QString sevenZipPath = appDir + "/tools/7z2500-extra/7za.exe";

            // 检查7za.exe是否存在，如果不存在，尝试在当前目录下查找
            if (!QFileInfo::exists(sevenZipPath)) {
                qWarning() << "在应用程序目录下未找到7za.exe:" << sevenZipPath;

                // 尝试在当前目录的tools文件夹中查找
                sevenZipPath = QDir::currentPath() + "/tools/7z2500-extra/7za.exe";
                qDebug() << "尝试在当前目录下查找7za.exe:" << sevenZipPath;

                if (!QFileInfo::exists(sevenZipPath)) {
                    qWarning() << "在当前目录下也未找到7za.exe";
                    return false;
                }
            }

            program = sevenZipPath;
            arguments.clear();
            arguments << "x";
            arguments << QDir::toNativeSeparators(rarFile);
            arguments << "-o" + QDir::toNativeSeparators(extractDir);
            arguments << "-y";
            arguments << "-bsp0";
            arguments << "-bso0";
            arguments << "-bse0";

            qDebug() << "回退使用7za命令:" << program << arguments.join(" ");
        }
    }
#else
    QString program = "unrar";
    QStringList arguments;
    // 添加-mt参数启用多线程解压
    arguments << "x" << "-y" << "-mt" << rarFile << extractDir;
#endif

    QProcess process;
    process.setProcessChannelMode(QProcess::MergedChannels);
    process.start(program, arguments);

    // 检查进程是否成功启动
    if (!process.waitForStarted(3000)) {
        qWarning() << "启动解压进程失败:" << process.errorString();
        return false;
    }

    // 使用事件循环而不是阻塞等待，提高响应性
    QEventLoop loop;
    QObject::connect(&process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                    &loop, &QEventLoop::quit);
    loop.exec();

    bool success = process.exitCode() == 0;
    if (!success) {
        QString errorOutput = process.readAllStandardOutput() + "\n" + process.readAllStandardError();
        qWarning() << "解压失败，退出码:" << process.exitCode() << "错误:" << errorOutput;
    } else {
        qDebug() << "解压成功完成";
    }
    return success;
}

QString ArchiveHandler::findTermDiskDir(const QString &rootDir, const QString &extractedDirName)
{
    // 递归查找termdisk目录
    qDebug() << "查找termdisk目录，从根目录:" << rootDir;
    qDebug() << "优先查找目录:" << (extractedDirName.isEmpty() ? "无" : extractedDirName);

    // 确保路径格式正确
    QString localRootDir = rootDir;
    if (localRootDir.startsWith("file:///")) {
        localRootDir = QUrl(localRootDir).toLocalFile();
    } else if (localRootDir.startsWith("file://")) {
        // 修复file://格式的URL（没有第三个斜杠）
        localRootDir = "file:///" + localRootDir.mid(7);
        localRootDir = QUrl(localRootDir).toLocalFile();
    }

    qDebug() << "本地根目录路径:" << localRootDir;

    // 如果指定了优先查找的目录名称，只在该目录内查找
    if (!extractedDirName.isEmpty()) {
        QString priorityDirPath = localRootDir + "/" + extractedDirName;
        qDebug() << "优先检查目录:" << priorityDirPath;

        // 检查优先目录是否存在
        if (!QDir(priorityDirPath).exists()) {
            qDebug() << "优先目录不存在，尝试不使用子目录直接查找";

            // 直接检查根目录下是否有termdisk目录
            QString directTermDiskPath = localRootDir + "/termdisk";
            if (QDir(directTermDiskPath).exists()) {
                qDebug() << "在根目录中找到termdisk目录:" << directTermDiskPath;

                // 在termdisk同级目录下创建logs目录
                QString logsDir = localRootDir + "/logs";
                QDir logsQDir;
                if (!logsQDir.exists(logsDir)) {
                    qDebug() << "创建logs目录:" << logsDir;
                    logsQDir.mkpath(logsDir);
                }

                return directTermDiskPath;
            }

            // 检查根目录下的子目录
            QDir rootDirObj(localRootDir);
            QStringList subDirs = rootDirObj.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

            qDebug() << "检查根目录下的所有子目录，共" << subDirs.size() << "个";

            foreach (const QString &subDir, subDirs) {
                QString fullSubDirPath = localRootDir + "/" + subDir;
                qDebug() << "检查子目录:" << fullSubDirPath;

                // 检查该子目录下是否有termdisk目录
                QString termDiskPath = fullSubDirPath + "/termdisk";
                if (QDir(termDiskPath).exists()) {
                    qDebug() << "在子目录中找到termdisk目录:" << termDiskPath;

                    // 在termdisk同级目录下创建logs目录
                    QString logsDir = fullSubDirPath + "/logs";
                    QDir logsQDir;
                    if (!logsQDir.exists(logsDir)) {
                        qDebug() << "创建logs目录:" << logsDir;
                        logsQDir.mkpath(logsDir);
                    }

                    return termDiskPath;
                }
            }

            qDebug() << "未找到termdisk目录";
            return QString();
        }

        // 检查该目录下是否有termdisk目录
        QString termDiskPath = priorityDirPath + "/termdisk";
        if (QDir(termDiskPath).exists()) {
            qDebug() << "在优先目录中找到termdisk目录:" << termDiskPath;

            // 在termdisk同级目录下创建logs目录
            QString logsDir = priorityDirPath + "/logs";
            QDir logsQDir;
            if (!logsQDir.exists(logsDir)) {
                qDebug() << "创建logs目录:" << logsDir;
                logsQDir.mkpath(logsDir);
            }

            return termDiskPath;
        }

        // 如果在优先目录中未找到termdisk，递归搜索该目录下的子目录
        QDirIterator it(priorityDirPath, QDir::Dirs | QDir::NoDotAndDotDot, QDirIterator::Subdirectories);
        qDebug() << "开始递归搜索优先目录的子目录，搜索路径:" << priorityDirPath;
        int subDirCount = 0;
        while (it.hasNext()) {
            QString dirPath = it.next();
            QFileInfo fileInfo(dirPath);
            subDirCount++;
            qDebug() << "检查子目录 #" << subDirCount << ":" << dirPath << "名称:" << fileInfo.fileName();

            if (fileInfo.fileName().toLower() == "termdisk") {
                qDebug() << "在优先目录的子目录中找到termdisk目录:" << dirPath;

                // 在termdisk同级目录下创建logs目录
                QString parentPath = fileInfo.absolutePath();
                QString logsDir = parentPath + "/logs";
                QDir logsQDir;
                if (!logsQDir.exists(logsDir)) {
                    qDebug() << "创建logs目录:" << logsDir;
                    logsQDir.mkpath(logsDir);
                }

                return dirPath;
            }
        }

        qDebug() << "递归搜索完成，共检查了 " << subDirCount << " 个子目录，未找到termdisk目录";

        // 如果在指定的优先目录中未找到termdisk，不再继续查找
        qDebug() << "在优先目录及其子目录中未找到termdisk目录，不再继续查找";
        return QString();
    }

    // 如果没有指定优先目录，则在给定的根目录中查找
    // 先检查根目录下是否直接有termdisk目录
    QString termDiskPathDirect = localRootDir + "/termdisk";
    if (QDir(termDiskPathDirect).exists()) {
        qDebug() << "在根目录中找到termdisk目录:" << termDiskPathDirect;

        // 在termdisk同级目录下创建logs目录
        QString logsDir = localRootDir + "/logs";
        QDir logsQDir;
        if (!logsQDir.exists(logsDir)) {
            qDebug() << "创建logs目录:" << logsDir;
            logsQDir.mkpath(logsDir);
        }

        return termDiskPathDirect;
    }

    // 获取根目录下的子目录（通常是压缩包名称的目录）
    QDir rootDirObj(localRootDir);
    QStringList subDirs = rootDirObj.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

    // 首先检查直接子目录中是否有termdisk目录
    foreach (const QString &subDir, subDirs) {
        QString fullSubDirPath = localRootDir + "/" + subDir;
        qDebug() << "检查子目录:" << fullSubDirPath;

        // 检查该子目录下是否有termdisk目录
        QString termDiskPath = fullSubDirPath + "/termdisk";
        if (QDir(termDiskPath).exists()) {
            qDebug() << "在子目录中找到termdisk目录:" << termDiskPath;

            // 在termdisk同级目录下创建logs目录
            QString logsDir = fullSubDirPath + "/logs";
            QDir logsQDir;
            if (!logsQDir.exists(logsDir)) {
                qDebug() << "创建logs目录:" << logsDir;
                logsQDir.mkpath(logsDir);
            }

            return termDiskPath;
        }

        // 只在当前子目录内递归查找，不查询其他子目录
        QDirIterator it(fullSubDirPath, QDir::Dirs | QDir::NoDotAndDotDot, QDirIterator::Subdirectories);
        while (it.hasNext()) {
            QString dirPath = it.next();
            QFileInfo fileInfo(dirPath);
            qDebug() << "检查目录:" << dirPath;
            if (fileInfo.fileName().toLower() == "termdisk") {
                qDebug() << "在子目录中找到termdisk目录:" << dirPath;

                // 在termdisk同级目录下创建logs目录
                QString parentPath = fileInfo.absolutePath();
                QString logsDir = parentPath + "/logs";
                QDir logsQDir;
                if (!logsQDir.exists(logsDir)) {
                    qDebug() << "创建logs目录:" << logsDir;
                    logsQDir.mkpath(logsDir);
                }

                return dirPath;
            }
        }
    }

    // 如果找不到，则返回空字符串
    qDebug() << "未能找到termdisk目录";
    return QString();
}


