7-Zip Plugin for FAR Manager
----------------------------

FAR Manager is a file manager working in text mode. 
You can download "FAR Manager" from site:
http://www.farmanager.com

Files:

far7z.txt     - This file
far7z.reg     - Regisrty file for MultiArc Plugin
7zToFar.ini   - Supporting 7z for MultiArc Plugin
7-ZipFar.dll  - 7-Zip Plugin for FAR Manager
7-ZipEng.hlf  - Help file in English for FAR Manager
7-ZipRus.hlf  - Help file in Russian for FAR Manager
7-ZipEng.lng  - Plugin message strings in English for FAR Manager
7-ZipRus.lng  - Plugin message strings in Russian for FAR Manager

There are two ways to use 7-Zip with FAR Manager:

  1) Via 7-Zip FAR Plugin (it's recommended way).
  2) Via standard MultiArc Plugin.


7-Zip FAR Plugin
~~~~~~~~~~~~~~~~

7-Zip FAR Plugin is first level plugin for FAR Manager, like MultiArc plugin.
It very fast extracts and updates files in archive, since it doesn't use 
external programs. It supports all formats supported by 7-Zip: 
7z, ZIP, RAR, CAB, ARJ, GZIP, BZIP2, Z, TAR, CPIO, RPM and DEB.

To install 7-Zip FAR Plugin:
  1) Create "7-Zip" folder in ...\Program Files\Far\Plugins folder.
  2) Copy all files from "FAR" folder of this package to created folder.
  3) Install 7-Zip, or copy 7z.dll from 7-Zip to Program Files\Far\Plugins\7-Zip\
  4) Restart FAR.

Also you must enable "OEM plugin support" option in FAR Manager:

  1) F9 / Options / Plugins manager settings / check on "OEM plugin support".
  2) F9 / Options / Save setup.
  4) Restart FAR

You can open archives with one of the following ways:
  * Pressing Enter.
  * Pressing Ctrl-PgDown.
  * Pressing F11 and selecting 7-Zip item.
  
  
You can create new archives with 7-Zip by pressing F11 and 
selecting 7-Zip (add to archive) item.

If you think that some operations with archives is better to do with MultiArc Plugin,
you can disable 7-Zip plugin via Options / Pligin configuration / 7-Zip. In such mode 
opening archives by pressing Enter and Ctrl-PgDown will start MultiArc Plugin. And 
if you want to open archive with 7-Zip, press F11 and select 7-Zip item.


Using command line 7-Zip via MultiArc Plugin
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

If you want to use 7-Zip via MultiArc Plugin, you must
register file far7z.reg.

If you want to use 7z archives via MultiArc Plugin, you must
append contents of file Far\7zToFar.ini to file
..\Program Files\Far\Plugins\MultiArc\Formats\Custom.ini.


If you want to cancel using 7-Zip by MultiArc, just remove lines that contain
7-Zip (7z) program name from HKEY_LOCAL_MACHINE\SOFTWARE\Far\Plugins\MultiArc\ZIP
registry key.
