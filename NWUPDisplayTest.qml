import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12
import "ProtocolDisplayModules"

/**
 * @brief 南网上行协议显示测试界面
 * 用于测试NWUPDisplayModule的显示效果
 */
ApplicationWindow {
    id: testWindow
    title: "南网上行协议显示测试"
    width: 1200
    height: 800
    visible: true

    // 模拟树形模型
    ListModel {
        id: resultTreeModel
        
        function clear() {
            resultTreeModel.clear()
        }
        
        function appendRow(item) {
            resultTreeModel.append(item)
        }
    }

    // 南网上行协议显示模块
    NWUPDisplayModule {
        id: nwupDisplayModule
        
        // 设置依赖
        resultTreeModel: resultTreeModel
        addTreeItemWithVisibility: addTreeItemFunction
        messageInput: testMessageInput
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10

        // 标题
        Text {
            text: "南网上行协议显示测试"
            font.pixelSize: 18
            font.bold: true
        }

        // 测试报文输入区域
        GroupBox {
            title: "测试报文"
            Layout.fillWidth: true
            Layout.preferredHeight: 150

            ColumnLayout {
                anchors.fill: parent

                ComboBox {
                    id: testFrameSelector
                    Layout.fillWidth: true
                    model: [
                        "链路接口检测报文",
                        "读当前数据报文",
                        "读事件记录报文",
                        "写参数报文",
                        "确认/否定报文"
                    ]
                    
                    onCurrentTextChanged: {
                        switch(currentIndex) {
                            case 0: // 链路接口检测
                                testMessageInput.text = "681A1A68CB4402010000010240000F100000001020304050607080910111213141516A716"
                                break
                            case 1: // 读当前数据
                                testMessageInput.text = "680E0E68CB44020100000100C001010001000012345678CB16"
                                break
                            case 2: // 读事件记录
                                testMessageInput.text = "681C1C68CB44020100000100E001010EE01000024051510300024051511000001020304050607080B816"
                                break
                            case 3: // 写参数
                                testMessageInput.text = "68121268AB4402010000010400C00000F001000044020100000119F16"
                                break
                            case 4: // 确认/否定
                                testMessageInput.text = "680B0B68884402010000010000C00000E000000000A316"
                                break
                        }
                    }
                }

                ScrollView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    TextArea {
                        id: testMessageInput
                        text: "681A1A68CB4402010000010240000F100000001020304050607080910111213141516A716"
                        wrapMode: TextArea.Wrap
                        selectByMouse: true
                        font.family: "Consolas, Monaco, monospace"
                        placeholderText: "输入十六进制报文数据..."
                    }
                }

                Button {
                    text: "解析并显示"
                    Layout.alignment: Qt.AlignRight
                    onClicked: parseAndDisplay()
                }
            }
        }

        // 解析结果显示区域
        GroupBox {
            title: "解析结果树形显示"
            Layout.fillWidth: true
            Layout.fillHeight: true

            ScrollView {
                anchors.fill: parent
                
                TreeView {
                    id: resultTreeView
                    model: resultTreeModel
                    
                    delegate: Rectangle {
                        width: resultTreeView.width
                        height: 25
                        color: index % 2 === 0 ? "#f9f9f9" : "white"
                        
                        Row {
                            anchors.left: parent.left
                            anchors.leftMargin: model.level * 20 + 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 5
                            
                            // 展开/收缩图标
                            Text {
                                text: model.hasChildren ? (model.expanded ? "▼" : "▶") : "  "
                                color: "#666"
                                font.pixelSize: 10
                                anchors.verticalCenter: parent.verticalCenter
                                
                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        if (model.hasChildren) {
                                            model.expanded = !model.expanded
                                        }
                                    }
                                }
                            }
                            
                            // 字段名
                            Text {
                                text: model.name || ""
                                font.bold: model.level <= 1
                                color: model.level === 0 ? "#2c5aa0" : "#333"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            // 原始数据
                            Text {
                                text: model.rawData ? "[" + model.rawData + "]" : ""
                                color: "#d73a49"
                                font.family: "Consolas, Monaco, monospace"
                                font.pixelSize: 10
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            // 描述
                            Text {
                                text: model.description || ""
                                color: "#666"
                                font.pixelSize: 11
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                }
            }
        }

        // 状态栏
        Rectangle {
            Layout.fillWidth: true
            height: 30
            color: "#f0f0f0"
            border.color: "#ccc"
            
            Text {
                id: statusText
                anchors.left: parent.left
                anchors.leftMargin: 10
                anchors.verticalCenter: parent.verticalCenter
                text: "准备就绪"
                color: "#666"
            }
        }
    }

    // 添加树形项的函数
    function addTreeItemFunction(name, rawData, description, level, hasChildren, expanded, visible) {
        if (visible) {
            resultTreeModel.append({
                "name": name,
                "rawData": rawData,
                "description": description,
                "level": level,
                "hasChildren": hasChildren,
                "expanded": expanded,
                "visible": visible
            })
        }
    }

    // 获取原始报文的函数
    function getRawMessage() {
        return testMessageInput.text.replace(/\s/g, "").toUpperCase()
    }

    // 提取字节的函数
    function extractBytes(rawMessage, startPos, length) {
        if (!rawMessage || rawMessage.length < (startPos + length - 1) * 2) {
            return ""
        }
        
        var start = (startPos - 1) * 2
        var end = start + length * 2
        return rawMessage.substring(start, end)
    }

    // 解析并显示函数
    function parseAndDisplay() {
        statusText.text = "正在解析..."
        resultTreeModel.clear()
        
        var rawMessage = getRawMessage()
        if (rawMessage.length === 0) {
            statusText.text = "错误：请输入报文数据"
            return
        }
        
        if (rawMessage.length % 2 !== 0) {
            statusText.text = "错误：报文长度必须是偶数"
            return
        }

        // 模拟解析结果（实际应该调用C++解析器）
        var mockParsedData = createMockParsedData(rawMessage)
        var mockResult = {
            "isValid": true,
            "summary": "南网上行协议解析成功",
            "parsedData": mockParsedData
        }

        // 调用显示模块
        try {
            nwupDisplayModule.buildTree(mockResult, 1, "NW-UP")
            statusText.text = "解析完成，共 " + resultTreeModel.count + " 个节点"
        } catch (error) {
            statusText.text = "显示错误：" + error.toString()
        }
    }

    // 创建模拟解析数据
    function createMockParsedData(rawMessage) {
        var frameLength = rawMessage.length / 2
        
        return {
            "frameLength": frameLength,
            "rawFrame": rawMessage,
            "frameHeader": {
                "startFlag1": "68",
                "userDataLength": 26,
                "startFlag2": "68"
            },
            "controlField": {
                "value": "CB",
                "direction": "上行",
                "prm": 1,
                "functionCode": 11,
                "functionDescription": "请求2级数据"
            },
            "addressField": {
                "regionCode": "440201",
                "terminalAddress": "000001",
                "masterAddress": "01"
            },
            "applicationLayer": {
                "afn": 2,
                "afnDescription": "链路接口检测",
                "sequence": {
                    "tpv": 1,
                    "fir": 1,
                    "fin": 1,
                    "con": 0
                },
                "specificData": {
                    "type": "链路接口检测",
                    "contentData": {
                        "dataItemName": "终端登录数据",
                        "formattedValue": "登录信息数据"
                    },
                    "raw": "01020304050607080910111213141516"
                }
            },
            "checksum": {
                "value": "A7",
                "valid": true
            }
        }
    }

    Component.onCompleted: {
        // 设置全局函数
        nwupDisplayModule.getRawMessage = getRawMessage
        nwupDisplayModule.extractBytes = extractBytes
        
        // 自动解析第一个测试报文
        parseAndDisplay()
    }
}