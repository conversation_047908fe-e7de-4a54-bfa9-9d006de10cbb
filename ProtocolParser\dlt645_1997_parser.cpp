#include "dlt645_1997_parser.h"
#include <QDebug>

DLT645_1997_Parser::DLT645_1997_Parser(QObject *parent)
    : QObject(parent)
    , m_dataConfig(DataIdentifierConfig::instance())
{
    initializeMappings();
}

DLT645_1997_Parser::~DLT645_1997_Parser()
{
}

ProtocolParseResult DLT645_1997_Parser::parseFrame(const QByteArray &frameData)
{
    if (frameData.isEmpty()) {
        return createErrorResult("帧数据为空");
    }

    if (frameData.size() < 12) {
        return createErrorResult("帧数据长度不足");
    }

    QVariantMap parsedData;
    QString summary;
    QString detailInfo;

    try {
        // 解析帧结构
        parsedData["frameLength"] = frameData.size();
        parsedData["rawFrame"] = formatHexString(frameData);
        parsedData["protocol"] = "DL/T645-1997";

        // 1. 解析起始标志
        quint8 startFlag1 = static_cast<quint8>(frameData.at(0));
        parsedData["startFlag1"] = QString("0x%1").arg(startFlag1, 2, 16, QChar('0')).toUpper();

        // 2. 解析地址域（6字节）
        QByteArray addressData = frameData.mid(1, 6);
        QString address = parseAddress(addressData);
        parsedData["address"] = address;

        // 3. 解析第二个起始标志
        quint8 startFlag2 = static_cast<quint8>(frameData.at(7));
        parsedData["startFlag2"] = QString("0x%1").arg(startFlag2, 2, 16, QChar('0')).toUpper();

        // 4. 解析控制码
        quint8 controlCode = static_cast<quint8>(frameData.at(8));
        QVariantMap controlInfo = parseControlCode(controlCode);
        parsedData["controlCode"] = controlInfo;

        // 5. 解析数据长度
        quint8 dataLength = static_cast<quint8>(frameData.at(9));
        parsedData["dataLength"] = dataLength;

        // 6. 解析数据域（如果有数据）
        if (dataLength > 0 && frameData.size() >= 12 + dataLength) {
            QByteArray dataField = frameData.mid(10, dataLength);
            
            // DL/T645-1997数据域需要减33H处理
            for (int i = 0; i < dataField.size(); ++i) {
                dataField[i] = static_cast<char>(static_cast<quint8>(dataField[i]) - 0x33);
            }
            
            QVariantMap dataInfo = parseDataField(dataField, controlCode);
            parsedData["dataField"] = dataInfo;
        }

        // 7. 解析校验码
        quint8 checksum = static_cast<quint8>(frameData.at(frameData.size() - 2));
        parsedData["checksum"] = QString("0x%1").arg(checksum, 2, 16, QChar('0')).toUpper();

        // 8. 解析结束标志
        quint8 endFlag = static_cast<quint8>(frameData.at(frameData.size() - 1));
        parsedData["endFlag"] = QString("0x%1").arg(endFlag, 2, 16, QChar('0')).toUpper();

        // 生成摘要信息
        summary = QString("DL/T645-1997 地址:%1 功能:%2")
                  .arg(address)
                  .arg(controlInfo["functionDescription"].toString());

        // 生成详细信息
        detailInfo = QString("帧长度: %1字节\n地址域: %2\n控制码: %3\n数据长度: %4字节")
                     .arg(frameData.size())
                     .arg(address)
                     .arg(controlInfo["description"].toString())
                     .arg(dataLength);

        return createSuccessResult(parsedData, summary, detailInfo);

    } catch (const std::exception &e) {
        return createErrorResult(QString("解析异常: %1").arg(e.what()));
    } catch (...) {
        return createErrorResult("未知解析异常");
    }
}

QString DLT645_1997_Parser::parseAddress(const QByteArray &addressData)
{
    if (addressData.size() != 6) {
        return "无效地址";
    }

    // DL/T645-1997地址域格式：低字节在前
    QString address;
    for (int i = 5; i >= 0; --i) {
        address += QString("%1").arg(static_cast<quint8>(addressData.at(i)), 2, 16, QChar('0')).toUpper();
    }
    
    return address;
}

QVariantMap DLT645_1997_Parser::parseControlCode(quint8 controlCode)
{
    QVariantMap result;
    
    result["raw"] = QString("0x%1").arg(controlCode, 2, 16, QChar('0')).toUpper();
    result["direction"] = (controlCode & 0x80) ? "从站应答" : "主站命令";
    result["response"] = (controlCode & 0x40) ? "异常应答" : "正确应答";
    result["hasMore"] = (controlCode & 0x20) ? "有后续帧" : "无后续帧";
    
    quint8 functionCode = controlCode & 0x1F;
    result["functionCode"] = functionCode;
    
    // DL/T645-1997功能码定义
    QString functionDesc;
    switch (functionCode) {
    case 0x01: functionDesc = "读数据"; break;
    case 0x02: functionDesc = "读后续数据"; break;
    case 0x04: functionDesc = "写数据"; break;
    case 0x08: functionDesc = "广播校时"; break;
    case 0x10: functionDesc = "冻结命令"; break;
    case 0x11: functionDesc = "更改通信速率"; break;
    case 0x12: functionDesc = "修改密码"; break;
    case 0x13: functionDesc = "最大需量清零"; break;
    default: functionDesc = QString("未知功能码(0x%1)").arg(functionCode, 2, 16, QChar('0')); break;
    }
    
    result["functionDescription"] = functionDesc;
    result["description"] = QString("%1 %2 %3")
                           .arg(result["direction"].toString())
                           .arg(functionDesc)
                           .arg(result["response"].toString());
    
    return result;
}

QVariantMap DLT645_1997_Parser::parseDataField(const QByteArray &dataField, quint8 controlCode)
{
    QVariantMap result;
    
    if (dataField.isEmpty()) {
        result["type"] = "空数据域";
        return result;
    }
    
    result["rawData"] = formatHexString(dataField);
    result["length"] = dataField.size();
    
    quint8 functionCode = controlCode & 0x1F;
    bool isResponse = (controlCode & 0x80) != 0;
    
    // 根据功能码解析数据域
    switch (functionCode) {
    case 0x01: // 读数据
        if (isResponse && dataField.size() >= 2) {
            // 从站应答读数据：包含2字节数据标识 + 数据内容
            QByteArray dataId = dataField.left(2);
            QVariantMap idInfo = parseDataIdentifier(dataId);
            result["dataIdentifier"] = idInfo;
            result["type"] = "读数据应答";
            
            if (dataField.size() > 2) {
                QByteArray dataContent = dataField.mid(2);
                result["dataContent"] = formatHexString(dataContent);
                
                // 使用配置文件解析数据（DL/T645-1997协议）
                if (m_dataConfig) {
                    // 将2字节数据标识转换为4字节格式以兼容配置系统
                    quint32 identifier = static_cast<quint8>(dataId.at(0)) |
                                        (static_cast<quint8>(dataId.at(1)) << 8);
                    
                    DataItemConfig config = m_dataConfig->getDataItemConfig(identifier, ProtocolType::DLT645_1997);
                    if (!config.id.isEmpty()) {
                        result["itemName"] = config.name;
                        result["itemDescription"] = config.description;
                        result["unit"] = config.unit;
                        result["encoding"] = config.encoding;
                        result["format"] = config.format;
                    }
                }
            }
        } else if (!isResponse && dataField.size() >= 2) {
            // 主站请求读数据：包含2字节数据标识
            QByteArray dataId = dataField.left(2);
            QVariantMap idInfo = parseDataIdentifier(dataId);
            result["dataIdentifier"] = idInfo;
            result["type"] = "读数据请求";
        }
        break;
        
    default:
        result["type"] = QString("功能码0x%1数据域").arg(functionCode, 2, 16, QChar('0'));
        break;
    }
    
    return result;
}

QVariantMap DLT645_1997_Parser::parseDataIdentifier(const QByteArray &dataId)
{
    QVariantMap result;
    
    if (dataId.size() != 2) {
        result["error"] = "数据标识长度错误";
        return result;
    }
    
    quint16 identifier = static_cast<quint8>(dataId.at(0)) |
                        (static_cast<quint8>(dataId.at(1)) << 8);
    
    result["raw"] = formatHexString(dataId);
    result["value"] = QString("0x%1").arg(identifier, 4, 16, QChar('0')).toUpper();
    
    // 使用配置文件获取数据项信息
    if (m_dataConfig) {
        QString name = m_dataConfig->getDataItemName(identifier, ProtocolType::DLT645_1997);
        QString description = m_dataConfig->getDataItemDescription(identifier, ProtocolType::DLT645_1997);
        
        result["name"] = name;
        result["description"] = description;
    }
    
    return result;
}

void DLT645_1997_Parser::initializeMappings()
{
    // 初始化DL/T645-1997特有的映射表
    qDebug() << "DLT645_1997_Parser初始化完成";
}

ProtocolParseResult DLT645_1997_Parser::createSuccessResult(const QVariantMap &data, const QString &summary, const QString &detail)
{
    ProtocolParseResult result;
    result.isValid = true;
    result.protocolType = ProtocolType::DLT645_1997;
    result.parsedData = data;
    result.summary = summary;
    result.detailInfo = detail;
    return result;
}

ProtocolParseResult DLT645_1997_Parser::createErrorResult(const QString &error)
{
    ProtocolParseResult result;
    result.isValid = false;
    result.errorMessage = error;
    return result;
}

QString DLT645_1997_Parser::formatHexString(const QByteArray &data, const QString &separator)
{
    QString result;
    for (int i = 0; i < data.size(); ++i) {
        if (i > 0) result += separator;
        result += QString("%1").arg(static_cast<quint8>(data.at(i)), 2, 16, QChar('0')).toUpper();
    }
    return result;
}
