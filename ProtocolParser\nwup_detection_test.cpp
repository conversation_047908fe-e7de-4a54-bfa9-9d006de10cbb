#include <QCoreApplication>
#include <QDebug>
#include <QByteArray>
#include <QString>
#include "protocoldetector.h"
#include "nwupparser.h"
#include "protocoltypes.h"

// 测试南网上�?�协�?检测和解析功能
int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);
    
    qDebug() << "南网上�?�协�?检测和解析测试程序";
    
    // 测试报文 (链路接口检测报�?)
    QString testFrameHex = "68 1A 1A 68 CB 44 02 01 00 00 01 02 C0 00 00 F1 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 21 16";
    
    // 移除空格
    testFrameHex.remove(' ');
    
    // �?�?为字节数�?
    QByteArray testFrame = QByteArray::fromHex(testFrameHex.toLatin1());
    
    qDebug() << "测试报文:" << testFrameHex;
    qDebug() << "字节长度:" << testFrame.size();
    
    // 创建协�??检测器实例
    ProtocolDetector detector;
    
    // 检测协�?类型
    qDebug() << "=== 协�??检�? ===";
    FrameParseResult frameResult = detector.detectProtocol(testFrame);
    
    qDebug() << "检测结�?:" << (frameResult.isValid ? "有效" : "无效");
    qDebug() << "协�??类型:" << ProtocolTypeUtils::protocolTypeToString(frameResult.protocolType);
    
    if (!frameResult.isValid) {
        qDebug() << "错�??信息:" << frameResult.errorMessage;
        
        // 尝试单独检测南网上行协�?
        qDebug() << "\n=== 单独检测南网上行协�? ===";
        FrameParseResult nwupResult = detector.detectNWUP(testFrame);
        
        qDebug() << "检测结�?:" << (nwupResult.isValid ? "有效" : "无效");
        if (!nwupResult.isValid) {
            qDebug() << "错�??信息:" << nwupResult.errorMessage;
        }
        
        // 测试帧查�?
        int frameStart = -1, frameEnd = -1;
        bool frameFound = detector.findNWUPFrame(testFrame, frameStart, frameEnd);
        
        qDebug() << "帧查找结�?:" << (frameFound ? "找到" : "�?找到");
        if (frameFound) {
            qDebug() << "帧起始位�?:" << frameStart;
            qDebug() << "帧结束位�?:" << frameEnd;
            qDebug() << "帧长�?:" << (frameEnd - frameStart + 1);
            
            // 提取帧数�?并验证校验和
            QByteArray frameData = testFrame.mid(frameStart, frameEnd - frameStart + 1);
            bool checksumValid = detector.verifyNWUPChecksum(frameData);
            
            qDebug() << "校验和验证结�?:" << (checksumValid ? "正确" : "错�??");
            
            // 手动计算校验�?
            QByteArray dataToCheck = frameData.mid(4, frameData.size() - 6);
            quint8 calculatedCS = 0;
            for (int i = 0; i < dataToCheck.size(); ++i) {
                calculatedCS += static_cast<quint8>(dataToCheck.at(i));
            }
            
            quint8 frameCS = static_cast<quint8>(frameData.at(frameData.size() - 2));
            
            qDebug() << "计算的校验和:" << QString("0x%1").arg(calculatedCS, 2, 16, QChar('0')).toUpper();
            qDebug() << "帧中的校验和:" << QString("0x%1").arg(frameCS, 2, 16, QChar('0')).toUpper();
        }
    } else {
        // 如果检测成功，尝试解析
        qDebug() << "\n=== 协�??解析 ===";
        NWUPParser parser;
        ProtocolParseResult parseResult = parser.parseFrame(frameResult.frameData);
        
        qDebug() << "解析结果:" << (parseResult.isValid ? "成功" : "失败");
        if (!parseResult.isValid) {
            qDebug() << "错�??信息:" << parseResult.errorMessage;
        } else {
            qDebug() << "摘�??:" << parseResult.summary;
            qDebug() << "详细信息:" << parseResult.detailInfo;
        }
    }
    
    return 0;
}