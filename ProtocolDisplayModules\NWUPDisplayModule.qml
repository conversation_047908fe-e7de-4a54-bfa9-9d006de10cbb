import QtQuick 2.12

/**
 * @brief 南网上行协议显示模块
 * 专门处理南网上行协议(NW-UP)的树形显示
 * 基于GB/T 18657.1的FT1.2异步式传输帧格式
 */
BaseDisplayModule {
    id: nwupModule
    
    /**
     * @brief 构建南网上行协议特定的树形结构
     * @param parsedData 解析后的数据
     * @param level 层级
     */
    function buildProtocolSpecificTree(parsedData, level) {
        if (!parsedData) {
            addTreeItemWithVisibility("解析错误", "", "没有可显示的协议数据", level, false, false, true)
            return
        }
        
        console.log("NWUPDisplayModule: buildProtocolSpecificTree - parsedData:", JSON.stringify(parsedData))
        
        // 获取原始报文用于提取原始字节数据
        var rawMessage = getRawMessage()
        
        // 构建南网上行协议帧结构
        buildNWUPFrameTree(parsedData, level, rawMessage)
    }
    
    /**
     * @brief 构建南网上行协议帧结构树
     * 基于GB/T 18657.1的FT1.2异步式传输帧格式
     */
    function buildNWUPFrameTree(parsedData, level, rawMessage) {
        // 2级：起始字符 (68H) - 显示原始报文第1字节
        var startFrameRaw = extractBytes(rawMessage, 1, 1)
        addTreeItemWithVisibility("起始字符", startFrameRaw, "帧起始标识符 (68H)", level, false, false, true)

        // 2级：长度L - 显示原始报文第2-3字节
        var lengthRaw = extractBytes(rawMessage, 2, 2)
        var lengthValue = parsedData.frameHeader ? parsedData.frameHeader.userDataLength : 0
        var lengthDesc = "用户数据长度：" + lengthValue + "字节"
        addTreeItemWithVisibility("长度L", lengthRaw, lengthDesc, level, false, false, true)

        // 2级：长度L (重复) - 显示原始报文第4-5字节
        var lengthRepeatRaw = extractBytes(rawMessage, 4, 2)
        addTreeItemWithVisibility("长度L", lengthRepeatRaw, "重复的用户数据长度", level, false, false, true)

        // 2级：起始字符 (68H 重复) - 显示原始报文第6字节
        var startFrame2Raw = extractBytes(rawMessage, 6, 1)
        addTreeItemWithVisibility("起始字符", startFrame2Raw, "第二个帧起始标识符 (68H)", level, false, false, true)

        // 2级：控制域C (有子项) - 显示原始报文第7字节
        var controlRaw = extractBytes(rawMessage, 7, 1)
        if (parsedData.controlField) {
            var controlDesc = buildControlFieldDescription(parsedData.controlField)
            addTreeItemWithVisibility("控制域C", controlRaw, controlDesc, level, true, true, true)
            buildControlFieldTree(parsedData.controlField, level + 1)
        } else {
            addTreeItemWithVisibility("控制域C", controlRaw, "控制域信息", level, false, false, true)
        }

        // 2级：地址域A (有子项) - 显示原始报文第8-14字节
        var addressRaw = extractBytes(rawMessage, 8, 7)
        if (parsedData.addressField) {
            var addressDesc = "地址域信息"
            addTreeItemWithVisibility("地址域A", addressRaw, addressDesc, level, true, true, true)
            buildAddressFieldTree(parsedData.addressField, level + 1, rawMessage)
        } else {
            addTreeItemWithVisibility("地址域A", addressRaw, "地址域信息", level, false, false, true)
        }

        // 2级：应用层数据 (有子项)
        if (parsedData.applicationLayer) {
            addTreeItemWithVisibility("应用层数据", "", "链路用户数据", level, true, true, true)
            buildApplicationLayerTree(parsedData.applicationLayer, level + 1, rawMessage)
        }

        // 2级：帧校验和CS - 显示倒数第3字节
        var checksumRaw = rawMessage.length >= 6 ? extractBytes(rawMessage, rawMessage.length/2 - 2, 1) : ""
        var checksumValue = parsedData.checksum ? parsedData.checksum.value : ""
        var checksumDesc = "校验和：" + checksumValue
        addTreeItemWithVisibility("帧校验和CS", checksumRaw, checksumDesc, level, false, false, true)

        // 2级：结束字符 (16H) - 显示最后1字节
        var endRaw = rawMessage.length >= 2 ? extractBytes(rawMessage, rawMessage.length/2, 1) : ""
        addTreeItemWithVisibility("结束字符", endRaw, "帧结束标识符 (16H)", level, false, false, true)
    }
    
    /**
     * @brief 构建控制域描述
     */
    function buildControlFieldDescription(controlField) {
        var desc = ""
        if (controlField.direction) {
            desc += controlField.direction + " "
        }
        if (controlField.functionCode !== undefined) {
            desc += "功能码:" + controlField.functionCode
        }
        if (controlField.functionDescription) {
            desc += " (" + controlField.functionDescription + ")"
        }
        return desc
    }

    /**
     * @brief 构建控制域子树
     */
    function buildControlFieldTree(controlField, level) {
        // 3级：传输方向位DIR
        if (controlField.direction !== undefined) {
            addTreeItemWithVisibility("传输方向位DIR", "", 
                controlField.direction === "上行" ? "DIR=1 (终端→主站)" : "DIR=0 (主站→终端)", 
                level, false, false, true)
        }

        // 3级：启动标志位PRM
        if (controlField.prm !== undefined) {
            addTreeItemWithVisibility("启动标志位PRM", "", 
                "PRM=" + controlField.prm + " (" + (controlField.prm ? "启动站" : "从动站") + ")", 
                level, false, false, true)
        }

        // 3级：功能码
        if (controlField.functionCode !== undefined) {
            var funcDesc = "功能码=" + controlField.functionCode
            if (controlField.functionDescription) {
                funcDesc += " (" + controlField.functionDescription + ")"
            }
            addTreeItemWithVisibility("功能码", "", funcDesc, level, false, false, true)
        }
    }

    /**
     * @brief 构建地址域子树
     */
    function buildAddressFieldTree(addressField, level, rawMessage) {
        // 3级：省地市区县码A1 (3字节BCD)
        if (addressField.regionCode !== undefined) {
            var regionRaw = extractBytes(rawMessage, 8, 3)
            addTreeItemWithVisibility("省地市区县码A1", regionRaw, 
                "区域码：" + addressField.regionCode, level, false, false, true)
        }

        // 3级：终端地址A2 (3字节BIN)
        if (addressField.terminalAddress !== undefined) {
            var terminalRaw = extractBytes(rawMessage, 11, 3)
            addTreeItemWithVisibility("终端地址A2", terminalRaw, 
                "终端地址：" + addressField.terminalAddress, level, false, false, true)
        }

        // 3级：主站地址A3 (1字节BIN)
        if (addressField.masterAddress !== undefined) {
            var masterRaw = extractBytes(rawMessage, 14, 1)
            addTreeItemWithVisibility("主站地址A3", masterRaw, 
                "主站地址：" + addressField.masterAddress, level, false, false, true)
        }
    }

    /**
     * @brief 构建应用层子树
     */
    function buildApplicationLayerTree(appLayer, level, rawMessage) {
        // 3级：应用层功能码AFN
        if (appLayer.afn !== undefined) {
            var afnDesc = "AFN=" + appLayer.afn.toString(16).toUpperCase() + "H"
            if (appLayer.afnDescription) {
                afnDesc += " (" + appLayer.afnDescription + ")"
            }
            addTreeItemWithVisibility("应用层功能码AFN", "", afnDesc, level, false, false, true)
        }

        // 3级：帧序列域SEQ
        if (appLayer.sequence) {
            var seqDesc = buildSequenceDescription(appLayer.sequence)
            addTreeItemWithVisibility("帧序列域SEQ", "", seqDesc, level, true, true, true)
            buildSequenceTree(appLayer.sequence, level + 1)
        }

        // 3级：信息体 (根据AFN类型显示不同内容)
        if (appLayer.dataItems && appLayer.dataItems.length > 0) {
            addTreeItemWithVisibility("信息体", "", "包含" + appLayer.dataItems.length + "个数据项", level, true, true, true)
            buildDataItemsTree(appLayer.dataItems, level + 1, rawMessage)
        } else if (appLayer.specificData) {
            // 显示特定AFN的解析结果
            addTreeItemWithVisibility("功能数据", "", appLayer.specificData.type || "功能数据", level, true, true, true)
            buildSpecificDataTree(appLayer.specificData, level + 1, rawMessage)
        }

        // 3级：消息认证码PW (如果存在)
        if (appLayer.messageAuthCode) {
            addTreeItemWithVisibility("消息认证码PW", "", "16字节认证码", level, false, false, true)
        }

        // 3级：时间标签Tp (如果存在)
        if (appLayer.timeTag) {
            var timeDesc = "时间标签：" + appLayer.timeTag
            addTreeItemWithVisibility("时间标签Tp", "", timeDesc, level, false, false, true)
        }
    }

    /**
     * @brief 构建帧序列域描述
     */
    function buildSequenceDescription(sequence) {
        var desc = ""
        if (sequence.tpv !== undefined) {
            desc += "TpV=" + sequence.tpv + " "
        }
        if (sequence.fir !== undefined) {
            desc += "FIR=" + sequence.fir + " "
        }
        if (sequence.fin !== undefined) {
            desc += "FIN=" + sequence.fin + " "
        }
        if (sequence.con !== undefined) {
            desc += "CON=" + sequence.con
        }
        return desc
    }

    /**
     * @brief 构建帧序列域子树
     */
    function buildSequenceTree(sequence, level) {
        // 4级：时间标签有效位TpV
        if (sequence.tpv !== undefined) {
            addTreeItemWithVisibility("时间标签有效位TpV", "", 
                "TpV=" + sequence.tpv + " (" + (sequence.tpv ? "有时间标签" : "无时间标签") + ")", 
                level, false, false, true)
        }

        // 4级：首帧标志FIR
        if (sequence.fir !== undefined) {
            addTreeItemWithVisibility("首帧标志FIR", "", 
                "FIR=" + sequence.fir + " (" + (sequence.fir ? "第一帧" : "非第一帧") + ")", 
                level, false, false, true)
        }

        // 4级：末帧标志FIN
        if (sequence.fin !== undefined) {
            addTreeItemWithVisibility("末帧标志FIN", "", 
                "FIN=" + sequence.fin + " (" + (sequence.fin ? "最后帧" : "非最后帧") + ")", 
                level, false, false, true)
        }

        // 4级：请求确认标志CON
        if (sequence.con !== undefined) {
            addTreeItemWithVisibility("请求确认标志CON", "", 
                "CON=" + sequence.con + " (" + (sequence.con ? "需要确认" : "不需要确认") + ")", 
                level, false, false, true)
        }
    }

    /**
     * @brief 构建数据项子树
     */
    function buildDataItemsTree(dataItems, level, rawMessage) {
        for (var i = 0; i < dataItems.length; i++) {
            var item = dataItems[i]
            var itemName = "数据项" + (i + 1)
            
            addTreeItemWithVisibility(itemName, "", "", level, true, true, true)
            
            // 5级：信息点标识DA
            if (item.dataAddress) {
                var daDesc = "DA=" + item.dataAddress.value + " (" + item.dataAddress.description + ")"
                addTreeItemWithVisibility("信息点标识DA", "", daDesc, level + 1, false, false, true)
            }
            
            // 5级：数据标识编码DI
            if (item.dataIdentifier) {
                var diDesc = "DI=" + item.dataIdentifier.value + " (" + item.dataIdentifier.description + ")"
                addTreeItemWithVisibility("数据标识编码DI", "", diDesc, level + 1, false, false, true)
            }
            
            // 5级：数据内容
            if (item.dataContent) {
                var contentDesc = ""
                if (item.dataContent.formattedValue) {
                    contentDesc = item.dataContent.formattedValue
                    if (item.dataContent.unit) {
                        contentDesc += " " + item.dataContent.unit
                    }
                }
                addTreeItemWithVisibility("数据内容", "", contentDesc, level + 1, false, false, true)
            }
        }
    }

    /**
     * @brief 构建特定功能数据子树
     */
    function buildSpecificDataTree(specificData, level, rawMessage) {
        if (specificData.type) {
            addTreeItemWithVisibility("数据类型", "", specificData.type, level, false, false, true)
        }
        
        // 根据不同的AFN类型显示不同的内容
        if (specificData.contentData) {
            var contentData = specificData.contentData
            if (contentData.dataItemName) {
                addTreeItemWithVisibility("数据项名称", "", contentData.dataItemName, level, false, false, true)
            }
            if (contentData.formattedValue) {
                addTreeItemWithVisibility("解析值", "", contentData.formattedValue, level, false, false, true)
            }
        }
        
        // 显示原始数据
        if (specificData.raw) {
            addTreeItemWithVisibility("原始数据", specificData.raw, "十六进制原始数据", level, false, false, true)
        }
    }
}
