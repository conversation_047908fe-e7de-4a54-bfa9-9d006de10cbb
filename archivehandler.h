#ifndef ARCHIVEHANDLER_H
#define ARCHIVEHANDLER_H

#include <QObject>
#include <QTemporaryDir>
#include <QString>
#include <QVariantMap>
#include <QStringList>
#include <QQmlApplicationEngine>

// 声明全局变量
extern QQmlApplicationEngine* g_enginePtr;

class ArchiveHandler : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString termDiskPath READ termDiskPath NOTIFY termDiskPathChanged)
    Q_PROPERTY(bool lastExtractionReused READ lastExtractionReused WRITE setLastExtractionReused NOTIFY lastExtractionReusedChanged)

public:
    explicit ArchiveHandler(QObject *parent = nullptr);
    ~ArchiveHandler();
    
    // 属性访问器
    bool lastExtractionReused() const;
    void setLastExtractionReused(bool reused);
    QString termDiskPath() const;
    
    // Q_INVOKABLE 方法
    Q_INVOKABLE bool openExistingDirectory(const QString &directoryPath);
    Q_INVOKABLE bool extractArchive(const QString &archivePath);
    Q_INVOKABLE bool hasTermDiskPath() const;
    Q_INVOKABLE QWidget* createLogAnalyzerWidget();
    Q_INVOKABLE QWidget* createParamViewerWidget();
    Q_INVOKABLE QWidget* createDatabaseWidget();
    Q_INVOKABLE QWidget* createProtocolParserWidget();
    Q_INVOKABLE void readLogFile(const QString& filePath);
    Q_INVOKABLE QVariantMap getFileInfo(const QString& filePath);
    Q_INVOKABLE bool checkLogsExist(const QString& dirPath);
    Q_INVOKABLE QStringList listLogFiles(const QString& dirPath);
    Q_INVOKABLE void processLogsWithBat();
    Q_INVOKABLE bool openFileWithApp(const QString &appPath, const QString &filePath);
    Q_INVOKABLE bool openFileDirectory(const QString &filePath);
    Q_INVOKABLE bool saveBatFile(const QString &filePath, const QString &content);
    Q_INVOKABLE bool executeBatFile(const QString &filePath);
    Q_INVOKABLE bool directoryExists(const QString &dirPath);
    Q_INVOKABLE bool ensureDirectoryExists(const QString &dirPath);
    Q_INVOKABLE bool copyFiles(const QString &sourceDir, const QString &destDir, const QString &filter);
    Q_INVOKABLE void manualMergeLogFiles(const QString &backupDir, bool isCompressed, 
                                        const QString &suffix, bool includeLatest,
                                        const QString &latestLocation, const QString &logType);
    Q_INVOKABLE QStringList listCustomScripts();
    Q_INVOKABLE bool executeCustomScript(const QString &scriptPath);
    Q_INVOKABLE bool removeDirectory(const QString& dirPath);
    Q_INVOKABLE QStringList listTmpDirectories();
    
signals:
    void termDiskPathChanged();
    void extractionCompleted(const QString &termDiskPath);
    void logFileReadCompleted(const QString &filePath, const QString &content);
    void logProcessingCompleted(const QString &logsDir);
    void lastExtractionReusedChanged(bool reused);
    void batchProcessStarted(const QString &message);
    void batchProcessFinished(bool success, const QString &message);
    void extractionFailed(const QString &errorMessage);
    
private:
    bool extractZip(const QString &zipFile, const QString &extractDir);
    bool extractRar(const QString &rarFile, const QString &extractDir);
    QString findTermDiskDir(const QString &rootDir, const QString &extractedDirName = QString());
    
private:
    QTemporaryDir *m_tempDir;
    QString m_termDiskPath;
    bool m_lastExtractionReused;
};

// 静态函数声明
void handleLastExtractionReusedChanged(bool reused);

#endif // ARCHIVEHANDLER_H
